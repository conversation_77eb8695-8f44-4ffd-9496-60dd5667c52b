# DimOperationUtil 配置示例
dcube:
  dim-operation:
    # 是否启用跟踪功能（生产环境建议设为 false）
    trace-enabled: false
    
    # 跟踪级别：simple（简单）、detailed（详细）
    trace-level: simple
    
    # 是否在跟踪日志中包含时间戳
    include-timestamp: true
    
    # 是否在跟踪日志中包含调用栈信息
    include-stack-trace: false
    
    # 性能监控阈值（毫秒）
    # 执行时间超过此值的表达式会被记录
    performance-threshold-ms: 100
    
    # 是否启用性能监控
    performance-monitor-enabled: true

---
# 开发环境配置
spring:
  profiles: dev
  
dcube:
  dim-operation:
    trace-enabled: true
    trace-level: detailed
    include-timestamp: true
    include-stack-trace: true
    performance-threshold-ms: 50
    performance-monitor-enabled: true

---
# 测试环境配置
spring:
  profiles: test
  
dcube:
  dim-operation:
    trace-enabled: true
    trace-level: simple
    include-timestamp: true
    include-stack-trace: false
    performance-threshold-ms: 100
    performance-monitor-enabled: true

---
# 生产环境配置
spring:
  profiles: prod
  
dcube:
  dim-operation:
    trace-enabled: false
    trace-level: simple
    include-timestamp: false
    include-stack-trace: false
    performance-threshold-ms: 200
    performance-monitor-enabled: true
