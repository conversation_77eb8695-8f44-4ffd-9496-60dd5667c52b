# DimOperationUtil 配置示例
dcube:
  dim-operation:
    # 是否启用跟踪功能（生产环境建议设为 false）
    trace-enabled: false

    # 跟踪级别：simple（简单）、detailed（详细）、slf4j-debug、slf4j-info、slf4j-warn、slf4j-error
    trace-level: simple

    # 是否使用 SLF4J 日志输出（推荐）
    use-slf4j: true

    # SLF4J 日志级别：TRACE、DEBUG、INFO、WARN、ERROR
    slf4j-level: DEBUG

    # 是否在跟踪日志中包含时间戳
    include-timestamp: true

    # 是否在跟踪日志中包含调用栈信息
    include-stack-trace: false

    # 性能监控阈值（毫秒）
    # 执行时间超过此值的表达式会被记录
    performance-threshold-ms: 100

    # 是否启用性能监控
    performance-monitor-enabled: true

---
# 开发环境配置
spring:
  profiles: dev

dcube:
  dim-operation:
    trace-enabled: true
    trace-level: slf4j-debug
    use-slf4j: true
    slf4j-level: DEBUG
    include-timestamp: true
    include-stack-trace: true
    performance-threshold-ms: 50
    performance-monitor-enabled: true

---
# 测试环境配置
spring:
  profiles: test

dcube:
  dim-operation:
    trace-enabled: true
    trace-level: slf4j-info
    use-slf4j: true
    slf4j-level: INFO
    include-timestamp: true
    include-stack-trace: false
    performance-threshold-ms: 100
    performance-monitor-enabled: true

---
# 生产环境配置
spring:
  profiles: prod

dcube:
  dim-operation:
    trace-enabled: false
    trace-level: slf4j-warn
    use-slf4j: true
    slf4j-level: WARN
    include-timestamp: false
    include-stack-trace: false
    performance-threshold-ms: 200
    performance-monitor-enabled: true
