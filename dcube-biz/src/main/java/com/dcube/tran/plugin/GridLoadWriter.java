package com.dcube.tran.plugin;

import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class GridLoadWriter extends Writer {

    private static final int BATCH_SIZE = 1024 * 5;

    private TableMetaData tableMetaData;

    private ReportDto reportDto;

    private String tableName;

    List<String> columnNames;

    private Map<String, String> backupConfigMap;

    private GridLoadWriter() {

    }

    public GridLoadWriter(String tableName, TableMetaData tableMetaData, ReportDto reportDto, List<String> columnNames, Map<String, String> backupConfigMap) {
        super();
        this.tableName = tableName;
        this.tableMetaData = tableMetaData;
        this.reportDto = reportDto;
        this.columnNames = columnNames;
        this.backupConfigMap = backupConfigMap;
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<Record> writeBuffer = new ArrayList<>(BATCH_SIZE);
        Object[][] rowData = new Object[this.reportDto.getTotalRecord().intValue()][];
        MemorySchema.getDatabase().createTable(tableMetaData);
        int curIndex = 0;
        try {
            Record record;
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record != null) {
                    if (record instanceof TerminateRecord) {
                        break;
                    }
                    Object[] row = new Object[tableMetaData.getColumnTypes().size()];
                    for (int j = 0; j < tableMetaData.getColumnTypes().size(); j++) {
                        String columnName = StringUtils.EMPTY;
                        if (j < columnNames.size()) {
                            columnName = columnNames.get(j);
                        }
                        String columnCode = MapUtils.getString(backupConfigMap, columnName, columnName);
                        Column col = record.getColumn(j);
                        if (StringUtils.equalsIgnoreCase(columnCode, col.getColumnName())) {
                            row[j] = getValue(tableMetaData.getColumnTypes().get(j), col);
                        } else {
                            row[j] = null;
                        }
                    }
                    rowData[curIndex] = row;
                    curIndex++;

                    writeBuffer.add(record);
                    if (writeBuffer.size() >= BATCH_SIZE) {
                        // 记录报告进度
                        ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                        writeBuffer.clear();
                    }
                }
            }
            if (getTaskHelp().checkStatus() && !writeBuffer.isEmpty()) {
                // 记录报告进度
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            if (!writeBuffer.isEmpty()) {
                log.error("❕有数据未写入，数据条数：{}，任务状态：{}", writeBuffer.size(), getTaskHelp().checkStatus());
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            getTaskHelp().setWriterStatus(true);
            MemorySchema.getDatabase().reData(tableName, rowData);
        } catch (Exception e) {
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(e.getLocalizedMessage());
            log.error("{} PreWriter is error.", CommonErrorCode.RUNTIME_ERROR, e);
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            writeBuffer.clear();
            TaskReport.finished(this.reportDto);
        }
    }

    private Object getValue(Class clazz, Column columnValue) {
        boolean isNull = columnValue.asString() == null;
        if (Integer.class.equals(clazz)) {
            return isNull ? null : columnValue.asLong().intValue();
        } else if (Double.class.equals(clazz)) {
            return isNull ? null : columnValue.asDouble();
        } else if (String.class.equals(clazz)) {
            return isNull ? null : columnValue.asString();
        } else if (Date.class.equals(clazz)) {
            return isNull ? null : new java.sql.Timestamp(columnValue.asDate().getTime());
        }
        throw new IllegalArgumentException("Unsupported type: " + clazz.getName());
    }
}