package com.dcube.tran.plugin;

import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.dto.ReportDto;
import com.dcube.tran.element.*;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordSender;
import com.dcube.tran.store.repository.AbstractRepository;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;

@Slf4j
public class RdbmsReader extends Reader {

    protected static final int SOCKET_TIMEOUT_INSECOND = 2 * 24 * 60 * 60;

    protected AbstractRepository repository;

    protected String querySql;

    protected ReportDto reportDto;

    protected final long waitSeconds = 10;

    protected RdbmsReader() {
    }

    public RdbmsReader(AbstractRepository repository, String querySql, ReportDto reportDto) {
        this.repository = repository;
        this.querySql = querySql;
        this.reportDto = reportDto;
    }

    @Override
    public void startRead(RecordSender recordSender) {
        log.info("============================================={}==============================================", "执行startRead");

        // MYSQL DEFAULT MIN_VALUE
        if (repository.getDbType().equals(JdbcUtils.DB_TYPE_MYSQL)) {
            this.setFetchSize(Integer.MIN_VALUE);
        }

        Connection conn = null;
        int columnNumber;
        ResultSet rs = null;
        try {
            conn = JdbcUtils.getConnection(repository.getConfig());
            long start = System.currentTimeMillis();
            rs = this.query(conn, this.querySql, this.getFetchSize());
            log.info("sql {} spent {} ms", querySql, System.currentTimeMillis() - start);

            ResultSetMetaData metaData = rs.getMetaData();
            columnNumber = metaData.getColumnCount();

            while (getTaskHelp().checkStatus() && rs.next()) {
                this.transportOneRecord(recordSender, rs, metaData, columnNumber);
            }
            getTaskHelp().setReaderStatus(true);
        } catch (Exception e) {
            getTaskHelp().setReaderStatus(false);
            getTaskHelp().setMsg(e.getMessage());
            log.error(e.getMessage(), e);
            throw DataTranException.asDataTranException(CommonErrorCode.READ_SQL_QUERY, String.format("执行的SQL为[%s]", this.querySql));
        } finally {
            JdbcUtils.close(conn, null, rs);
        }
    }

    protected com.dcube.tran.element.Record transportOneRecord(RecordSender recordSender, ResultSet rs, ResultSetMetaData metaData, int columnNumber) {
        com.dcube.tran.element.Record record = buildRecord(rs, metaData, columnNumber);
        while (getTaskHelp().checkStatus()) {
            if (recordSender.sendToWriter(record, waitSeconds)) {
                break;
            }
        }
        return record;
    }

    protected com.dcube.tran.element.Record buildRecord(ResultSet rs, ResultSetMetaData metaData, int columnCount) {
        com.dcube.tran.element.Record record = new DefaultRecord();
        try {
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                buildRecord(rs, metaData, i, columnName, record);
            }
        } catch (Exception e) {
            log.error("read data {} occur exception:", record, e);
            throw new IllegalStateException(e);

        }
        return record;
    }

    protected void buildRecord(ResultSet rs, ResultSetMetaData metaData, int i, String columnName, com.dcube.tran.element.Record record) throws SQLException {
        switch (metaData.getColumnType(i)) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.LONGVARCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
                StringColumn stringColumn1 = new StringColumn(rs.getString(i));
                stringColumn1.setColumnName(columnName);
                record.addColumn(stringColumn1);
                break;
            // 与167行的修改，是为了解决oracle中raw类型的乱码问题
            case Types.VARBINARY:
            case Types.CLOB:
            case Types.NCLOB:
                StringColumn stringColumn = new StringColumn(rs.getString(i));
                stringColumn.setColumnName(columnName);
                record.addColumn(stringColumn);
                break;

            case Types.SMALLINT:
            case Types.TINYINT:
            case Types.INTEGER:
            case Types.BIGINT:
                LongColumn longColumn1 = new LongColumn(rs.getString(i));
                longColumn1.setColumnName(columnName);
                record.addColumn(longColumn1);
                break;

            case Types.NUMERIC:
            case Types.DECIMAL:
            case Types.FLOAT:
            case Types.REAL:
            case Types.DOUBLE:
                DoubleColumn doubleColumn = new DoubleColumn(rs.getString(i));
                doubleColumn.setColumnName(columnName);
                record.addColumn(doubleColumn);
                break;

            case Types.TIME:
                DateColumn dateColumn1 = new DateColumn(rs.getTime(i));
                dateColumn1.setColumnName(columnName);
                record.addColumn(dateColumn1);
                break;

            // for mysql bug, see http://bugs.mysql.com/bug.php?id=35115
            case Types.DATE:
                if (metaData.getColumnTypeName(i).equalsIgnoreCase(
                        "year")) {
                    LongColumn longColumn = new LongColumn(rs.getInt(i));
                    longColumn.setColumnName(columnName);
                    record.addColumn(longColumn);
                } else {
                    DateColumn dateColumn = new DateColumn(rs.getDate(i));
                    dateColumn.setColumnName(columnName);
                    record.addColumn(dateColumn);
                }
                break;

            case Types.TIMESTAMP:
                DateColumn dateColumn = new DateColumn(rs.getTimestamp(i));
                dateColumn.setColumnName(columnName);
                record.addColumn(dateColumn);
                break;

            case Types.BINARY:
//                    case Types.VARBINARY:
            case Types.BLOB:
            case Types.LONGVARBINARY:
                BytesColumn bytesColumn = new BytesColumn(rs.getBytes(i));
                bytesColumn.setColumnName(columnName);
                record.addColumn(bytesColumn);
                break;

            // warn: bit(1) -> Types.BIT 可使用BoolColumn
            // warn: bit(>1) -> Types.VARBINARY 可使用BytesColumn
            case Types.BOOLEAN:
            case Types.BIT:
                BoolColumn column = new BoolColumn(rs.getBoolean(i));
                column.setColumnName(columnName);
                record.addColumn(column);
                break;

            case Types.NULL:
                String stringData = null;
                if (rs.getObject(i) != null) {
                    stringData = rs.getObject(i).toString();
                }
                StringColumn stringColumn2 = new StringColumn(stringData);
                stringColumn2.setColumnName(columnName);
                record.addColumn(stringColumn2);
                break;
            //case Types.TIME_WITH_TIMEZONE:
            //case Types.TIMESTAMP_WITH_TIMEZONE:
            //    record.addColumn(new StringColumn(rs.getString(i)));
            //    break;

            default:
                // warn:not support INTERVAL etc: Types.JAVA_OBJECT
                throw DataTranException
                        .asDataTranException(
                                CommonErrorCode.READ_NOT_SUPPORT,
                                String.format(
                                        "不支持数据库读取这种字段类型. 字段名:[%s], 字段类型名称:[%s], 字段Java类型:[%s].",
                                        metaData.getColumnLabel(i),
                                        metaData.getColumnTypeName(i),
                                        metaData.getColumnClassName(i)));
        }
    }

    protected ResultSet query(Connection conn, String sql, int fetchSize) throws SQLException {
        return query(conn, sql, fetchSize, SOCKET_TIMEOUT_INSECOND);
    }

    protected ResultSet query(Connection conn, String sql, int fetchSize, int queryTimeout) throws SQLException {
        // make sure autocommit is off
        if (!repository.getDbType().equals(JdbcUtils.DB_TYPE_CLICKHOUSE)) {
            conn.setAutoCommit(false);
        }
        Statement stmt = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        stmt.setFetchSize(fetchSize);
        stmt.setQueryTimeout(queryTimeout);
        return query(stmt, sql);
    }

    protected ResultSet query(Statement stmt, String sql) throws SQLException {
        return stmt.executeQuery(sql);
    }
}