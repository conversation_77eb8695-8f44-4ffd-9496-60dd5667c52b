package com.dcube.tran.execution;

import com.dcube.biz.json.DerivationJson;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.DefaultRecord;
import com.dcube.tran.element.LongColumn;
import com.dcube.tran.element.Record;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

public class DerivationTranExecution extends AbstractTranExecution {

    public DerivationTranExecution(Map<String, Object> tranParasMap) {
        super(tranParasMap);
        assert null != tranParasMap;
        this.json = (DerivationJson) tranParasMap.get("json");
        this.mainKeys = new HashSet<>(Arrays.asList(((String) tranParasMap.get("mainKeys")).split(",")));
        this.subKey = (String) tranParasMap.get("subKey");
        this.json.setInitValue(1);
        this.json.setIntervalValue(1);
    }

    private final Set<String> mainKeys;

    private final String subKey;

    private final DerivationJson json;

    @Override
    public List<Record> doTran(Record record) {
        int limitMax = 1;
        for (Column column : record.getColumnList()) {
            if (column.getColumnName().equals(json.getLimitColumnCode())) {
                limitMax = column.asLong().intValue();
                break;
            }
        }

        //预先收集需要复制的列，避免多次遍历
        List<Column> addColumns;
        if (CollectionUtils.isNotEmpty(mainKeys)) {
            addColumns = new ArrayList<>(mainKeys.size());
            for (Column column : record.getColumnList()) {
                if (mainKeys.contains(column.getColumnName())) {
                    addColumns.add(column);
                }
            }
        } else {
            addColumns = Collections.emptyList();
        }

        List<Record> records = new ArrayList<>();
        int initValue = json.getInitValue();
        int intervalValue = json.getIntervalValue();
        for (int i = initValue; i <= limitMax; i = i + intervalValue) {
            Record copy = new DefaultRecord();
            copy.addColumns(addColumns);
            Column subKeyColumn = new LongColumn(i);
            subKeyColumn.setColumnName(subKey);
            copy.addColumn(subKeyColumn);
            records.add(copy);
        }
        return records;
    }
}
