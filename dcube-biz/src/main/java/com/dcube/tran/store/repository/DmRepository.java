package com.dcube.tran.store.repository;

import com.dcube.biz.base.PageDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.grid.TableMetaData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class DmRepository extends AbstractRepository {

    DmRepository(String dbType, SourceConfigJson config, String tableName) {
        super(dbType, config, tableName);
    }

    @Override
    public List<Map<String, Object>> listPage(String sql, PageDto pageDto) {
        // 达梦数据库使用 LIMIT 和 OFFSET 进行分页
        String paginatedSql = String.format("%s LIMIT %d OFFSET %d", sql, pageDto.getPageSize(), pageDto.getSkip());
        return this.executeQuerySql(paginatedSql);
    }

    @Override
    public List<Map<String, Object>> avgAndSum(String sql) {
        return this.executeQuerySql(sql);
    }

    private static final String DM_PRIMARY_KEY = "CONSTRAINT %s_pk PRIMARY KEY (%s)";
    private static final String DM_COLUMN_STRING_FORMAT = "%s VARCHAR(%s)";
    private static final String DM_COLUMN_INT_FORMAT = "%s INT";
    private static final String DM_COLUMN_DOUBLE_FORMAT = "%s NUMERIC(%s,%s)";
    private static final String DM_COLUMN_DATETIME_FORMAT = "%s DATETIME";
    private static final String DM_COLUMN_DATE_FORMAT = "%s DATE";
    private static final String DM_COLUMN_LONGTEXT_FORMAT = "%s CLOB";
    private static final String DM_COLUMN_BLOB_FORMAT = "%s BLOB";

    @Override
    public synchronized void syncTable(String databaseName, TableMetaData tableMetaData) {
        /*boolean hitFlag = super.existsTableCheck(databaseName, null);
        if (hitFlag) {
            //删除表
            super.executeSql(String.format("DROP TABLE %s", getTableName().toLowerCase()));
        }*/
        List<String> columnList = buildCreateTableColumns(tableMetaData);
        String sql = String.format("CREATE TABLE %s (%s)", getTableName().toLowerCase(), String.join(",", columnList));
        try {
            super.executeSql(sql);
        } catch (Exception ex) {
            log.error("syncTable executeSql:{} error:", sql, ex);
            boolean showDetail = SpringUtils.getBean(GlobalExceptionProperties.class).isShowDetail();
            if (showDetail) {
                throw new ServiceException(ex);
            } else {
                throw new ServiceException("数据库操作失败，请联系系统管理员！");
            }
        }
    }

    private List<String> buildCreateTableColumns(TableMetaData tableMetaData) {
        List<String> columns = new ArrayList<>(tableMetaData.getColumnTypes().size());
        for (int i = 0; i < tableMetaData.getColumnTypes().size(); i++) {
            Class<?> clazz = tableMetaData.getColumnTypes().get(i);
            if (Integer.class == clazz) {
                columns.add(String.format(DM_COLUMN_INT_FORMAT, tableMetaData.getColumnNames().get(i)));
            } else if (Double.class == clazz) {
                columns.add(String.format(DM_COLUMN_DOUBLE_FORMAT, tableMetaData.getColumnNames().get(i), 32, 8));
            } else if (Date.class == clazz) {
                columns.add(String.format(DM_COLUMN_DATETIME_FORMAT, tableMetaData.getColumnNames().get(i)));
            } else if (String.class == clazz) {
                columns.add(String.format(DM_COLUMN_STRING_FORMAT, tableMetaData.getColumnNames().get(i), 8188));
            } else {
                log.error("Unsupported Class: {}", clazz);
            }
        }
        //columns.add(String.format(DM_PRIMARY_KEY, getTableName(), tableMetaData.getColumnNames().get(0)));
        return columns;
    }

    @Override
    public int getMaxId(String sql) {
        return 0;
    }
}