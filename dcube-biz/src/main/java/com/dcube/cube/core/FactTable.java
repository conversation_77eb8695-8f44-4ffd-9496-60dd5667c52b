package com.dcube.cube.core;

import com.dcube.cube.math.DoubleDouble;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.RoaringBitmap;
import org.springframework.core.Ordered;
import org.springframework.util.Assert;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.*;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.BiFunction;

@Slf4j
public class FactTable {

    private FactTable(String name) {
        // Internal
        Meta meta = new Meta();
        meta.name = name;
        Assert.hasText(name, "Fact-table name can not empty.");

        this.meta = meta;
        this.records = new HashMap<>(0);
    }

    @Getter
    public Meta meta;

    @Getter
    private final Map<Integer, Record> records;

    @Getter
    private final Map<String, RoaringBitmap> bitmapIndex = new HashMap<>();

    @Getter
    private final FactTable.FactTableBuilder builder = null;

    private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock();

    public Map<String, Object> getData() {
        try {
            readWriteLock.readLock().lock();
            Map<String, Object> data = new HashMap<>(2);
            data.put("records", records);
            data.put("bitmapIndex", bitmapIndex);
            return data;
        } finally {
            readWriteLock.readLock().unlock();
        }
    }

    public void merge(FactTable merge) {
        if (merge == null) {
            throw new IllegalArgumentException();
        }
        if (log.isDebugEnabled()) {
            log.debug("Try to merge {} into {}.", merge, this);
        }
        try {
            readWriteLock.writeLock().lock();
            // Start merge
            this.records.putAll(merge.records);
            for (Map.Entry<String, RoaringBitmap> entry : merge.bitmapIndex.entrySet()) {
                this.bitmapIndex.merge(entry.getKey(), entry.getValue(),
                        new BiFunction<RoaringBitmap, RoaringBitmap, RoaringBitmap>() {
                            @Override
                            public RoaringBitmap apply(RoaringBitmap t, RoaringBitmap u) {
                                return RoaringBitmap.or(t, u);
                            }
                        });
            }
        } finally {
            readWriteLock.writeLock().unlock();
        }
        if (log.isDebugEnabled()) {
            log.debug("Merge {} successfully into {}.", merge, this);
        }
    }

    public int getIndIndex(String indName) throws IllegalArgumentException {
        int index = -1;
        if (indName == null || indName.isEmpty() || (index = meta.indColumnNames.get(indName)) < 0) {
            throw new IllegalArgumentException();
        }
        return index;
    }

    public int getDimIndex(String dimName) throws IllegalArgumentException {
        int index = -1;
        if (dimName == null || "".equals(dimName) || (index = meta.dimColumnNames.get(dimName)) < 0) {
            throw new IllegalArgumentException();
        }
        return index;
    }

    @Override
    public String toString() {
        return "FactTable [meta=" + meta + ", records=" + records.size() + "]";
    }


    @ToString
    public static class Meta {

        public String name;
        @Getter
        private final LinkedHashMap<String, Integer> indColumnNames = new LinkedHashMap<>();
        @Getter
        private final LinkedHashMap<String, Integer> dimColumnNames = new LinkedHashMap<>();

    }

    @ToString
    public class Record {

        // Equal to PK. Can hold 2^31 records.
        private final int id;

        /**
         * Use DoubleDouble for better performance. See http://tsusiatsoftware.net/dd/main.html
         */
        @Setter
        @Getter
        private DoubleDouble[] indOfFact = null;

        @Setter
        @Getter
        private String[] dimOfFact = null;

        public Record(Integer id) {
            super();
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public DoubleDouble getInd(String indName) {

            int index = FactTable.this.getIndIndex(indName);
            return indOfFact[index];
        }

        public String getDim(String dimName) {

            final int index = FactTable.this.getDimIndex(dimName);
            return dimOfFact[index];
        }

    }

    public interface FactTableBuilderUserDefineDimProvider extends Ordered {

        /**
         * @return column and expr configuration for user define dimensions. MUST NOT NULL.
         */
        LinkedHashMap<String, String> getUserDefineDimConfig();
    }

    public static class FactTableBuilder {
        private static final ThreadLocal<FactTable> IN_BUILDING = new ThreadLocal<>();

        private static final List<FactTableBuilderUserDefineDimProvider> providers = new ArrayList<>();
        private static final ScriptEngine scriptEngine;

        static {
            ServiceLoader<FactTableBuilderUserDefineDimProvider> serviceLoader = ServiceLoader.load(FactTableBuilderUserDefineDimProvider.class);
            for (FactTableBuilderUserDefineDimProvider factTableBuilderUserDefineDimProvider : serviceLoader) {
                providers.add(factTableBuilderUserDefineDimProvider);
            }
            Collections.sort(providers, new Comparator<FactTableBuilderUserDefineDimProvider>() {
                @Override
                public int compare(FactTableBuilderUserDefineDimProvider o1,
                                   FactTableBuilderUserDefineDimProvider o2) {
                    return o1.getOrder() - o2.getOrder();
                }
            });
            if (log.isDebugEnabled()) {
                log.debug("Retrieve user define dimension providers {}", providers);
            }

            scriptEngine = new ScriptEngineManager().getEngineByName("nashorn");

            for (FactTableBuilderUserDefineDimProvider p : providers) {
                for (Map.Entry<String, String> e : p.getUserDefineDimConfig().entrySet()) {
                    try {
                        scriptEngine.eval(e.getValue());
                        if (log.isDebugEnabled()) {
                            log.debug("register user-define column {} expr {}", e.getKey(), e.getValue());
                        }
                    } catch (Exception e1) {
                        log.error("Error occurred when try to process user-define column {} expr {}", e.getKey(), e.getValue());
                        throw new RuntimeException(e1);
                    }
                }
            }
        }

        /**
         * Constructor
         */
        public FactTableBuilder() {
            super();
        }

        /**
         * Constructor
         */
        public FactTableBuilder(FactTable factTable) {
            if (IN_BUILDING.get() != null) {
                throw new IllegalStateException("Previous building " + IN_BUILDING.get() + " is doing call #done to finish it.");
            }
            IN_BUILDING.set(factTable);
        }

        public FactTableBuilder build(String name) {
            if (IN_BUILDING.get() != null) {
                throw new IllegalStateException("Previous building " + IN_BUILDING.get() + " is doing call #done to finish it.");
            }
            IN_BUILDING.set(new FactTable(name));
            return this;
        }

        public FactTableBuilder addDimColumns(List<String> dimColumnNames) {
            FactTable current = IN_BUILDING.get();
            if (current == null) {
                throw new IllegalStateException("Current building is not started, call #build first.");
            }
            for (String dimColumnName : dimColumnNames) {
                if (current.meta.dimColumnNames.containsKey(dimColumnName)) {
                    throw new IllegalStateException("Dimension " + dimColumnName + " has exists.");
                }
                current.meta.dimColumnNames.put(dimColumnName, current.meta.dimColumnNames.size());
            }

            // Add user-define dimension process
            for (FactTableBuilderUserDefineDimProvider p : providers) {
                for (String key : p.getUserDefineDimConfig().keySet()) {
                    current.meta.dimColumnNames.put(key, current.meta.dimColumnNames.size());
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("Complete filling user-define dimension and now dimension columns is {}", current.meta.dimColumnNames);
            }
            return this;
        }

        public FactTableBuilder addIndColumns(List<String> indColumnNames) {
            FactTable current = IN_BUILDING.get();
            if (current == null) {
                throw new IllegalStateException("Current building is not started, call #build first.");
            }
            for (String indColumnName : indColumnNames) {
                if (current.meta.indColumnNames.containsKey(indColumnName)) {
                    throw new IllegalStateException("Indication " + indColumnName + " has exists.");
                }
                current.meta.indColumnNames.put(indColumnName, current.meta.indColumnNames.size());
            }
            return this;
        }

        public FactTableBuilder addDimDatas(Integer primaryKey, List<String> dimDatas) {
            FactTable current = IN_BUILDING.get();
            if (current == null) {
                throw new IllegalStateException("Current building is not started, call #build first.");
            }
            Assert.isTrue(!current.meta.dimColumnNames.isEmpty(), "Fact-table must have a dimension column at least.");

            Record record = current.records.computeIfAbsent(primaryKey, k -> current.new Record(k));

            // Fill dimension data
            fillDimDatas(current, record, dimDatas);
            return this;
        }

        private void fillDimDatas(FactTable current, Record record, List<String> dimDatas) {
            // Build bitmap index
            int size = dimDatas.size();
            int baseIndex = record.dimOfFact == null ? 0 : record.dimOfFact.length - size;
            for (int i = 0; i < size; i++) {
                String dimValue = dimDatas.get(i);
                int fi = baseIndex + i;
                // Fill dimension value
                if (record.dimOfFact == null) {
                    record.dimOfFact = new String[current.meta.dimColumnNames.size()];
                }
                record.dimOfFact[fi] = dimValue;

                // Index dimension value
                String column = current.meta.dimColumnNames.entrySet().stream().filter(v -> v.getValue() == fi).findFirst().get().getKey();
                String bitMapKey = column + ":" + dimValue;
                RoaringBitmap bitmap = current.bitmapIndex.computeIfAbsent(bitMapKey, k -> new RoaringBitmap());
                bitmap.add(record.getId());
            }
        }

        public FactTableBuilder addIndDatas(Integer primaryKey, List<DoubleDouble> indDatas) {
            FactTable current = IN_BUILDING.get();
            if (current == null) {
                throw new IllegalStateException("Current building is not started, call #build first.");
            }
            Record record = current.records.computeIfAbsent(primaryKey, k -> current.new Record(k));
            record.indOfFact = indDatas.toArray(new DoubleDouble[0]);
            if (record.indOfFact.length != current.meta.indColumnNames.size()) {
                throw new IllegalStateException("Current version only support one-time indicator data filling.");
            }

            // Add user-define dimension process
            /*int i = -1;
            for (String indColumn : current.meta.indColumnNames.keySet()) {
                i++;
                scriptEngine.put(indColumn, indDatas.get(i).doubleValue());
            }*/

            List<String> userDefineDimensions = new ArrayList<>();
            /*for (FactTableBuilderUserDefineDimProvider p : providers) {
                for (Map.Entry<String, String> e : p.getUserDefineDimConfig().entrySet()) {
                    try {
                        Invocable inv = (Invocable) scriptEngine;
                        Object o = inv.invokeFunction(e.getKey(), new Object[0]);
                        userDefineDimensions.add(o.toString());
                        log.debug("process user-define column {} expr {} value {}", e.getKey(), e.getValue(), o);
                    } catch (Exception e1) {
                        log.error("Error occurred when try to process user-define column {} expr {}", e.getKey(), e.getValue());
                        throw new RuntimeException(e1);
                    }
                }
            }*/
            fillDimDatas(current, record, userDefineDimensions);
            return this;
        }

        public FactTable done() {
            FactTable current = IN_BUILDING.get();
            if (current == null) {
                throw new IllegalStateException("Current building is not started, call #build first.");
            }
            IN_BUILDING.remove();

//            Set<String> allNames = new HashSet<>();
//            allNames.addAll(current.meta.dimColumnNames.keySet());
//            allNames.addAll(current.meta.indColumnNames.keySet());
//            Assert.isTrue(allNames.size() == current.meta.dimColumnNames.size() + current.meta.indColumnNames.size(), "Contains same name between dimension and indicators.");

            int usedKb = 0;
            int usedBytes = 0;
            for (Map.Entry<String, RoaringBitmap> e : current.bitmapIndex.entrySet()) {
                e.getValue().trim();
                if (usedBytes > (1024 * 1024 * 1024)) {
                    usedKb = usedKb + (usedBytes / 1024);
                    usedBytes = 0;
                }
                usedBytes = usedBytes + e.getValue().getSizeInBytes();
                if (log.isDebugEnabled()) {
                    log.debug("Index for {} of {} records", e.getKey(), e.getValue().getCardinality());
                }
            }
            usedKb = usedKb + (usedBytes / 1024);
            if (log.isDebugEnabled()) {
                log.debug("Build completed: name {} with {} dimension columns, {} measure columns and {} records, {} indexes used {} kb.",
                        current.meta.name, current.meta.dimColumnNames.size(), current.meta.indColumnNames.size(),
                        current.records.size(), current.bitmapIndex.size(), usedKb);
            }
            return current;
        }
    }
}
