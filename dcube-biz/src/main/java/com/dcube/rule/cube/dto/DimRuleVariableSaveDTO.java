package com.dcube.rule.cube.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
@ToString
public class DimRuleVariableSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "多维表id")
    private Long dimTableId;

    @Schema(description = "多维表规则id")
    private Long dimRuleId;

    /**
     * 变量名称
     */
    @Schema(description = "变量名称")
    private String variableName;

    @Schema(description = "多维变量详情")
    private List<DimRuleVariableDetailSaveDTO> variableDetails;

}
