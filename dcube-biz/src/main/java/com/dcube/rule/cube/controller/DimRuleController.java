package com.dcube.rule.cube.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.rule.cube.dto.*;
import com.dcube.rule.cube.service.IDimRuleService;
import com.dcube.rule.cube.service.IDimRuleServiceHelperService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 多维计算规则Controller
 */
@RestController
@RequestMapping("/cube/dimRule")
@Tag(name = "DCUBE-多维计算规则", description = "DCUBE-多维计算规则")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DimRuleController extends BaseController {

    private final IDimRuleService dimRuleService;
    private final IDimRuleServiceHelperService dimRuleServiceHelperService;

    @Operation(summary = "查询规则")
    @GetMapping("/get")
    public AjaxResult getCubeRule(@RequestParam("dimTableId") Long dimTableId) {
        return AjaxResult.success(dimRuleService.getCubeRule(dimTableId));
    }

    @PutMapping("/move")
    @Operation(summary = "移动规则顺序")
    @Log(title = "移动规则顺序", businessType = BusinessType.UPDATE)
    public AjaxResult moveGroup(@RequestBody DimRuleMoveDTO moveDto) {
        Assert.notNull(moveDto.getId(), "id不能为空");
        Assert.notNull(moveDto.getMoveType(), "移动方式不能为空");
        return AjaxResult.success(dimRuleService.move(moveDto.getId(), moveDto.getMoveType()));
    }

    @PostMapping("/add")
    @Operation(summary = "添加规则")
    @Log(title = "添加规则", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody DimRuleAddDTO addDto) {
        return AjaxResult.success(dimRuleService.add(addDto));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除规则")
    @Log(title = "删除规则", businessType = BusinessType.DELETE)
    public AjaxResult deleteById(@Parameter(description = "删除规则") @PathVariable("id") Long id) {
        return AjaxResult.success(dimRuleService.deleteById(id));
    }

    @Operation(summary = "查询指标运算规则")
    @PostMapping("/getIndicatorOperation")
    public AjaxResult getIndicatorOperation(@RequestParam("dimRuleId") Long dimRuleId) {
        return AjaxResult.success(dimRuleService.getIndicatorOperation(dimRuleId));
    }

    @Operation(summary = "保存指标运算规则")
    @Log(title = "保存指标运算规则", businessType = BusinessType.INSERT)
    @PostMapping("/saveIndicatorOperation")
    public AjaxResult saveIndicatorOperation(@RequestBody IndicatorOperationSaveDTO indicatorOperationSaveDto) {
        return AjaxResult.success(dimRuleService.saveIndicatorOperation(indicatorOperationSaveDto));
    }

    @Operation(summary = "批量保存指标维度作用范围")
    @Log(title = "批量保存指标维度作用范围", businessType = BusinessType.INSERT)
    @PostMapping("/saveIndicatorOperationEffectScopes")
    public AjaxResult saveIndicatorOperationEffectScopes(@RequestBody IndicatorOperationEffectScopeBatchSaveDTO indicatorOperationEffectScopeBatchSaveDto) {
        return AjaxResult.success(dimRuleService.saveIndicatorOperationEffectScopes(indicatorOperationEffectScopeBatchSaveDto));
    }

    @Operation(summary = "查询指标运算规则的维度")
    @PostMapping("/queryIndicatorOperationDimInstance")
    public AjaxResult queryIndicatorOperationDimInstance(@RequestBody IndicatorOperationDimInstanceQueryDTO indicatorOperationDimInstanceQueryDto) {
        return AjaxResult.success(dimRuleService.queryIndicatorOperationDimInstance(indicatorOperationDimInstanceQueryDto));
    }

    @Operation(summary = "查询指标是否有规则")
    @GetMapping("/getIndHasRule")
    public AjaxResult getIndHasRule(@RequestParam("indId") String indId) {
        return AjaxResult.success(dimRuleService.getIndHasRule(Arrays.asList(indId.split(","))));
    }

    @Operation(summary = "执行多维表计算规则")
    @PostMapping("/execute")
    @Log(title = "执行多维表计算规则", businessType = BusinessType.INSERT)
    public AjaxResult execute(@RequestParam("dimTableId") Integer dimTableId) {
        dimRuleServiceHelperService.execute(dimTableId);
        return AjaxResult.success();
    }

    @Operation(summary = "查询规则的多维变量")
    @GetMapping("/listDimVariable")
    public AjaxResult listDimVariable(@RequestParam("dimRuleId") Long dimRuleId) {
        return AjaxResult.success(dimRuleService.listDimVariable(dimRuleId));
    }

    @Operation(summary = "查询维度计算的维度")
    @GetMapping("/getDimRuleDims")
    public AjaxResult getDimRuleDims(@RequestParam("tableId") Long tableId,
                                     @RequestParam(value = "dimVariableId", required = false) Long dimVariableId) {
        return AjaxResult.success(dimRuleService.getDimRuleDims(tableId, dimVariableId));
    }

    @Operation(summary = "保存多维变量")
    @Log(title = "保存多维变量", businessType = BusinessType.INSERT)
    @PostMapping("/saveDimVariable")
    public AjaxResult saveDimVariable(@RequestBody DimRuleVariableSaveDTO dimRuleVariableSaveDTO) {
        return AjaxResult.success(dimRuleService.saveDimVariable(dimRuleVariableSaveDTO));
    }

    @Operation(summary = "删除多维变量")
    @Log(title = "删除多维变量", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeDimVariable")
    public AjaxResult removeDimVariable(@RequestParam("id") Long id) {
        return AjaxResult.success(dimRuleService.removeDimVariable(id));
    }

    @Operation(summary = "保存维度运算规则")
    @Log(title = "保存维度运算规则", businessType = BusinessType.INSERT)
    @PostMapping("/saveDimOperation")
    public AjaxResult saveDimOperation(@Validated @RequestBody DimOperationSaveDTO dimOperationSaveDTO) {
        return AjaxResult.success(dimRuleService.saveDimOperation(dimOperationSaveDTO));
    }

}
