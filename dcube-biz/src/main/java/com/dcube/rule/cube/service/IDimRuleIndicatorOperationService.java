package com.dcube.rule.cube.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cube_dim_rule_indicator_operation(多维计算规则指标运算)】的数据库操作Service
 * @createDate 2024-06-30 13:08:21
 */
public interface IDimRuleIndicatorOperationService extends IService<DimRuleIndicatorOperation> {

    List<DimRuleIndicatorOperation> getIndicatorOperations(Long dimRuleId, IndicatorOperationTypeEnum indicatorOperationType);

    List<DimRuleIndicatorOperation> listByDimTableIdAndIndicatorOperationType(Integer dimTableId, IndicatorOperationTypeEnum indicatorOperationTypeEnum);

}
