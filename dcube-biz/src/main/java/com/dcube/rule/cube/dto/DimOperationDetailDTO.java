package com.dcube.rule.cube.dto;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class DimOperationDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运算详情类型
     */
    @Schema(description = "运算详情类型（四则运算，括号，变量）", requiredMode = Schema.RequiredMode.REQUIRED)
    private DimOperationDetailTypeEnum dimOperationDetailType;

    /**
     * 变量ID
     */
    @Schema(description = "变量ID")
    private Long dimVariableId;

    /**
     * 变量名
     */
    @Schema(description = "变量名")
    private String dimVariableName;

    /**
     * 维度操作类型（加减乘除）
     */
    @Schema(description = "维度操作类型（加减乘除）")
    private DimOperationFundamentalTypeEnum fundamentalType;

    /**
     * 括号操作类型（左右）
     */
    @Schema(description = "括号操作类型（左右）")
    private DimOperationBracketTypeEnum bracketType;

}
