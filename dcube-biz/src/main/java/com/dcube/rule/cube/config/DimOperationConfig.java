package com.dcube.rule.cube.config;

import com.dcube.rule.cube.utils.DimOperationUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

/**
 * 维度运算配置类
 * 
 * 用于配置 DimOperationUtil 的跟踪和性能参数
 */
@Configuration
@ConfigurationProperties(prefix = "dcube.dim-operation")
public class DimOperationConfig {

    /**
     * 是否启用跟踪功能
     */
    private boolean traceEnabled = false;

    /**
     * 跟踪级别：debug, info, warn, error, trace, detailed
     */
    private String traceLevel = "debug";

    /**
     * SLF4J 日志级别：TRACE, DEBUG, INFO, WARN, ERROR
     */
    private String slf4jLevel = "DEBUG";

    /**
     * 是否包含时间戳
     */
    private boolean includeTimestamp = false;

    /**
     * 是否包含调用栈信息
     */
    private boolean includeStackTrace = false;

    /**
     * 性能监控阈值（毫秒）
     * 超过此阈值的执行会被记录
     */
    private long performanceThresholdMs = 100;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = false;

    /**
     * 获取跟踪配置
     */
    public DimOperationUtil.TraceConfig getTraceConfig() {
        return getTraceConfig(null);
    }

    /**
     * 获取跟踪配置（带自定义日志器）
     */
    public DimOperationUtil.TraceConfig getTraceConfig(Consumer<String> customLogger) {
        if (!traceEnabled) {
            return DimOperationUtil.TraceConfig.disabled();
        }

        if (customLogger != null) {
            return new DimOperationUtil.TraceConfig(true, customLogger, includeTimestamp, includeStackTrace,
                    DimOperationUtil.TraceConfig.LogLevel.valueOf(slf4jLevel.toUpperCase()));
        }

        // 如果启用 SLF4J
        if (useSlf4j) {
            return getSlf4jTraceConfig();
        }

        // 传统配置
        switch (traceLevel.toLowerCase()) {
            case "detailed":
                return DimOperationUtil.TraceConfig.detailed();
            case "simple":
            default:
                return DimOperationUtil.TraceConfig.simple();
        }
    }

    /**
     * 获取 SLF4J 跟踪配置
     */
    private DimOperationUtil.TraceConfig getSlf4jTraceConfig() {
        DimOperationUtil.TraceConfig.LogLevel logLevel;
        try {
            logLevel = DimOperationUtil.TraceConfig.LogLevel.valueOf(slf4jLevel.toUpperCase());
        } catch (IllegalArgumentException e) {
            logLevel = DimOperationUtil.TraceConfig.LogLevel.DEBUG;
        }

        switch (traceLevel.toLowerCase()) {
            case "detailed":
            case "slf4j-detailed":
                return DimOperationUtil.TraceConfig.slf4jDetailed(logLevel);
            case "slf4j-trace":
                return DimOperationUtil.TraceConfig.slf4jTrace();
            case "slf4j-debug":
                return DimOperationUtil.TraceConfig.slf4jDebug();
            case "slf4j-info":
                return DimOperationUtil.TraceConfig.slf4jInfo();
            case "slf4j-warn":
                return DimOperationUtil.TraceConfig.slf4jWarn();
            case "slf4j-error":
                return DimOperationUtil.TraceConfig.slf4jError();
            case "simple":
            default:
                return DimOperationUtil.TraceConfig.slf4jCustom(logLevel, includeTimestamp, includeStackTrace);
        }
    }

    /**
     * 检查是否需要性能监控
     */
    public boolean shouldMonitorPerformance(long executionTimeMs) {
        return performanceMonitorEnabled && executionTimeMs > performanceThresholdMs;
    }

    // Getters and Setters
    public boolean isTraceEnabled() {
        return traceEnabled;
    }

    public void setTraceEnabled(boolean traceEnabled) {
        this.traceEnabled = traceEnabled;
    }

    public String getTraceLevel() {
        return traceLevel;
    }

    public void setTraceLevel(String traceLevel) {
        this.traceLevel = traceLevel;
    }

    public boolean isIncludeTimestamp() {
        return includeTimestamp;
    }

    public void setIncludeTimestamp(boolean includeTimestamp) {
        this.includeTimestamp = includeTimestamp;
    }

    public boolean isIncludeStackTrace() {
        return includeStackTrace;
    }

    public void setIncludeStackTrace(boolean includeStackTrace) {
        this.includeStackTrace = includeStackTrace;
    }

    public long getPerformanceThresholdMs() {
        return performanceThresholdMs;
    }

    public void setPerformanceThresholdMs(long performanceThresholdMs) {
        this.performanceThresholdMs = performanceThresholdMs;
    }

    public boolean isPerformanceMonitorEnabled() {
        return performanceMonitorEnabled;
    }

    public void setPerformanceMonitorEnabled(boolean performanceMonitorEnabled) {
        this.performanceMonitorEnabled = performanceMonitorEnabled;
    }

    public boolean isUseSlf4j() {
        return useSlf4j;
    }

    public void setUseSlf4j(boolean useSlf4j) {
        this.useSlf4j = useSlf4j;
    }

    public String getSlf4jLevel() {
        return slf4jLevel;
    }

    public void setSlf4jLevel(String slf4jLevel) {
        this.slf4jLevel = slf4jLevel;
    }
}
