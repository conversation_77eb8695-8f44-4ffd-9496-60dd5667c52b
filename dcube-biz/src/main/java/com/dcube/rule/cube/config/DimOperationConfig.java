package com.dcube.rule.cube.config;

import com.dcube.rule.cube.utils.DimOperationUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

/**
 * 维度运算配置类
 * 
 * 用于配置 DimOperationUtil 的跟踪和性能参数
 */
@Configuration
@ConfigurationProperties(prefix = "dcube.dim-operation")
public class DimOperationConfig {

    /**
     * 是否启用跟踪功能
     */
    private boolean traceEnabled = false;

    /**
     * 跟踪级别：debug, info, warn, error, trace, detailed
     */
    private String traceLevel = "debug";

    /**
     * SLF4J 日志级别：TRACE, DEBUG, INFO, WARN, ERROR
     */
    private String slf4jLevel = "DEBUG";

    /**
     * 是否包含时间戳
     */
    private boolean includeTimestamp = false;

    /**
     * 是否包含调用栈信息
     */
    private boolean includeStackTrace = false;

    /**
     * 性能监控阈值（毫秒）
     * 超过此阈值的执行会被记录
     */
    private long performanceThresholdMs = 100;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = false;


    /**
     * 获取跟踪配置
     */
    public DimOperationUtil.TraceConfig getTraceConfig() {
        if (!traceEnabled) {
            return DimOperationUtil.TraceConfig.disabled();
        }

        DimOperationUtil.TraceConfig.LogLevel logLevel = parseLogLevel(slf4jLevel);

        // 根据跟踪级别创建配置
        DimOperationUtil.TraceConfig config = createBaseConfig(traceLevel, logLevel);

        // 应用自定义设置
        return config.withTimestamp(includeTimestamp)
                    .withStackTrace(includeStackTrace);
    }

    private DimOperationUtil.TraceConfig.LogLevel parseLogLevel(String level) {
        try {
            return DimOperationUtil.TraceConfig.LogLevel.valueOf(level.toUpperCase());
        } catch (IllegalArgumentException e) {
            return DimOperationUtil.TraceConfig.LogLevel.DEBUG;
        }
    }

    private DimOperationUtil.TraceConfig createBaseConfig(String traceLevel, DimOperationUtil.TraceConfig.LogLevel logLevel) {
        switch (traceLevel.toLowerCase()) {
            case "detailed":
                return DimOperationUtil.TraceConfig.detailed(logLevel);
            case "trace":
                return DimOperationUtil.TraceConfig.trace();
            case "debug":
                return DimOperationUtil.TraceConfig.debug();
            case "info":
                return DimOperationUtil.TraceConfig.info();
            case "warn":
                return DimOperationUtil.TraceConfig.warn();
            case "error":
                return DimOperationUtil.TraceConfig.error();
            default:
                return DimOperationUtil.TraceConfig.of(logLevel);
        }
    }

    /**
     * 检查是否需要性能监控
     */
    public boolean shouldMonitorPerformance(long executionTimeMs) {
        return performanceMonitorEnabled && executionTimeMs > performanceThresholdMs;
    }

    // Getters and Setters
    public boolean isTraceEnabled() {
        return traceEnabled;
    }

    public void setTraceEnabled(boolean traceEnabled) {
        this.traceEnabled = traceEnabled;
    }

    public String getTraceLevel() {
        return traceLevel;
    }

    public void setTraceLevel(String traceLevel) {
        this.traceLevel = traceLevel;
    }

    public boolean isIncludeTimestamp() {
        return includeTimestamp;
    }

    public void setIncludeTimestamp(boolean includeTimestamp) {
        this.includeTimestamp = includeTimestamp;
    }

    public boolean isIncludeStackTrace() {
        return includeStackTrace;
    }

    public void setIncludeStackTrace(boolean includeStackTrace) {
        this.includeStackTrace = includeStackTrace;
    }

    public long getPerformanceThresholdMs() {
        return performanceThresholdMs;
    }

    public void setPerformanceThresholdMs(long performanceThresholdMs) {
        this.performanceThresholdMs = performanceThresholdMs;
    }

    public boolean isPerformanceMonitorEnabled() {
        return performanceMonitorEnabled;
    }

    public void setPerformanceMonitorEnabled(boolean performanceMonitorEnabled) {
        this.performanceMonitorEnabled = performanceMonitorEnabled;
    }

    public String getSlf4jLevel() {
        return slf4jLevel;
    }

    public void setSlf4jLevel(String slf4jLevel) {
        this.slf4jLevel = slf4jLevel;
    }
}
