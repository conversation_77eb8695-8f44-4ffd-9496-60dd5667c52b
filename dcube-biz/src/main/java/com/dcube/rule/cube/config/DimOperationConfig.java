package com.dcube.rule.cube.config;

import com.dcube.rule.cube.utils.DimOperationUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

/**
 * 维度运算配置类
 * 
 * 用于配置 DimOperationUtil 的跟踪和性能参数
 */
@Configuration
@ConfigurationProperties(prefix = "dcube.dim-operation")
public class DimOperationConfig {

    /**
     * 是否启用跟踪功能
     */
    private boolean traceEnabled = false;

    /**
     * 跟踪级别：simple, detailed, custom
     */
    private String traceLevel = "simple";

    /**
     * 是否包含时间戳
     */
    private boolean includeTimestamp = false;

    /**
     * 是否包含调用栈信息
     */
    private boolean includeStackTrace = false;

    /**
     * 性能监控阈值（毫秒）
     * 超过此阈值的执行会被记录
     */
    private long performanceThresholdMs = 100;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = false;

    /**
     * 获取跟踪配置
     */
    public DimOperationUtil.TraceConfig getTraceConfig() {
        return getTraceConfig(null);
    }

    /**
     * 获取跟踪配置（带自定义日志器）
     */
    public DimOperationUtil.TraceConfig getTraceConfig(Consumer<String> customLogger) {
        if (!traceEnabled) {
            return DimOperationUtil.TraceConfig.disabled();
        }

        if (customLogger != null) {
            return new DimOperationUtil.TraceConfig(true, customLogger, includeTimestamp, includeStackTrace);
        }

        switch (traceLevel.toLowerCase()) {
            case "detailed":
                return DimOperationUtil.TraceConfig.detailed();
            case "simple":
            default:
                return DimOperationUtil.TraceConfig.simple();
        }
    }

    /**
     * 检查是否需要性能监控
     */
    public boolean shouldMonitorPerformance(long executionTimeMs) {
        return performanceMonitorEnabled && executionTimeMs > performanceThresholdMs;
    }

    // Getters and Setters
    public boolean isTraceEnabled() {
        return traceEnabled;
    }

    public void setTraceEnabled(boolean traceEnabled) {
        this.traceEnabled = traceEnabled;
    }

    public String getTraceLevel() {
        return traceLevel;
    }

    public void setTraceLevel(String traceLevel) {
        this.traceLevel = traceLevel;
    }

    public boolean isIncludeTimestamp() {
        return includeTimestamp;
    }

    public void setIncludeTimestamp(boolean includeTimestamp) {
        this.includeTimestamp = includeTimestamp;
    }

    public boolean isIncludeStackTrace() {
        return includeStackTrace;
    }

    public void setIncludeStackTrace(boolean includeStackTrace) {
        this.includeStackTrace = includeStackTrace;
    }

    public long getPerformanceThresholdMs() {
        return performanceThresholdMs;
    }

    public void setPerformanceThresholdMs(long performanceThresholdMs) {
        this.performanceThresholdMs = performanceThresholdMs;
    }

    public boolean isPerformanceMonitorEnabled() {
        return performanceMonitorEnabled;
    }

    public void setPerformanceMonitorEnabled(boolean performanceMonitorEnabled) {
        this.performanceMonitorEnabled = performanceMonitorEnabled;
    }
}
