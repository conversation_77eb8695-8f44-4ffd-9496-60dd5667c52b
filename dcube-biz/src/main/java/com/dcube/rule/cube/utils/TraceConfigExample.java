package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TraceConfig 优化后的使用示例
 * 
 * 展示了新的链式配置 API 和简化的使用方式
 */
public class TraceConfigExample {

    public static void main(String[] args) {
        System.out.println("=== TraceConfig 优化示例 ===\n");
        
        // 创建测试表达式: (a + b) * c
        List<DimOperationDetailDTO> expression = createTestExpression();
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("10"),
                2L, new BigDecimal("5"),
                3L, new BigDecimal("2")
        );
        
        // 示例1：基础配置
        basicConfigExample(expression, values);
        
        // 示例2：链式配置
        chainConfigExample(expression, values);
        
        // 示例3：详细配置
        detailedConfigExample(expression, values);
        
        // 示例4：配置对比
        configComparisonExample(expression, values);
        
        System.out.println("\n=== 示例完成 ===");
    }

    /**
     * 示例1：基础配置
     */
    private static void basicConfigExample(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        System.out.println("1. 基础配置示例");
        
        // 简单的日志级别配置
        DimOperationUtil.TraceConfig[] configs = {
            DimOperationUtil.TraceConfig.disabled(),
            DimOperationUtil.TraceConfig.debug(),
            DimOperationUtil.TraceConfig.info(),
            DimOperationUtil.TraceConfig.warn(),
            DimOperationUtil.TraceConfig.error()
        };
        
        for (DimOperationUtil.TraceConfig config : configs) {
            DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                    expression, values, config);
            System.out.println(config + " -> 结果: " + result.getResult() + 
                             ", 时间: " + result.getExecutionTimeMs() + "ms");
        }
        System.out.println();
    }

    /**
     * 示例2：链式配置
     */
    private static void chainConfigExample(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        System.out.println("2. 链式配置示例");
        
        // 链式配置：从基础配置开始，逐步添加功能
        DimOperationUtil.TraceConfig baseConfig = DimOperationUtil.TraceConfig.info();
        System.out.println("基础配置: " + baseConfig);
        
        DimOperationUtil.TraceConfig withTimestamp = baseConfig.withTimestamp(true);
        System.out.println("添加时间戳: " + withTimestamp);
        
        DimOperationUtil.TraceConfig withStackTrace = withTimestamp.withStackTrace(true);
        System.out.println("添加调用栈: " + withStackTrace);
        
        DimOperationUtil.TraceConfig withDifferentLevel = withStackTrace.withLevel(
                DimOperationUtil.TraceConfig.LogLevel.WARN);
        System.out.println("更改级别: " + withDifferentLevel);
        
        // 一次性链式配置
        DimOperationUtil.TraceConfig chainedConfig = DimOperationUtil.TraceConfig.debug()
                .withTimestamp(true)
                .withStackTrace(false)
                .withLevel(DimOperationUtil.TraceConfig.LogLevel.INFO);
        
        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                expression, values, chainedConfig);
        System.out.println("链式配置结果: " + result.getResult() + 
                         ", 时间: " + result.getExecutionTimeMs() + "ms");
        System.out.println();
    }

    /**
     * 示例3：详细配置
     */
    private static void detailedConfigExample(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        System.out.println("3. 详细配置示例");
        
        // 详细配置：包含时间戳和调用栈
        DimOperationUtil.TraceConfig detailedDebug = DimOperationUtil.TraceConfig.detailed(
                DimOperationUtil.TraceConfig.LogLevel.DEBUG);
        System.out.println("详细DEBUG配置: " + detailedDebug);
        
        DimOperationUtil.TraceConfig detailedInfo = DimOperationUtil.TraceConfig.detailed(
                DimOperationUtil.TraceConfig.LogLevel.INFO);
        System.out.println("详细INFO配置: " + detailedInfo);
        
        // 自定义配置
        DimOperationUtil.TraceConfig customConfig = DimOperationUtil.TraceConfig.custom(
                DimOperationUtil.TraceConfig.LogLevel.WARN, true, false);
        System.out.println("自定义配置: " + customConfig);
        
        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                expression, values, detailedDebug);
        System.out.println("详细配置结果: " + result.getResult() + 
                         ", 时间: " + result.getExecutionTimeMs() + "ms");
        System.out.println("跟踪日志行数: " + result.getTraceLog().split("\n").length);
        System.out.println();
    }

    /**
     * 示例4：配置对比
     */
    private static void configComparisonExample(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        System.out.println("4. 配置对比示例");
        
        // 对比不同配置的性能影响
        DimOperationUtil.TraceConfig[] testConfigs = {
            DimOperationUtil.TraceConfig.disabled(),
            DimOperationUtil.TraceConfig.info(),
            DimOperationUtil.TraceConfig.info().withTimestamp(true),
            DimOperationUtil.TraceConfig.detailed(DimOperationUtil.TraceConfig.LogLevel.DEBUG)
        };
        
        String[] configNames = {
            "禁用跟踪",
            "基础INFO",
            "INFO+时间戳", 
            "详细DEBUG"
        };
        
        int iterations = 1000;
        System.out.println("执行 " + iterations + " 次性能对比:");
        
        for (int i = 0; i < testConfigs.length; i++) {
            long startTime = System.nanoTime();
            
            for (int j = 0; j < iterations; j++) {
                DimOperationUtil.executeExpressionWithTrace(expression, values, testConfigs[i]);
            }
            
            long elapsed = (System.nanoTime() - startTime) / 1_000_000;
            System.out.println(configNames[i] + ": " + elapsed + "ms");
        }
        System.out.println();
    }

    /**
     * 创建测试表达式: (a + b) * c
     */
    private static List<DimOperationDetailDTO> createTestExpression() {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("c", 3L));
        return expression;
    }

    // 辅助方法
    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }
}
