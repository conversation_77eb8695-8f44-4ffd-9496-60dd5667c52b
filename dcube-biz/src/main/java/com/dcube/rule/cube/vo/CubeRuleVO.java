package com.dcube.rule.cube.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CubeRuleVO {

    /**
     * 表id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimTableId;

    /**
     * 指标id｜二维表列编码
     */
    private String indId;

    /**
     * 引用表类型，二维｜多维
     */
    private String refTableType = "cube";

    /**
     * 引用类型，指标｜维度
     */
    private String refType = "ind";

    public CubeRuleVO(Long dimTableId, String indId) {
        this.dimTableId = dimTableId;
        this.indId = indId;
    }

    public CubeRuleVO(Long dimTableId, Long indId) {
        this.dimTableId = dimTableId;
        this.indId = String.valueOf(indId);
    }

    public CubeRuleVO(Integer dimTableId, String indId, String refTableType) {
        this.dimTableId = Long.valueOf(dimTableId);
        this.indId = indId;
        this.refTableType = refTableType;
    }

    public CubeRuleVO(Integer dimTableId, String indId, String refTableType, String refType) {
        this.dimTableId = Long.valueOf(dimTableId);
        this.indId = indId;
        this.refTableType = refTableType;
        this.refType = refType;
    }

    public CubeRuleVO(Long dimTableId, String indId, String refType) {
        this.dimTableId = dimTableId;
        this.indId = indId;
        this.refType = refType;
    }

}