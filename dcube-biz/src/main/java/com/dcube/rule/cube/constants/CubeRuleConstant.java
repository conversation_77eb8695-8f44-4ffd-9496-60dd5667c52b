package com.dcube.rule.cube.constants;

public interface CubeRuleConstant {

    String DIM_INSTANCE_MAP_CACHE = "DIM_INSTANCE_MAP_CACHE";

    String DIM_DIRECTORY_MAP_CACHE = "DIM_DIRECTORY_MAP_CACHE";

    String T_DIM_TABLE_IND_NAME_ID_MAP_CACHE = "T_DIM_TABLE_IND_NAME_ID_MAP_CACHE";

    String T_TABLE_COLUMN_NAME_CODE_MAP_CACHE = "T_TABLE_COLUMN_NAME_CODE_MAP_CACHE";

    String T_DIM_TABLE_NAME_ID_MAP_CACHE = "T_DIM_TABLE_NAME_ID_MAP_CACHE";

    String T_DIM_TABLE_DIM_NAME_ID_MAP_CACHE = "T_DIM_TABLE_DIM_NAME_ID_MAP_CACHE";

    String EXPRESS_CACHE_MAP_CACHE = "EXPRESS_CACHE_MAP_CACHE";

    String DIM_EXPRESS_CACHE_MAP_CACHE = "DIM_EXPRESS_CACHE_MAP_CACHE";

    String DIM_TABLE_DATA_MAP_CACHE = "DIM_TABLE_DATA_MAP_CACHE";

    String WRITER_FILTER_DIM_CACHE = "WRITER_FILTER_DIM_CACHE";

    String DIM_INSTANCE_DIRECTORY_MAP_CACHE = "DIM_INSTANCE_DIRECTORY_MAP_CACHE";

    String TO_PARENT_MAP = "TO_PARENT_MAP";

}
