package com.dcube.rule.cube.dto;

import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
@ToString
public class DimOperationSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "多维表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "多维表id不能为空")
    private Long dimTableId;

    @Schema(description = "多维表规则id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "多维表规则id不能为空")
    private Long dimRuleId;

    /**
     * 结果变量ID
     */
    @Schema(description = "结果变量ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结果变量ID不能为空")
    private Long resDimVariableId;

    /**
     * 变量名
     */
    @Schema(description = "变量名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "变量名不能为空")
    private String dimVariableName;

    /**
     * 运算详情
     */
    @Schema(description = "运算详情", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "运算详情不能为空")
    private List<DimOperationDetailDTO> dimOperationDetails;

}
