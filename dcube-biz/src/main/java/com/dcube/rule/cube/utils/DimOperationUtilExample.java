package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DimOperationUtil 使用示例
 * 
 * 本示例展示了如何使用优化后的 DimOperationUtil 工具类，包括：
 * 1. 基本的表达式计算
 * 2. 跟踪功能的使用
 * 3. 性能优化的效果
 * 4. 错误处理
 */
public class DimOperationUtilExample {

    public static void main(String[] args) {
        System.out.println("=== DimOperationUtil 使用示例 ===\n");
        
        // 示例1：基本计算
        basicCalculationExample();
        
        // 示例2：复杂表达式
        complexExpressionExample();
        
        // 示例3：跟踪功能
        tracingExample();
        
        // 示例4：自定义日志
        customLoggingExample();
        
        // 示例5：错误处理
        errorHandlingExample();
        
        // 示例6：性能对比
        performanceComparisonExample();
    }

    /**
     * 示例1：基本计算 - 简单的加法运算
     */
    private static void basicCalculationExample() {
        System.out.println("1. 基本计算示例：2 + 3");
        
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("2"),
                2L, new BigDecimal("3")
        );
        
        BigDecimal result = DimOperationUtil.executeExpression(expression, values);
        System.out.println("表达式: " + DimOperationUtil.printExpression(expression));
        System.out.println("结果: " + result);
        System.out.println();
    }

    /**
     * 示例2：复杂表达式 - 包含括号和多种运算
     */
    private static void complexExpressionExample() {
        System.out.println("2. 复杂表达式示例：(价格 + 税费) * 数量 / 折扣");
        
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("价格", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("税费", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("数量", 3L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.DIVIDE));
        expression.add(createVariable("折扣", 4L));
        
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("100.00"),  // 价格
                2L, new BigDecimal("8.00"),    // 税费
                3L, new BigDecimal("5"),       // 数量
                4L, new BigDecimal("0.9")      // 折扣
        );
        
        BigDecimal result = DimOperationUtil.executeExpression(expression, values);
        System.out.println("表达式: " + DimOperationUtil.printExpression(expression));
        System.out.println("结果: " + result);
        System.out.println();
    }

    /**
     * 示例3：跟踪功能 - 展示执行过程
     */
    private static void tracingExample() {
        System.out.println("3. 跟踪功能示例：(10 + 5) * 2");
        
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("x", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("y", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("z", 3L));
        
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("10"),
                2L, new BigDecimal("5"),
                3L, new BigDecimal("2")
        );
        
        // 使用简单跟踪
        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                expression, values, DimOperationUtil.TraceConfig.simple());
        
        System.out.println("表达式: " + DimOperationUtil.printExpression(expression));
        System.out.println("结果: " + result.getResult());
        System.out.println("执行时间: " + result.getExecutionTimeMs() + "ms");
        System.out.println();
    }

    /**
     * 示例4：自定义日志 - 将跟踪信息保存到自定义位置
     */
    private static void customLoggingExample() {
        System.out.println("4. 自定义日志示例：100 / 3");
        
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createVariable("dividend", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.DIVIDE));
        expression.add(createVariable("divisor", 2L));
        
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("100"),
                2L, new BigDecimal("3")
        );
        
        // 自定义日志收集器
        List<String> customLogs = new ArrayList<>();
        DimOperationUtil.TraceConfig customConfig = DimOperationUtil.TraceConfig.custom(customLogs::add);
        
        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                expression, values, customConfig);
        
        System.out.println("表达式: " + DimOperationUtil.printExpression(expression));
        System.out.println("结果: " + result.getResult());
        System.out.println("收集到的日志条数: " + customLogs.size());
        System.out.println("第一条日志: " + customLogs.get(0));
        System.out.println("最后一条日志: " + customLogs.get(customLogs.size() - 1));
        System.out.println();
    }

    /**
     * 示例5：错误处理 - 展示各种错误情况
     */
    private static void errorHandlingExample() {
        System.out.println("5. 错误处理示例");
        
        // 错误1：除零
        try {
            List<DimOperationDetailDTO> expression = new ArrayList<>();
            expression.add(createVariable("a", 1L));
            expression.add(createOperator(DimOperationFundamentalTypeEnum.DIVIDE));
            expression.add(createVariable("b", 2L));
            
            Map<Long, BigDecimal> values = Map.of(
                    1L, new BigDecimal("10"),
                    2L, new BigDecimal("0")
            );
            
            DimOperationUtil.executeExpression(expression, values);
        } catch (ArithmeticException e) {
            System.out.println("捕获除零错误: " + e.getMessage());
        }
        
        // 错误2：变量未定义
        try {
            List<DimOperationDetailDTO> expression = new ArrayList<>();
            expression.add(createVariable("a", 1L));
            expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
            expression.add(createVariable("b", 2L));
            
            Map<Long, BigDecimal> values = Map.of(
                    1L, new BigDecimal("10")
                    // 缺少变量2的值
            );
            
            DimOperationUtil.executeExpression(expression, values);
        } catch (IllegalArgumentException e) {
            System.out.println("捕获变量未定义错误: " + e.getMessage());
        }
        
        System.out.println();
    }

    /**
     * 示例6：性能对比 - 展示优化效果
     */
    private static void performanceComparisonExample() {
        System.out.println("6. 性能对比示例");
        
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("c", 3L));
        
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("2"),
                2L, new BigDecimal("3"),
                3L, new BigDecimal("4")
        );
        
        int iterations = 1000;
        
        // 测试无跟踪版本
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            DimOperationUtil.executeExpression(expression, values);
        }
        long noTraceTime = (System.nanoTime() - startTime) / 1_000_000;
        
        // 测试禁用跟踪版本
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            DimOperationUtil.executeExpressionWithTrace(expression, values, DimOperationUtil.TraceConfig.disabled());
        }
        long disabledTraceTime = (System.nanoTime() - startTime) / 1_000_000;
        
        System.out.println("执行 " + iterations + " 次性能测试:");
        System.out.println("无跟踪版本: " + noTraceTime + "ms");
        System.out.println("禁用跟踪版本: " + disabledTraceTime + "ms");
        System.out.println("性能差异: " + Math.abs(disabledTraceTime - noTraceTime) + "ms");
        System.out.println();
    }

    // 辅助方法
    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }
}
