package com.dcube.rule.cube.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.dto.DimTableDataCellDto;
import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.common.constant.enums.MoveTypeEnum;
import com.dcube.rule.cube.domain.DimRule;
import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;
import com.dcube.rule.cube.domain.DimRuleVariable;
import com.dcube.rule.cube.dto.*;
import com.dcube.rule.cube.vo.*;
import com.dcube.rule.grid.RuleGraph;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【cube_dim_rule(多维计算规则)】的数据库操作Service
 * @createDate 2024-06-22 21:21:33
 */
public interface IDimRuleService extends IService<DimRule> {

    List<DimRule> getCubeRule(Long dimTableId);

    Boolean move(Long id, MoveTypeEnum moveType);

    Boolean moveGroupUp(Long id);

    Boolean moveGroupDown(Long id);

    DimRule add(DimRuleAddDTO addDto);

    IndicatorOperationVO getIndicatorOperation(Long dimRuleId);

    DimRuleIndicatorOperation saveIndicatorOperation(IndicatorOperationSaveDTO indicatorOperationSaveDto);

    List<DimInstanceVO> queryIndicatorOperationDimInstance(IndicatorOperationDimInstanceQueryDTO indicatorOperationDimInstanceQueryDto);

    Boolean deleteById(Long id);

    Map<String, Boolean> getIndHasRule(Collection<? extends Serializable> indIds);

    Boolean saveIndicatorOperationEffectScopes(IndicatorOperationEffectScopeBatchSaveDTO indicatorOperationEffectScopeBatchSaveDto);

    RuleGraph getRuleGraph(Long dimRuleId);

    DimTableInfoVo getByIdExt(Long dimTableId);

    DimTableInfoVo getDimTableByName(String tableName);

    CubeRuleVO parse(String string);

    void execute(Integer dimTableId);

    void execute(Integer dimTableId, List<DimTableDataCellDto> parentCellList);

    void initDimValue(Long dimTableId, List<DimRuleIndicatorOperation> indicatorOperations);

    boolean validateRule(Long dimTableId, Long dimRuleId, Long indId, String express);

    List<DimRule> listByDimTableId(Long dimTableId);

    DimRule getByRuleId(Long ruleId);

    List<DimRuleVariableVO> listDimVariable(Long dimRuleId);

    /**
     * 查询维度计算的维度
     */
    List<DimRuleDimVO> getDimRuleDims(Long tableId, Long dimVariableId);

    /**
     * 保存多维变量
     */
    DimRuleVariable saveDimVariable(DimRuleVariableSaveDTO dimRuleVariableSaveDTO);

    /**
     * 删除多维变量
     */
    boolean removeDimVariable(Long id);
}
