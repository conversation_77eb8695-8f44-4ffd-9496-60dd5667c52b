package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class DimOperationUtil {

    private DimOperationUtil() {

    }

    public static String printExpression(@NotEmpty List<DimOperationDetailDTO> details) {
        StringBuilder expression = new StringBuilder();
        for (DimOperationDetailDTO dto : details) {
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();

            switch (type) {
                case VARIABLE:
                    // 变量处理：优先使用变量名，没有则用ID
                    String varName = dto.getDimVariableName();
                    if (varName == null || varName.trim().isEmpty()) {
                        varName = "变量#" + dto.getDimVariableId();
                    }
                    expression.append(varName);
                    break;

                case BRACKET:
                    // 括号处理
                    if (dto.getBracketType() == DimOperationBracketTypeEnum.LEFT) {
                        expression.append("(");
                    } else {
                        expression.append(")");
                    }
                    break;

                case FUNDAMENTAL:
                    // 四则运算符号处理
                    String symbol = getOperatorSymbol(dto.getFundamentalType());
                    expression.append(symbol);
                    break;

                default:
                    expression.append("[未知类型]");
            }
        }
        return expression.toString();
    }

    private static String getOperatorSymbol(DimOperationFundamentalTypeEnum opType) {
        if (opType == null) return "[?]";
        switch (opType) {
            case ADD:
                return "+";
            case SUBTRACT:
                return "-";
            case MULTIPLY:
                return "×";
            case DIVIDE:
                return "÷";
            default:
                return "[?]";
        }
    }

    // 状态定义
    private enum ParseState {
        START,              // 初始状态
        AFTER_OPERAND,      // 操作数后（变量或右括号）
        AFTER_OPERATOR,     // 运算符后
        AFTER_LEFT_BRACKET  // 左括号后
    }

    /**
     * @Author: yanghao
     * @Date: 2025-06-17 18:02:55
     * @Params: [details]
     * @Return: void
     * @Description: 校验运算详情
     */
    public static void validateDimOperationDetails(@NotEmpty List<DimOperationDetailDTO> details) {
        int size = details.size();
        Stack<Integer> bracketStack = new Stack<>();
        int variableCount = 0;
        ParseState state = ParseState.START;

        for (int i = 0; i < size; i++) {
            DimOperationDetailDTO current = details.get(i);
            DimOperationDetailTypeEnum type = current.getDimOperationDetailType();

            // 基本元素校验
            switch (type) {
                case VARIABLE:
                    variableCount++;
                    break;
                case FUNDAMENTAL:
                    if (current.getFundamentalType() == null) {
                        throw new IllegalArgumentException("四则运算类型不能为空（位置：" + i + ")");
                    }
                    break;
                case BRACKET:
                    if (current.getBracketType() == null) {
                        throw new IllegalArgumentException("括号类型不能为空（位置：" + i + ")");
                    }
                    break;
            }

            // 基于状态机的语法校验
            switch (state) {
                case START:
                    handleStartState(current, i);
                    break;

                case AFTER_OPERAND:
                    handleAfterOperandState(current, i);
                    break;

                case AFTER_OPERATOR:
                    handleAfterOperatorState(current, i);
                    break;

                case AFTER_LEFT_BRACKET:
                    handleAfterLeftBracketState(current, i);
                    break;
            }

            // 状态转换
            switch (type) {
                case VARIABLE:
                    state = ParseState.AFTER_OPERAND;
                    break;

                case FUNDAMENTAL:
                    state = ParseState.AFTER_OPERATOR;
                    break;

                case BRACKET:
                    if (isLeftBracket(current)) {
                        state = ParseState.AFTER_LEFT_BRACKET;
                        bracketStack.push(i);
                    } else {
                        state = ParseState.AFTER_OPERAND;
                        handleRightBracket(bracketStack, i);
                    }
                    break;
            }
        }

        // 最终状态校验
        if (state != ParseState.AFTER_OPERAND) {
            throw new IllegalArgumentException("表达式不能以运算符或左括号结尾");
        }

        if (!bracketStack.isEmpty()) {
            throw new IllegalArgumentException("存在未闭合的左括号（位置：" + bracketStack.pop() + ")");
        }

        if (variableCount == 0) {
            throw new IllegalArgumentException("表达式中至少需要一个变量");
        }
    }

    /**
     * 状态处理函数
     */
    private static void handleStartState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("表达式不能以运算符开头（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("表达式不能以右括号开头（位置：" + position + ")");
        }
    }

    private static void handleAfterOperandState(DimOperationDetailDTO current, int position) {
        if (isLeftBracket(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟左括号（位置：" + position + ")");
        }
        if (isVariable(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟变量（位置：" + position + ")");
        }
    }

    private static void handleAfterOperatorState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("不能连续出现运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("运算符后不能直接跟右括号（位置：" + position + ")");
        }
    }

    private static void handleAfterLeftBracketState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("左括号后不能直接跟运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("括号内不能为空（位置：" + (position - 1) + "和" + position + ")");
        }
    }

    /**
     * 括号处理函数
     */
    private static void handleRightBracket(Stack<Integer> bracketStack, int position) {
        if (bracketStack.isEmpty()) {
            throw new IllegalArgumentException("右括号没有匹配的左括号（位置：" + position + ")");
        }
        bracketStack.pop();
    }

    /**
     * 辅助函数
     */
    private static boolean isLeftBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.LEFT;
    }

    private static boolean isRightBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.RIGHT;
    }

    private static boolean isOperator(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.FUNDAMENTAL;
    }

    private static boolean isVariable(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.VARIABLE;
    }

    public static double executeExpression(@NotEmpty List<DimOperationDetailDTO> details,
                                           @NotNull Map<Long, Double> variableValueMap) {
        // 中缀表达式转后缀表达式（逆波兰表达式）
        List<Object> postfix = toPostfix(details, variableValueMap);

        // 执行后缀表达式
        return evaluatePostfix(postfix);
    }

    private static double evaluatePostfix(List<Object> postfix) {
        Deque<Double> stack = new ArrayDeque<>();

        for (Object token : postfix) {
            if (token instanceof Double) {
                stack.push((Double) token);
            } else if (token instanceof String) {
                String op = (String) token;
                double b = stack.pop();
                double a = stack.pop();
                double result;
                switch (op) {
                    case "+":
                        result = a + b;
                        break;
                    case "-":
                        result = a - b;
                        break;
                    case "×":
                        result = a * b;
                        break;
                    case "÷":
                        if (b == 0) throw new ArithmeticException("除以0错误");
                        result = a / b;
                        break;
                    default:
                        throw new IllegalArgumentException("未知运算符: " + op);
                }
                stack.push(result);
            }
        }

        if (stack.size() != 1) {
            throw new IllegalStateException("表达式计算错误，结果栈中不止一个元素");
        }

        return stack.pop();
    }

    private static List<Object> toPostfix(List<DimOperationDetailDTO> details, Map<Long, Double> varMap) {
        List<Object> output = new ArrayList<>();
        Deque<String> operators = new ArrayDeque<>();

        for (DimOperationDetailDTO dto : details) {
            switch (dto.getDimOperationDetailType()) {
                case VARIABLE:
                    Long varId = dto.getDimVariableId();
                    if (!varMap.containsKey(varId)) {
                        throw new IllegalArgumentException("变量ID未提供数值: " + varId);
                    }
                    output.add(varMap.get(varId));
                    break;

                case FUNDAMENTAL:
                    String op = getOperatorSymbol(dto.getFundamentalType());
                    while (!operators.isEmpty() && precedence(op) <= precedence(operators.peek())) {
                        output.add(operators.pop());
                    }
                    operators.push(op);
                    break;

                case BRACKET:
                    if (dto.getBracketType() == DimOperationBracketTypeEnum.LEFT) {
                        operators.push("(");
                    } else {
                        while (!operators.isEmpty() && !operators.peek().equals("(")) {
                            output.add(operators.pop());
                        }
                        if (operators.isEmpty() || !operators.peek().equals("(")) {
                            throw new IllegalArgumentException("括号不匹配");
                        }
                        operators.pop(); // 弹出左括号
                    }
                    break;
            }
        }

        while (!operators.isEmpty()) {
            String op = operators.pop();
            if (op.equals("(")) {
                throw new IllegalArgumentException("括号不匹配");
            }
            output.add(op);
        }

        return output;
    }



    private static int precedence(String op) {
        switch (op) {
            case "+":
            case "-":
                return 1;
            case "×":
            case "÷":
                return 2;
            default:
                return 0;
        }
    }


    public static void main(String[] args) {
        // 创建表达式: (价格 + 运费) × 数量
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("价格", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("运费", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("数量", 3L));
        Map<Long, Double> values = Map.of(
                1L, 2.0,
                2L, 3.0,
                3L, 4.0
        );

        double result = executeExpression(expression, values);
        System.out.println("计算结果: " + result); // 输出 20.0
    }

    // 辅助创建方法
    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }

}