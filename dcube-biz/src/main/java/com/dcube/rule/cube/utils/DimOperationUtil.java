package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DimOperationUtil {

    private DimOperationUtil() {
    }

    // 跟踪配置
    public static class TraceConfig {
        private final boolean enabled;
        private final Consumer<String> logger;
        private final boolean includeTimestamp;
        private final boolean includeStackTrace;
        private final LogLevel logLevel;

        public enum LogLevel {
            TRACE(log::trace),
            DEBUG(log::debug),
            INFO(log::info),
            WARN(log::warn),
            ERROR(log::error);

            private final Consumer<String> logger;

            LogLevel(Consumer<String> logger) {
                this.logger = logger;
            }

            public Consumer<String> getLogger() {
                return logger;
            }
        }

        private TraceConfig(boolean enabled, LogLevel logLevel, boolean includeTimestamp, boolean includeStackTrace) {
            this.enabled = enabled;
            this.logLevel = logLevel != null ? logLevel : LogLevel.DEBUG;
            this.logger = this.logLevel.getLogger();
            this.includeTimestamp = includeTimestamp;
            this.includeStackTrace = includeStackTrace;
        }

        // 静态工厂方法 - 基础配置
        public static TraceConfig disabled() {
            return new TraceConfig(false, LogLevel.DEBUG, false, false);
        }

        public static TraceConfig of(LogLevel level) {
            return new TraceConfig(true, level, false, false);
        }

        // 便捷方法 - 各个日志级别
        public static TraceConfig trace() { return of(LogLevel.TRACE); }
        public static TraceConfig debug() { return of(LogLevel.DEBUG); }
        public static TraceConfig info() { return of(LogLevel.INFO); }
        public static TraceConfig warn() { return of(LogLevel.WARN); }
        public static TraceConfig error() { return of(LogLevel.ERROR); }

        // 详细配置
        public static TraceConfig detailed(LogLevel level) {
            return new TraceConfig(true, level, true, true);
        }

        public static TraceConfig custom(LogLevel level, boolean includeTimestamp, boolean includeStackTrace) {
            return new TraceConfig(true, level, includeTimestamp, includeStackTrace);
        }

        // 链式配置方法
        public TraceConfig withTimestamp(boolean includeTimestamp) {
            return new TraceConfig(this.enabled, this.logLevel, includeTimestamp, this.includeStackTrace);
        }

        public TraceConfig withStackTrace(boolean includeStackTrace) {
            return new TraceConfig(this.enabled, this.logLevel, this.includeTimestamp, includeStackTrace);
        }

        public TraceConfig withLevel(LogLevel level) {
            return new TraceConfig(this.enabled, level, this.includeTimestamp, this.includeStackTrace);
        }

        // Getter 方法
        public boolean isEnabled() { return enabled; }
        public Consumer<String> getLogger() { return logger; }
        public boolean isIncludeTimestamp() { return includeTimestamp; }
        public boolean isIncludeStackTrace() { return includeStackTrace; }
        public LogLevel getLogLevel() { return logLevel; }

        @Override
        public String toString() {
            return String.format("TraceConfig{enabled=%s, level=%s, timestamp=%s, stackTrace=%s}",
                    enabled, logLevel, includeTimestamp, includeStackTrace);
        }
    }

    // 执行上下文
    public static class ExecutionContext {
        private final TraceConfig traceConfig;
        private final long startTime;
        private final StringBuilder traceLog;
        private int step;

        // 内存保护：限制日志大小
        private static final int MAX_TRACE_LOG_SIZE = 10 * 1024; // 10KB
        private static final int MAX_STEPS = 1000; // 最大步骤数
        private boolean logTruncated = false;

        public ExecutionContext(TraceConfig traceConfig) {
            this.traceConfig = traceConfig;
            this.startTime = System.nanoTime();
            // 预估初始容量，避免频繁扩容
            this.traceLog = new StringBuilder(512);
            this.step = 0;
        }

        public void trace(String message) {
            traceWithLevel(message, traceConfig.getLogLevel());
        }

        public void traceWithLevel(String message, TraceConfig.LogLevel level) {
            if (!traceConfig.isEnabled()) return;

            // 内存保护：限制步骤数
            if (step >= MAX_STEPS) {
                if (!logTruncated) {
                    level.getLogger().accept("[DimOperation] 警告: 跟踪步骤数超过限制(" + MAX_STEPS + ")，后续日志将被忽略");
                    logTruncated = true;
                }
                return;
            }

            step++;
            String logMessage = formatLogMessage(message);

            // 内存保护：限制日志大小
            if (traceLog.length() + logMessage.length() + 1 > MAX_TRACE_LOG_SIZE) {
                if (!logTruncated) {
                    String truncateMsg = "\n[DimOperation] 警告: 跟踪日志大小超过限制(" + MAX_TRACE_LOG_SIZE + "字节)，后续日志将被截断";
                    traceLog.append(truncateMsg);
                    level.getLogger().accept("[DimOperation] 警告: 跟踪日志大小超过限制，后续日志将被截断");
                    logTruncated = true;
                }
                // 只输出到日志器，不再添加到 traceLog
                level.getLogger().accept(logMessage);
                return;
            }

            traceLog.append(logMessage).append("\n");
            level.getLogger().accept(logMessage);
        }

        private String formatLogMessage(String message) {
            StringBuilder sb = new StringBuilder();

            // 添加 DimOperation 前缀标识
            sb.append("[DimOperation] ");

            if (traceConfig.isIncludeTimestamp()) {
                long elapsed = (System.nanoTime() - startTime) / 1_000_000; // ms
                sb.append("[").append(elapsed).append("ms] ");
            }

            sb.append("Step ").append(step).append(": ").append(message);

            if (traceConfig.isIncludeStackTrace()) {
                StackTraceElement caller = Thread.currentThread().getStackTrace()[4]; // 调整调用栈深度
                sb.append(" (").append(caller.getMethodName()).append(":").append(caller.getLineNumber()).append(")");
            }

            return sb.toString();
        }

        public String getFullTrace() {
            return traceLog.toString();
        }

        public long getElapsedTimeMs() {
            return (System.nanoTime() - startTime) / 1_000_000;
        }

        /**
         * 清理资源，防止内存泄露
         */
        public void cleanup() {
            // 清空 StringBuilder 内容，释放内存
            if (traceLog.length() > 0) {
                traceLog.setLength(0);
                traceLog.trimToSize();
            }
        }

        /**
         * 获取当前日志大小（字节）
         */
        public int getCurrentLogSize() {
            return traceLog.length();
        }

        /**
         * 是否已截断
         */
        public boolean isLogTruncated() {
            return logTruncated;
        }
    }

    // 性能优化：缓存常用的符号映射
    private static final Map<DimOperationFundamentalTypeEnum, String> SYMBOL_CACHE = new ConcurrentHashMap<>();

    // 性能优化：预编译的常量
    private static final String LEFT_BRACKET = "(";
    private static final String RIGHT_BRACKET = ")";
    private static final String UNKNOWN_TYPE = "[未知类型]";
    private static final String UNKNOWN_SYMBOL = "[?]";
    private static final String VARIABLE_PREFIX = "变量#";

    // 内存监控相关
    private static volatile long lastMaintenanceTime = System.currentTimeMillis();
    private static volatile long executionCounter = 0;
    private static final long MAINTENANCE_INTERVAL_MS = 5 * 60 * 1000; // 5分钟
    private static final long MAINTENANCE_EXECUTION_THRESHOLD = 1000; // 1000次执行后检查

    // Caffeine 缓存配置 - 内存优化版本
    private static final Cache<String, List<Object>> POSTFIX_CACHE = Caffeine.newBuilder()
            .maximumSize(500)  // 减少缓存大小
            .expireAfterWrite(Duration.ofMinutes(30))  // 缩短过期时间
            .expireAfterAccess(Duration.ofMinutes(10)) // 添加访问过期
            .removalListener((key, value, cause) -> {
                // 缓存移除时的清理逻辑
                if (value instanceof List) {
                    ((List<?>) value).clear();
                }
            })
            .recordStats()
            .build();

    private static final Cache<String, List<DimOperationDetailDTO>> OPTIMIZED_EXPRESSION_CACHE = Caffeine.newBuilder()
            .maximumSize(200)  // 减少缓存大小
            .expireAfterWrite(Duration.ofMinutes(20))  // 缩短过期时间
            .expireAfterAccess(Duration.ofMinutes(5))  // 添加访问过期
            .removalListener((key, value, cause) -> {
                // 缓存移除时的清理逻辑
                if (value instanceof List) {
                    ((List<?>) value).clear();
                }
            })
            .recordStats()
            .build();

    // 表达式优化配置
    public static class OptimizationConfig {
        private final boolean enabled;
        private final boolean constantFolding;
        private final boolean redundantBracketRemoval;
        private final boolean algebraicSimplification;

        public OptimizationConfig(boolean enabled, boolean constantFolding,
                                 boolean redundantBracketRemoval, boolean algebraicSimplification) {
            this.enabled = enabled;
            this.constantFolding = constantFolding;
            this.redundantBracketRemoval = redundantBracketRemoval;
            this.algebraicSimplification = algebraicSimplification;
        }

        public static OptimizationConfig disabled() {
            return new OptimizationConfig(false, false, false, false);
        }

        public static OptimizationConfig basic() {
            return new OptimizationConfig(true, true, true, false);
        }

        public static OptimizationConfig full() {
            return new OptimizationConfig(true, true, true, true);
        }

        // Getters
        public boolean isEnabled() { return enabled; }
        public boolean isConstantFolding() { return constantFolding; }
        public boolean isRedundantBracketRemoval() { return redundantBracketRemoval; }
        public boolean isAlgebraicSimplification() { return algebraicSimplification; }
    }

    /**
     * 性能优化版本的表达式打印
     */
    public static String printExpression(@NotEmpty List<DimOperationDetailDTO> details) {
        if (details.isEmpty()) {
            return "";
        }

        // 预估容量以减少StringBuilder扩容
        StringBuilder expression = new StringBuilder(details.size() * 8);

        for (DimOperationDetailDTO dto : details) {
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();

            switch (type) {
                case VARIABLE:
                    // 变量处理：优先使用变量名，没有则用ID
                    String varName = dto.getDimVariableName();
                    if (varName == null || varName.trim().isEmpty()) {
                        expression.append(VARIABLE_PREFIX).append(dto.getDimVariableId());
                    } else {
                        expression.append(varName);
                    }
                    break;

                case BRACKET:
                    // 括号处理 - 使用预编译常量
                    expression.append(dto.getBracketType() == DimOperationBracketTypeEnum.LEFT ?
                                    LEFT_BRACKET : RIGHT_BRACKET);
                    break;

                case FUNDAMENTAL:
                    // 四则运算符号处理 - 使用缓存
                    expression.append(getOperatorSymbolCached(dto.getFundamentalType()));
                    break;

                default:
                    expression.append(UNKNOWN_TYPE);
            }
        }
        return expression.toString();
    }

    /**
     * 性能优化：带缓存的符号获取
     */
    private static String getOperatorSymbolCached(DimOperationFundamentalTypeEnum opType) {
        if (opType == null) return UNKNOWN_SYMBOL;
        return SYMBOL_CACHE.computeIfAbsent(opType, DimOperationFundamentalTypeEnum::getSymbol);
    }

    /**
     * 兼容性方法
     */
    private static String getOperatorSymbol(DimOperationFundamentalTypeEnum opType) {
        return getOperatorSymbolCached(opType);
    }

    // 状态定义
    private enum ParseState {
        START,              // 初始状态
        AFTER_OPERAND,      // 操作数后（变量或右括号）
        AFTER_OPERATOR,     // 运算符后
        AFTER_LEFT_BRACKET  // 左括号后
    }

    /**
     * 优化版本的表达式校验 - 使用 Deque 替代 Stack
     * @Author: yanghao
     * @Date: 2025-06-17 18:02:55
     * @Params: [details]
     * @Return: void
     * @Description: 校验运算详情
     */
    public static void validateDimOperationDetails(@NotEmpty List<DimOperationDetailDTO> details) {
        if (details.isEmpty()) {
            throw new IllegalArgumentException("表达式不能为空");
        }

        int size = details.size();
        // 性能优化：使用 ArrayDeque 替代 Stack
        Deque<Integer> bracketStack = new ArrayDeque<>(size / 4 + 1);
        int variableCount = 0;
        ParseState state = ParseState.START;

        for (int i = 0; i < size; i++) {
            DimOperationDetailDTO current = details.get(i);
            DimOperationDetailTypeEnum type = current.getDimOperationDetailType();

            if (type == null) {
                throw new IllegalArgumentException("运算详情类型不能为空（位置：" + i + ")");
            }

            // 基本元素校验
            switch (type) {
                case VARIABLE:
                    variableCount++;
                    if (current.getDimVariableId() == null) {
                        throw new IllegalArgumentException("变量ID不能为空（位置：" + i + ")");
                    }
                    break;
                case FUNDAMENTAL:
                    if (current.getFundamentalType() == null) {
                        throw new IllegalArgumentException("四则运算类型不能为空（位置：" + i + ")");
                    }
                    break;
                case BRACKET:
                    if (current.getBracketType() == null) {
                        throw new IllegalArgumentException("括号类型不能为空（位置：" + i + ")");
                    }
                    break;
                default:
                    throw new IllegalArgumentException("未知的运算详情类型：" + type + "（位置：" + i + ")");
            }

            // 基于状态机的语法校验
            switch (state) {
                case START:
                    handleStartState(current, i);
                    break;

                case AFTER_OPERAND:
                    handleAfterOperandState(current, i);
                    break;

                case AFTER_OPERATOR:
                    handleAfterOperatorState(current, i);
                    break;

                case AFTER_LEFT_BRACKET:
                    handleAfterLeftBracketState(current, i);
                    break;
            }

            // 状态转换
            switch (type) {
                case VARIABLE:
                    state = ParseState.AFTER_OPERAND;
                    break;

                case FUNDAMENTAL:
                    state = ParseState.AFTER_OPERATOR;
                    break;

                case BRACKET:
                    if (isLeftBracket(current)) {
                        state = ParseState.AFTER_LEFT_BRACKET;
                        bracketStack.push(i);
                    } else {
                        state = ParseState.AFTER_OPERAND;
                        handleRightBracketOptimized(bracketStack, i);
                    }
                    break;
            }
        }

        // 最终状态校验
        if (state != ParseState.AFTER_OPERAND) {
            throw new IllegalArgumentException("表达式不能以运算符或左括号结尾");
        }

        if (!bracketStack.isEmpty()) {
            throw new IllegalArgumentException("存在未闭合的左括号（位置：" + bracketStack.pop() + ")");
        }

        if (variableCount == 0) {
            throw new IllegalArgumentException("表达式中至少需要一个变量");
        }
    }

    /**
     * 状态处理函数
     */
    private static void handleStartState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("表达式不能以运算符开头（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("表达式不能以右括号开头（位置：" + position + ")");
        }
    }

    private static void handleAfterOperandState(DimOperationDetailDTO current, int position) {
        if (isLeftBracket(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟左括号（位置：" + position + ")");
        }
        if (isVariable(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟变量（位置：" + position + ")");
        }
    }

    private static void handleAfterOperatorState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("不能连续出现运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("运算符后不能直接跟右括号（位置：" + position + ")");
        }
    }

    private static void handleAfterLeftBracketState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("左括号后不能直接跟运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("括号内不能为空（位置：" + (position - 1) + "和" + position + ")");
        }
    }

    /**
     * 优化版本的括号处理函数 - 使用 Deque
     */
    private static void handleRightBracketOptimized(Deque<Integer> bracketStack, int position) {
        if (bracketStack.isEmpty()) {
            throw new IllegalArgumentException("右括号没有匹配的左括号（位置：" + position + ")");
        }
        bracketStack.pop();
    }

    /**
     * 兼容性方法 - 保持向后兼容
     */
    @Deprecated
    private static void handleRightBracket(Stack<Integer> bracketStack, int position) {
        handleRightBracketOptimized(new ArrayDeque<>(bracketStack), position);
    }

    /**
     * 辅助函数
     */
    private static boolean isLeftBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.LEFT;
    }

    private static boolean isRightBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.RIGHT;
    }

    private static boolean isOperator(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.FUNDAMENTAL;
    }

    private static boolean isVariable(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.VARIABLE;
    }

    /**
     * 执行表达式 - 无跟踪版本（向后兼容）
     */
    public static BigDecimal executeExpression(@NotEmpty List<DimOperationDetailDTO> details,
                                               @NotNull Map<Long, BigDecimal> variableValueMap) {
        return executeExpressionWithOptimization(details, variableValueMap,
                TraceConfig.disabled(), OptimizationConfig.basic()).getResult();
    }

    /**
     * 执行表达式 - 带跟踪版本
     */
    public static ExecutionResult executeExpressionWithTrace(@NotEmpty List<DimOperationDetailDTO> details,
                                                             @NotNull Map<Long, BigDecimal> variableValueMap,
                                                             TraceConfig traceConfig) {
        return executeExpressionWithOptimization(details, variableValueMap, traceConfig, OptimizationConfig.basic());
    }

    /**
     * 执行表达式 - 完整版本（带跟踪和优化）
     */
    public static ExecutionResult executeExpressionWithOptimization(@NotEmpty List<DimOperationDetailDTO> details,
                                                                    @NotNull Map<Long, BigDecimal> variableValueMap,
                                                                    TraceConfig traceConfig,
                                                                    OptimizationConfig optimizationConfig) {
        // 自动内存监控和维护
        checkAndPerformAutoMaintenance();

        ExecutionContext context = new ExecutionContext(traceConfig);

        try {
            context.trace("开始执行表达式，包含 " + details.size() + " 个元素");

            // 输入验证
            if (details.isEmpty()) {
                throw new IllegalArgumentException("表达式不能为空");
            }

            // 表达式优化
            List<DimOperationDetailDTO> optimizedDetails = details;
            if (optimizationConfig.isEnabled()) {
                context.trace("开始表达式优化");
                optimizedDetails = optimizeExpression(details, optimizationConfig, context);
                context.trace("表达式优化完成，优化后包含 " + optimizedDetails.size() + " 个元素");
            }

            // 生成缓存键
            String cacheKey = generateCacheKey(optimizedDetails, variableValueMap.keySet());

            context.trace("开始转换为后缀表达式");

            // 内存优化：避免在 lambda 中持有大对象引用
            final List<DimOperationDetailDTO> finalOptimizedDetails = optimizedDetails;
            List<Object> postfix = POSTFIX_CACHE.get(cacheKey, key -> {
                // 创建新的轻量级上下文，避免持有原始 context 引用
                ExecutionContext lightContext = new ExecutionContext(TraceConfig.disabled());
                try {
                    return toPostfixWithTrace(finalOptimizedDetails, variableValueMap, lightContext);
                } finally {
                    // 立即清理轻量级上下文
                    lightContext.cleanup();
                }
            });

            if (postfix != null && !postfix.isEmpty()) {
                context.trace("使用缓存的后缀表达式，包含 " + postfix.size() + " 个元素");
            } else {
                context.trace("后缀表达式转换完成，包含 " + postfix.size() + " 个元素");
            }

            context.trace("开始计算后缀表达式");

            // 执行后缀表达式
            BigDecimal result = evaluatePostfixWithTrace(postfix, context);

            context.trace("表达式计算完成，结果: " + result);

            // 收集内存使用信息
            long memoryUsed = context.getCurrentLogSize();
            boolean truncated = context.isLogTruncated();

            ExecutionResult executionResult = new ExecutionResult(
                result,
                context.getFullTrace(),
                context.getElapsedTimeMs(),
                truncated,
                memoryUsed
            );

            // 内存清理：执行完成后清理上下文
            context.cleanup();

            return executionResult;

        } catch (Exception e) {
            context.trace("执行过程中发生异常: " + e.getMessage());
            // 异常情况下也要清理资源
            context.cleanup();
            throw e;
        }
    }

    /**
     * 执行结果封装
     */
    public static class ExecutionResult {
        private final BigDecimal result;
        private final String traceLog;
        private final long executionTimeMs;
        private final int traceLogSize;
        private final boolean logTruncated;
        private final long memoryUsedBytes;

        public ExecutionResult(BigDecimal result, String traceLog, long executionTimeMs) {
            this(result, traceLog, executionTimeMs, false, 0);
        }

        public ExecutionResult(BigDecimal result, String traceLog, long executionTimeMs,
                              boolean logTruncated, long memoryUsedBytes) {
            this.result = result;
            this.traceLog = traceLog;
            this.executionTimeMs = executionTimeMs;
            this.traceLogSize = traceLog != null ? traceLog.length() : 0;
            this.logTruncated = logTruncated;
            this.memoryUsedBytes = memoryUsedBytes;
        }

        public BigDecimal getResult() { return result; }
        public String getTraceLog() { return traceLog; }
        public long getExecutionTimeMs() { return executionTimeMs; }
        public int getTraceLogSize() { return traceLogSize; }
        public boolean isLogTruncated() { return logTruncated; }
        public long getMemoryUsedBytes() { return memoryUsedBytes; }

        /**
         * 获取内存使用情况（KB）
         */
        public double getMemoryUsedKB() {
            return memoryUsedBytes / 1024.0;
        }

        /**
         * 获取执行摘要
         */
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append("结果: ").append(result);
            sb.append(", 执行时间: ").append(executionTimeMs).append("ms");
            sb.append(", 日志大小: ").append(traceLogSize).append("字符");
            if (logTruncated) {
                sb.append(" (已截断)");
            }
            if (memoryUsedBytes > 0) {
                sb.append(", 内存使用: ").append(String.format("%.1f", getMemoryUsedKB())).append("KB");
            }
            return sb.toString();
        }

        @Override
        public String toString() {
            return getSummary();
        }
    }

    /**
     * 性能优化版本的后缀表达式计算
     */
    private static BigDecimal evaluatePostfix(List<Object> postfix) {
        return evaluatePostfixWithTrace(postfix, new ExecutionContext(TraceConfig.disabled()));
    }

    /**
     * 带跟踪的后缀表达式计算
     */
    private static BigDecimal evaluatePostfixWithTrace(List<Object> postfix, ExecutionContext context) {
        // 性能优化：预估栈大小
        Deque<BigDecimal> stack = new ArrayDeque<>(postfix.size() / 2 + 1);

        int tokenIndex = 0;
        for (Object token : postfix) {
            tokenIndex++;

            if (token instanceof BigDecimal) {
                BigDecimal value = (BigDecimal) token;
                stack.push(value);
                context.trace("压入操作数: " + value + " (栈大小: " + stack.size() + ")");

            } else if (token instanceof DimOperationFundamentalTypeEnum) {
                if (stack.size() < 2) {
                    throw new IllegalStateException("栈中操作数不足，位置: " + tokenIndex);
                }

                DimOperationFundamentalTypeEnum op = (DimOperationFundamentalTypeEnum) token;
                BigDecimal b = stack.pop();
                BigDecimal a = stack.pop();

                context.trace("执行运算: " + a + " " + op.getSymbol() + " " + b);

                BigDecimal result = op.apply(a, b);
                stack.push(result);

                context.trace("运算结果: " + result + " (栈大小: " + stack.size() + ")");
            } else {
                throw new IllegalStateException("未知的token类型: " + token.getClass().getSimpleName() + ", 位置: " + tokenIndex);
            }
        }

        if (stack.size() != 1) {
            throw new IllegalStateException("表达式计算错误，结果栈中有 " + stack.size() + " 个元素，期望1个");
        }

        return stack.pop();
    }

    /**
     * 性能优化版本的中缀转后缀
     */
    private static List<Object> toPostfix(List<DimOperationDetailDTO> details, Map<Long, BigDecimal> varMap) {
        return toPostfixWithTrace(details, varMap, new ExecutionContext(TraceConfig.disabled()));
    }

    /**
     * 带跟踪的中缀转后缀表达式
     */
    private static List<Object> toPostfixWithTrace(List<DimOperationDetailDTO> details,
                                                   Map<Long, BigDecimal> varMap,
                                                   ExecutionContext context) {
        // 性能优化：预估容量
        List<Object> output = new ArrayList<>(details.size());
        Deque<Object> operators = new ArrayDeque<>(details.size() / 2);

        int position = 0;
        for (DimOperationDetailDTO dto : details) {
            position++;
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();

            switch (type) {
                case VARIABLE:
                    Long varId = dto.getDimVariableId();
                    if (!varMap.containsKey(varId)) {
                        throw new IllegalArgumentException("变量ID未提供数值: " + varId + ", 位置: " + position);
                    }
                    BigDecimal value = varMap.get(varId);
                    output.add(value);
                    context.trace("处理变量 ID=" + varId + ", 值=" + value + " (位置: " + position + ")");
                    break;

                case FUNDAMENTAL:
                    DimOperationFundamentalTypeEnum op = dto.getFundamentalType();
                    if (op == null) {
                        throw new IllegalArgumentException("运算符不能为空, 位置: " + position);
                    }

                    // 性能优化：减少类型检查
                    while (!operators.isEmpty()) {
                        Object topOp = operators.peek();
                        if (topOp instanceof DimOperationFundamentalTypeEnum) {
                            DimOperationFundamentalTypeEnum topOpEnum = (DimOperationFundamentalTypeEnum) topOp;
                            if (op.getPrecedence() <= topOpEnum.getPrecedence()) {
                                output.add(operators.pop());
                                context.trace("弹出高优先级运算符: " + topOpEnum.getSymbol());
                            } else {
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                    operators.push(op);
                    context.trace("压入运算符: " + op.getSymbol() + " (位置: " + position + ")");
                    break;

                case BRACKET:
                    DimOperationBracketTypeEnum bracketType = dto.getBracketType();
                    if (bracketType == null) {
                        throw new IllegalArgumentException("括号类型不能为空, 位置: " + position);
                    }

                    if (bracketType == DimOperationBracketTypeEnum.LEFT) {
                        operators.push(LEFT_BRACKET);
                        context.trace("压入左括号 (位置: " + position + ")");
                    } else {
                        boolean foundLeftBracket = false;
                        while (!operators.isEmpty()) {
                            Object topOperator = operators.peek();
                            if (LEFT_BRACKET.equals(topOperator)) {
                                operators.pop(); // 弹出左括号
                                foundLeftBracket = true;
                                break;
                            } else {
                                output.add(operators.pop());
                                context.trace("弹出括号内运算符: " + topOperator);
                            }
                        }
                        if (!foundLeftBracket) {
                            throw new IllegalArgumentException("右括号没有匹配的左括号, 位置: " + position);
                        }
                        context.trace("处理右括号 (位置: " + position + ")");
                    }
                    break;

                default:
                    throw new IllegalArgumentException("未知的操作类型: " + type + ", 位置: " + position);
            }
        }

        // 弹出剩余的运算符
        while (!operators.isEmpty()) {
            Object op = operators.pop();
            if (LEFT_BRACKET.equals(op)) {
                throw new IllegalArgumentException("存在未匹配的左括号");
            }
            output.add(op);
            context.trace("弹出剩余运算符: " + op);
        }

        context.trace("中缀转后缀完成，输出: " + output);
        return output;
    }






    public static void main(String[] args) {
        // 创建表达式: (价格 + 运费) × 数量
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("价格", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("运费", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("数量", 3L));

        validateDimOperationDetails(expression);

        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("2.0"),
                2L, new BigDecimal("3.0"),
                3L, new BigDecimal("4.0")
        );

        // 测试无跟踪版本
        System.out.println("=== 无跟踪版本 ===");
        BigDecimal result = executeExpression(expression, values);
        System.out.println("计算结果: " + result);

        // 测试 DEBUG 级别跟踪版本
        System.out.println("\n=== DEBUG 级别跟踪版本 ===");
        ExecutionResult debugResult = executeExpressionWithTrace(expression, values, TraceConfig.debug());
        System.out.println("计算结果: " + debugResult.getResult());
        System.out.println("执行时间: " + debugResult.getExecutionTimeMs() + "ms");

        // 测试 INFO 级别跟踪版本
        System.out.println("\n=== INFO 级别跟踪版本 ===");
        ExecutionResult infoResult = executeExpressionWithTrace(expression, values, TraceConfig.info());
        System.out.println("计算结果: " + infoResult.getResult());
        System.out.println("执行时间: " + infoResult.getExecutionTimeMs() + "ms");

        // 测试详细跟踪版本
        System.out.println("\n=== 详细跟踪版本 ===");
        ExecutionResult detailedResult = executeExpressionWithTrace(expression, values,
                TraceConfig.detailed(TraceConfig.LogLevel.DEBUG));
        System.out.println("计算结果: " + detailedResult.getResult());
        System.out.println("执行时间: " + detailedResult.getExecutionTimeMs() + "ms");

        // 测试链式配置
        System.out.println("\n=== 链式配置示例 ===");
        ExecutionResult chainResult = executeExpressionWithTrace(expression, values,
                TraceConfig.info().withTimestamp(true).withStackTrace(false));
        System.out.println("链式配置结果: " + chainResult.getResult());
        System.out.println("执行时间: " + chainResult.getExecutionTimeMs() + "ms");

        // 测试优化功能
        System.out.println("\n=== 优化功能测试 ===");
        testOptimization(expression, values);

        // 性能测试
        System.out.println("\n=== 性能测试 ===");
        performanceTest(expression, values);

        // 缓存统计
        System.out.println("\n=== 缓存统计 ===");
        System.out.println(getCacheStats());
    }

    /**
     * 表达式优化
     */
    private static List<DimOperationDetailDTO> optimizeExpression(List<DimOperationDetailDTO> details,
                                                                  OptimizationConfig config,
                                                                  ExecutionContext context) {
        String cacheKey = generateOptimizationCacheKey(details, config);

        // 尝试从缓存获取优化后的表达式
        List<DimOperationDetailDTO> cached = OPTIMIZED_EXPRESSION_CACHE.getIfPresent(cacheKey);
        if (cached != null) {
            context.trace("使用缓存的优化表达式");
            return cached;
        }

        List<DimOperationDetailDTO> optimized = new ArrayList<>(details);

        // 1. 移除冗余括号
        if (config.isRedundantBracketRemoval()) {
            context.trace("执行冗余括号移除优化");
            optimized = removeRedundantBrackets(optimized);
        }

        // 2. 常量折叠（如果有常量表达式）
        if (config.isConstantFolding()) {
            context.trace("执行常量折叠优化");
            optimized = foldConstants(optimized);
        }

        // 3. 代数简化
        if (config.isAlgebraicSimplification()) {
            context.trace("执行代数简化优化");
            optimized = simplifyAlgebraically(optimized);
        }

        // 缓存优化结果
        OPTIMIZED_EXPRESSION_CACHE.put(cacheKey, optimized);

        return optimized;
    }

    /**
     * 移除冗余括号
     */
    private static List<DimOperationDetailDTO> removeRedundantBrackets(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> result = new ArrayList<>();
        Deque<Integer> bracketStack = new ArrayDeque<>();
        Map<Integer, Integer> bracketPairs = new HashMap<>();

        // 第一遍：找出所有括号对
        for (int i = 0; i < details.size(); i++) {
            DimOperationDetailDTO dto = details.get(i);
            if (isLeftBracket(dto)) {
                bracketStack.push(i);
            } else if (isRightBracket(dto)) {
                if (!bracketStack.isEmpty()) {
                    int leftPos = bracketStack.pop();
                    bracketPairs.put(leftPos, i);
                }
            }
        }

        // 第二遍：判断哪些括号是冗余的
        Set<Integer> redundantBrackets = new HashSet<>();
        for (Map.Entry<Integer, Integer> pair : bracketPairs.entrySet()) {
            int leftPos = pair.getKey();
            int rightPos = pair.getValue();

            // 简单的冗余检查：如果括号内只有一个变量，则认为是冗余的
            if (rightPos - leftPos == 2) {
                DimOperationDetailDTO middle = details.get(leftPos + 1);
                if (isVariable(middle)) {
                    redundantBrackets.add(leftPos);
                    redundantBrackets.add(rightPos);
                }
            }
        }

        // 第三遍：构建结果，跳过冗余括号
        for (int i = 0; i < details.size(); i++) {
            if (!redundantBrackets.contains(i)) {
                result.add(details.get(i));
            }
        }

        return result;
    }

    /**
     * 常量折叠（简化版本）
     */
    private static List<DimOperationDetailDTO> foldConstants(List<DimOperationDetailDTO> details) {
        // 这里实现简单的常量折叠逻辑
        // 在实际应用中，这需要更复杂的实现
        return details; // 暂时返回原表达式
    }

    /**
     * 代数简化
     */
    private static List<DimOperationDetailDTO> simplifyAlgebraically(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> result = new ArrayList<>();

        for (int i = 0; i < details.size(); i++) {
            DimOperationDetailDTO current = details.get(i);

            // 简单的代数简化：x + 0 = x, x * 1 = x, x * 0 = 0
            if (i >= 2 && isOperator(current)) {
                DimOperationDetailDTO prev1 = details.get(i - 1);
                DimOperationDetailDTO prev2 = details.get(i - 2);

                // 这里可以添加更多的代数简化规则
                // 当前只是示例框架
            }

            result.add(current);
        }

        return result;
    }

    /**
     * 生成缓存键 - 内存优化版本
     */
    private static String generateCacheKey(List<DimOperationDetailDTO> details, Set<Long> variableIds) {
        // 内存优化：使用更紧凑的缓存键格式
        StringBuilder sb = new StringBuilder(details.size() * 4 + variableIds.size() * 8);

        // 使用更短的标识符
        for (DimOperationDetailDTO dto : details) {
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();
            switch (type) {
                case VARIABLE:
                    sb.append("V").append(dto.getDimVariableId());
                    break;
                case FUNDAMENTAL:
                    // 使用运算符的简短标识
                    DimOperationFundamentalTypeEnum fundType = dto.getFundamentalType();
                    if (fundType != null) {
                        sb.append("F").append(fundType.ordinal()); // 使用序号而不是名称
                    }
                    break;
                case BRACKET:
                    DimOperationBracketTypeEnum bracketType = dto.getBracketType();
                    if (bracketType != null) {
                        sb.append("B").append(bracketType.ordinal()); // 使用序号而不是名称
                    }
                    break;
                default:
                    sb.append("U"); // Unknown
            }
            sb.append(",");
        }

        // 变量ID列表 - 排序后拼接
        if (!variableIds.isEmpty()) {
            sb.append("|");
            variableIds.stream().sorted().forEach(id -> sb.append(id).append(","));
        }

        return sb.toString();
    }

    /**
     * 生成优化缓存键
     */
    private static String generateOptimizationCacheKey(List<DimOperationDetailDTO> details, OptimizationConfig config) {
        StringBuilder sb = new StringBuilder();
        sb.append("OPT:")
          .append(config.isConstantFolding() ? "CF" : "")
          .append(config.isRedundantBracketRemoval() ? "RBR" : "")
          .append(config.isAlgebraicSimplification() ? "AS" : "")
          .append(":");

        for (DimOperationDetailDTO dto : details) {
            sb.append(dto.getDimOperationDetailType().name()).append(":");
            if (dto.getDimVariableId() != null) {
                sb.append("V").append(dto.getDimVariableId());
            } else if (dto.getFundamentalType() != null) {
                sb.append("F").append(dto.getFundamentalType().name());
            } else if (dto.getBracketType() != null) {
                sb.append("B").append(dto.getBracketType().name());
            }
            sb.append("|");
        }
        return sb.toString();
    }

    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format(
            "Postfix Cache: %s, Optimization Cache: %s",
            POSTFIX_CACHE.stats(),
            OPTIMIZED_EXPRESSION_CACHE.stats()
        );
    }

    /**
     * 清空缓存
     */
    public static void clearCache() {
        POSTFIX_CACHE.invalidateAll();
        OPTIMIZED_EXPRESSION_CACHE.invalidateAll();
        // 强制垃圾回收缓存相关的内存
        POSTFIX_CACHE.cleanUp();
        OPTIMIZED_EXPRESSION_CACHE.cleanUp();
    }

    /**
     * 获取内存使用情况
     */
    public static String getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        return String.format(
            "内存使用: %dMB/%dMB (最大: %dMB), 后缀缓存: %d项, 优化缓存: %d项",
            usedMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            maxMemory / 1024 / 1024,
            POSTFIX_CACHE.estimatedSize(),
            OPTIMIZED_EXPRESSION_CACHE.estimatedSize()
        );
    }

    /**
     * 手动触发缓存清理
     */
    public static void performMaintenance() {
        POSTFIX_CACHE.cleanUp();
        OPTIMIZED_EXPRESSION_CACHE.cleanUp();

        // 清理符号缓存中的无用条目
        if (SYMBOL_CACHE.size() > 100) {
            // 如果符号缓存过大，清理一部分
            SYMBOL_CACHE.clear();
        }

        // 建议垃圾回收
        System.gc();
    }

    /**
     * 获取详细的缓存统计信息
     */
    public static String getDetailedCacheStats() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 缓存详细统计 ===\n");

        // 后缀表达式缓存统计
        sb.append("后缀表达式缓存:\n");
        sb.append("  大小: ").append(POSTFIX_CACHE.estimatedSize()).append(" 项\n");
        sb.append("  统计: ").append(POSTFIX_CACHE.stats()).append("\n");

        // 优化表达式缓存统计
        sb.append("优化表达式缓存:\n");
        sb.append("  大小: ").append(OPTIMIZED_EXPRESSION_CACHE.estimatedSize()).append(" 项\n");
        sb.append("  统计: ").append(OPTIMIZED_EXPRESSION_CACHE.stats()).append("\n");

        // 符号缓存统计
        sb.append("符号缓存:\n");
        sb.append("  大小: ").append(SYMBOL_CACHE.size()).append(" 项\n");

        return sb.toString();
    }

    /**
     * 内存健康检查
     */
    public static MemoryHealthReport checkMemoryHealth() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        long postfixCacheSize = POSTFIX_CACHE.estimatedSize();
        long optimizedCacheSize = OPTIMIZED_EXPRESSION_CACHE.estimatedSize();
        int symbolCacheSize = SYMBOL_CACHE.size();

        MemoryHealthStatus status;
        List<String> recommendations = new ArrayList<>();

        // 内存使用率检查
        if (memoryUsagePercent > 90) {
            status = MemoryHealthStatus.CRITICAL;
            recommendations.add("内存使用率过高(" + String.format("%.1f", memoryUsagePercent) + "%)，建议立即清理缓存");
        } else if (memoryUsagePercent > 75) {
            status = MemoryHealthStatus.WARNING;
            recommendations.add("内存使用率较高(" + String.format("%.1f", memoryUsagePercent) + "%)，建议执行维护");
        } else {
            status = MemoryHealthStatus.HEALTHY;
        }

        // 缓存大小检查
        if (postfixCacheSize > 400) {
            recommendations.add("后缀表达式缓存过大(" + postfixCacheSize + "项)，建议清理");
        }
        if (optimizedCacheSize > 150) {
            recommendations.add("优化表达式缓存过大(" + optimizedCacheSize + "项)，建议清理");
        }
        if (symbolCacheSize > 80) {
            recommendations.add("符号缓存过大(" + symbolCacheSize + "项)，建议清理");
        }

        return new MemoryHealthReport(status, memoryUsagePercent,
                postfixCacheSize, optimizedCacheSize, symbolCacheSize, recommendations);
    }

    /**
     * 内存健康状态枚举
     */
    public enum MemoryHealthStatus {
        HEALTHY("健康"),
        WARNING("警告"),
        CRITICAL("严重");

        private final String description;

        MemoryHealthStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 内存健康报告
     */
    public static class MemoryHealthReport {
        private final MemoryHealthStatus status;
        private final double memoryUsagePercent;
        private final long postfixCacheSize;
        private final long optimizedCacheSize;
        private final int symbolCacheSize;
        private final List<String> recommendations;

        public MemoryHealthReport(MemoryHealthStatus status, double memoryUsagePercent,
                                 long postfixCacheSize, long optimizedCacheSize, int symbolCacheSize,
                                 List<String> recommendations) {
            this.status = status;
            this.memoryUsagePercent = memoryUsagePercent;
            this.postfixCacheSize = postfixCacheSize;
            this.optimizedCacheSize = optimizedCacheSize;
            this.symbolCacheSize = symbolCacheSize;
            this.recommendations = new ArrayList<>(recommendations);
        }

        public MemoryHealthStatus getStatus() { return status; }
        public double getMemoryUsagePercent() { return memoryUsagePercent; }
        public long getPostfixCacheSize() { return postfixCacheSize; }
        public long getOptimizedCacheSize() { return optimizedCacheSize; }
        public int getSymbolCacheSize() { return symbolCacheSize; }
        public List<String> getRecommendations() { return new ArrayList<>(recommendations); }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 内存健康报告 ===\n");
            sb.append("状态: ").append(status.getDescription()).append("\n");
            sb.append("内存使用率: ").append(String.format("%.1f", memoryUsagePercent)).append("%\n");
            sb.append("后缀缓存: ").append(postfixCacheSize).append(" 项\n");
            sb.append("优化缓存: ").append(optimizedCacheSize).append(" 项\n");
            sb.append("符号缓存: ").append(symbolCacheSize).append(" 项\n");

            if (!recommendations.isEmpty()) {
                sb.append("建议:\n");
                for (String recommendation : recommendations) {
                    sb.append("  - ").append(recommendation).append("\n");
                }
            }

            return sb.toString();
        }
    }

    /**
     * 自动内存监控和维护
     */
    private static void checkAndPerformAutoMaintenance() {
        // 增加执行计数器
        long currentCount = ++executionCounter;
        long currentTime = System.currentTimeMillis();

        // 检查是否需要执行维护
        boolean needMaintenance = false;

        // 条件1：执行次数达到阈值
        if (currentCount % MAINTENANCE_EXECUTION_THRESHOLD == 0) {
            needMaintenance = true;
        }

        // 条件2：时间间隔达到阈值
        if (currentTime - lastMaintenanceTime > MAINTENANCE_INTERVAL_MS) {
            needMaintenance = true;
        }

        // 条件3：内存使用率过高
        MemoryHealthReport healthReport = checkMemoryHealth();
        if (healthReport.getStatus() == MemoryHealthStatus.WARNING ||
            healthReport.getStatus() == MemoryHealthStatus.CRITICAL) {
            needMaintenance = true;
        }

        if (needMaintenance) {
            performAutoMaintenance(healthReport);
            lastMaintenanceTime = currentTime;
        }
    }

    /**
     * 执行自动维护
     */
    private static void performAutoMaintenance(MemoryHealthReport healthReport) {
        try {
            log.debug("[DimOperation] 开始自动维护，当前状态: {}", healthReport.getStatus().getDescription());

            // 根据健康状态决定维护策略
            switch (healthReport.getStatus()) {
                case CRITICAL:
                    // 严重情况：清空所有缓存
                    clearCache();
                    log.warn("[DimOperation] 内存使用严重，已清空所有缓存");
                    break;

                case WARNING:
                    // 警告情况：执行标准维护
                    performMaintenance();
                    log.info("[DimOperation] 内存使用警告，已执行维护");
                    break;

                case HEALTHY:
                    // 健康情况：轻量级清理
                    POSTFIX_CACHE.cleanUp();
                    OPTIMIZED_EXPRESSION_CACHE.cleanUp();
                    log.debug("[DimOperation] 执行轻量级维护");
                    break;
            }

            // 记录维护后的状态
            MemoryHealthReport afterReport = checkMemoryHealth();
            log.debug("[DimOperation] 维护完成，内存使用率: {}% -> {}%",
                     healthReport.getMemoryUsagePercent(), afterReport.getMemoryUsagePercent());

        } catch (Exception e) {
            log.error("[DimOperation] 自动维护过程中发生异常", e);
        }
    }

    /**
     * 获取执行统计信息
     */
    public static String getExecutionStats() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastMaintenance = currentTime - lastMaintenanceTime;

        return String.format(
            "执行统计: 总执行次数=%d, 距离上次维护=%d分钟, 下次维护倒计时=%d次执行",
            executionCounter,
            timeSinceLastMaintenance / 60000,
            MAINTENANCE_EXECUTION_THRESHOLD - (executionCounter % MAINTENANCE_EXECUTION_THRESHOLD)
        );
    }

    /**
     * 重置执行统计
     */
    public static void resetExecutionStats() {
        executionCounter = 0;
        lastMaintenanceTime = System.currentTimeMillis();
        log.info("[DimOperation] 执行统计已重置");
    }

    /**
     * 测试优化功能
     */
    private static void testOptimization(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        // 测试基本优化
        ExecutionResult basicResult = executeExpressionWithOptimization(
                expression, values, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("基本优化结果: " + basicResult.getResult());
        System.out.println("基本优化执行时间: " + basicResult.getExecutionTimeMs() + "ms");

        // 测试完整优化
        ExecutionResult fullResult = executeExpressionWithOptimization(
                expression, values, TraceConfig.debug(), OptimizationConfig.full());
        System.out.println("完整优化结果: " + fullResult.getResult());
        System.out.println("完整优化执行时间: " + fullResult.getExecutionTimeMs() + "ms");

        // 测试冗余括号优化
        List<DimOperationDetailDTO> redundantExpression = new ArrayList<>();
        redundantExpression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        redundantExpression.add(createVariable("单独变量", 10L));
        redundantExpression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        redundantExpression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        redundantExpression.add(createVariable("乘数", 11L));

        Map<Long, BigDecimal> redundantValues = Map.of(
                10L, new BigDecimal("5"),
                11L, new BigDecimal("3")
        );

        System.out.println("原始表达式: " + printExpression(redundantExpression));
        ExecutionResult optimizedResult = executeExpressionWithOptimization(
                redundantExpression, redundantValues, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("优化后结果: " + optimizedResult.getResult());
    }

    /**
     * 性能测试方法
     */
    private static void performanceTest(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        int iterations = 10000;

        // 测试无跟踪版本性能
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpression(expression, values);
        }
        long noTraceTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试带跟踪版本性能
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithTrace(expression, values, TraceConfig.disabled());
        }
        long withTraceTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试优化版本性能
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithOptimization(expression, values, TraceConfig.disabled(), OptimizationConfig.basic());
        }
        long optimizedTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试缓存效果（第二次运行）
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithOptimization(expression, values, TraceConfig.disabled(), OptimizationConfig.basic());
        }
        long cachedTime = (System.nanoTime() - startTime) / 1_000_000;

        System.out.println("执行 " + iterations + " 次:");
        System.out.println("无跟踪版本: " + noTraceTime + "ms");
        System.out.println("带跟踪版本(禁用): " + withTraceTime + "ms");
        System.out.println("优化版本(首次): " + optimizedTime + "ms");
        System.out.println("优化版本(缓存): " + cachedTime + "ms");
        System.out.println("缓存提升: " + (optimizedTime - cachedTime) + "ms (" +
                          String.format("%.2f", (double)(optimizedTime - cachedTime) / optimizedTime * 100) + "%)");
    }

    // 辅助创建方法
    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }

}