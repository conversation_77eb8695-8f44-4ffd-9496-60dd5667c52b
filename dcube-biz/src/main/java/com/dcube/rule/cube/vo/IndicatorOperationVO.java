package com.dcube.rule.cube.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
public class IndicatorOperationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关联指标对象集合
     */
    @Schema(description = "关联指标对象集合(实例)")
    private List<IndVO> indList = Collections.emptyList();

    /**
     * 关联维度对象集合
     */
    @Schema(description = "关联维度对象集合")
    private List<DimDirectoryVO> dimList = Collections.emptyList();

    @Schema(description = "初始化维度")
    private String initDimValue;
}
