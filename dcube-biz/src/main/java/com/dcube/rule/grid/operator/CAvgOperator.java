package com.dcube.rule.grid.operator;

import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.constants.enums.RuleOperatorEnum;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * 子表
 */
@Slf4j
public class CAvgOperator extends Operator {


    /**
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length != 1) {
            throw new RuleExecutionException("cavg规则定义的参数不正确");
        }
        String tableName = "";
        if (list[0] instanceof Object[]) {
            tableName = String.valueOf(((Object[]) list[0])[0]);
        }
        Object[] rowData = ThreadLocalUtils.get(ExpressUtils.class, RuleOperatorEnum.O.name());
        String key = SpringUtils.getBean(IRuleService.class).generateSubTableKey(tableName, rowData);
        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        Map<String, Object> map = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.CAVG + ruleId + "_" + tableName);
        return MapUtils.getObject(map, key);
    }
}
