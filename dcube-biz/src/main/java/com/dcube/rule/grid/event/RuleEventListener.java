package com.dcube.rule.grid.event;

import com.alibaba.ttl.TtlRunnable;
import com.dcube.biz.dto.RowDataDto;
import com.dcube.common.aspect.ThreadLocalCacheAspect;
import com.dcube.common.constant.enums.JobOperateTypeEnums;
import com.dcube.common.constant.enums.StateEnum;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.quartz.util.TaskUtil;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class RuleEventListener {
    @Autowired
    private IRuleService ruleService;
    @Autowired
    @Qualifier("taskExecutorAbort")
    private ThreadPoolTaskExecutor taskExecutorAbort;

    @EventListener
    public void listenDataChangeEvent(DataChangeEvent event) {
        taskExecutorAbort.execute(TtlRunnable.get(() -> {
            long start = System.currentTimeMillis();
            List<Object[]> rowData = event.getRowData();
            String type = event.getType();
            String taskId = event.getTaskId();
            ReportDto reportDto = TaskUtil.getTaskInfoCache(taskId, v -> {
                ReportDto reportDto1 = new ReportDto().setTaskId(taskId).setType(JobOperateTypeEnums.SAVE_DATA).setTaskName(JobOperateTypeEnums.SAVE_DATA.getName()).setStatus(TaskStatusEnums.WILL_START).setStartTime(start);
                log.info("创建任务id：{}，hashCode：{}", taskId, reportDto1.hashCode());
                return reportDto1;
            });
            //        if (log.isDebugEnabled()) {
//            log.debug("监听到数据改变，类型：{}", type);
//        }
            if (rowData == null) {
                reportDto.setCost((System.currentTimeMillis() - start)).setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
                return;
            }
            try {
                if (StringUtils.startsWithIgnoreCase(type, "before")) {

                } else if (StringUtils.startsWithIgnoreCase(type, "after")) {
                    // 初始化线程变量
                    ThreadLocalCacheAspect.init();
                    ThreadLocalCacheAspect.initTtl();
                    Integer tableId = event.getTableId();
                    Map<Integer, Map<Integer, List<String>>> executeColumnCodeMap = ExpressUtils.collectRuleRef(tableId);
                    if (MapUtils.isNotEmpty(executeColumnCodeMap)) {
                        // 是否需要更新全表
                        boolean flag = false;
                        int taskCount = 0;
                        Map<Integer, Map<String, String>> tableColumnNameCodeMap = new ConcurrentHashMap<>();
                        for (Map.Entry<Integer, Map<Integer, List<String>>> entry : executeColumnCodeMap.entrySet()) {
                            Map<Integer, List<String>> tableColumnExpressMap = entry.getValue();
                            if (MapUtils.isEmpty(tableColumnExpressMap)) {
                                continue;
                            }
                            for (Map.Entry<Integer, List<String>> entry1 : tableColumnExpressMap.entrySet()) {
                                if (CollectionUtils.isEmpty(entry1.getValue())) {
                                    continue;
                                }
                                taskCount++;
                                Integer tableId1 = entry1.getKey();
                                ruleService.initTableLocalCache(tableId1);
                                if (tableId.equals(tableId1)) {
                                    if (!flag) {
                                        Map<String, Map<String, Object>> indexExpressMap = ruleService.selectRuleExpressByTableIdAndColumnCodes(entry1.getKey(), entry1.getValue());
                                        if (MapUtils.isNotEmpty(indexExpressMap)) {
                                            for (Map.Entry<String, Map<String, Object>> entry2 : indexExpressMap.entrySet()) {
                                                Map<String, Object> map = entry2.getValue();
                                                String ruleExpress = MapUtils.getString(map, "ruleExpress");
                                                if (StringUtils.contains(ruleExpress, "alloca(") || StringUtils.contains(ruleExpress, "product(")) {
                                                    flag = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                                Map<String, String> columnNameCodeMap = tableColumnNameCodeMap.get(tableId1);
                                if (columnNameCodeMap == null) {
                                    columnNameCodeMap = ruleService.getTableColumnNameCodeMap(entry1.getKey());
                                    tableColumnNameCodeMap.put(tableId1, columnNameCodeMap);
                                }
                            }
                        }
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_COLUMN_NAME_CODE_MAP, tableColumnNameCodeMap);

                        boolean ownFlag = flag;
                        AtomicInteger taskCurrentCount = new AtomicInteger(0);
                        final int taskTotalCount = taskCount;
                        for (Map.Entry<Integer, Map<Integer, List<String>>> entry : executeColumnCodeMap.entrySet()) {
                            Map<Integer, List<String>> tableColumnExpressMap = entry.getValue();
                            if (MapUtils.isEmpty(tableColumnExpressMap)) {
                                continue;
                            }
                            for (Map.Entry<Integer, List<String>> entry1 : tableColumnExpressMap.entrySet()) {
                                if (CollectionUtils.isEmpty(entry1.getValue())) {
                                    continue;
                                }
                                int curTaskCount = taskCurrentCount.incrementAndGet();
                                reportDto.setTaskName(JobOperateTypeEnums.SAVE_DATA.getName() + StringUtils.format("-当前批次/总批次=>【{}/{}】", curTaskCount, taskTotalCount));
                                reportDto.setState(StateEnum.RUNNING.getCode());
                                reportDto.setBatchStatus(StateEnum.RUNNING);

                                // 本表规则
                                if (tableId.equals(entry1.getKey())) {
                                    if (ownFlag) {
                                        ruleService.doExecuteRuleExpress(entry1.getKey(), entry1.getValue(), reportDto);
                                    } else {
                                        ruleService.doExecuteRuleExpress(entry1.getKey(), entry1.getValue(), rowData, reportDto);
                                    }
                                } else {
                                    ruleService.doExecuteRuleExpress(entry1.getKey(), entry1.getValue(), reportDto);
                                }
                                if (curTaskCount == taskTotalCount) {
                                    reportDto.setBatchStatus(StateEnum.SUCCEEDED);
                                }
                            }
                        }
                        reportDto.setCost((System.currentTimeMillis() - start)).setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
                    } else {
                        reportDto.setCost((System.currentTimeMillis() - start)).setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
                    }
                }
            } catch (Exception e) {
                log.error("保存数据时规则计算出现异常！", e);
                if (e instanceof CompletionException || e instanceof ExecutionException) {
                    String message = ExceptionUtil.getLocalizedMessage(e.getCause());
                    TaskUtil.updateTaskStatus(taskId, TaskStatusEnums.ERROR, message);
                    throw new RuleExecutionException(message);
                }
                String message = ExceptionUtil.getLocalizedMessage(e);
                TaskUtil.updateTaskStatus(taskId, TaskStatusEnums.ERROR, message);
                throw new RuleExecutionException(message);
            } finally {
                // 回收线程变量
                ThreadLocalUtils.clear();
            }
        }));
    }

    @EventListener
    public void listenDeleteRowDataEvent(DeleteRowDataEvent event) {
        taskExecutorAbort.execute(TtlRunnable.get(() -> {
            long start = System.currentTimeMillis();
            String taskId = event.getTaskId();
            ReportDto reportDto = TaskUtil.getTaskInfoCache(taskId, v -> {
                ReportDto reportDto1 = new ReportDto().setTaskId(taskId).setType(JobOperateTypeEnums.SAVE_DATA).setTaskName(JobOperateTypeEnums.SAVE_DATA.getName()).setStatus(TaskStatusEnums.WILL_START).setStartTime(start);
                log.info("创建任务id：{}，hashCode：{}", taskId, reportDto1.hashCode());
                return reportDto1;
            });
            RowDataDto rowData = event.getRowData();
            if (rowData == null) {
                reportDto.setCost((System.currentTimeMillis() - start)).setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
                return;
            }
            try {
                // 初始化线程变量
                ThreadLocalCacheAspect.init();
                ThreadLocalCacheAspect.initTtl();
                Integer tableId = event.getTableId();
                Map<Integer, Map<Integer, List<String>>> executeColumnCodeMap = ExpressUtils.collectRuleRef(tableId);
                if (MapUtils.isNotEmpty(executeColumnCodeMap)) {
                    // 删除数据是否需要执行本表规则
                    Iterator<Map.Entry<Integer, Map<Integer, List<String>>>> iterator = executeColumnCodeMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Integer, Map<Integer, List<String>>> entry = iterator.next();
                        Map<Integer, List<String>> tableColumnExpressMap = entry.getValue();
                        if (MapUtils.isEmpty(tableColumnExpressMap)) {
                            iterator.remove();
                            continue;
                        }
                        boolean flag = false;
                        for (Map.Entry<Integer, List<String>> entry1 : tableColumnExpressMap.entrySet()) {
                            if (tableId.equals(entry1.getKey())) {
                                Map<String, Map<String, Object>> indexExpressMap = ruleService.selectRuleExpressByTableIdAndColumnCodes(entry1.getKey(), entry1.getValue());
                                if (MapUtils.isNotEmpty(indexExpressMap)) {
                                    for (Map.Entry<String, Map<String, Object>> entry2 : indexExpressMap.entrySet()) {
                                        Map<String, Object> map = entry2.getValue();
                                        if (StringUtils.contains(MapUtils.getString(map, "ruleExpress"), "alloca")) {
                                            flag = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        if (!flag) {
                            entry.getValue().remove(tableId);
                            if (MapUtils.isEmpty(entry.getValue())) {
                                iterator.remove();
                            }
                        }
                    }
                    int taskCount = 0;
                    Map<Integer, Integer> tableDataMap = new HashMap<>();
                    Map<Integer, Map<String, String>> tableColumnNameCodeMap = new ConcurrentHashMap<>();
                    for (Map.Entry<Integer, Map<Integer, List<String>>> entry : executeColumnCodeMap.entrySet()) {
                        Map<Integer, List<String>> tableColumnExpressMap = entry.getValue();
                        for (Map.Entry<Integer, List<String>> entry1 : tableColumnExpressMap.entrySet()) {
                            taskCount++;
                            Integer tableId1 = entry1.getKey();
                            ruleService.initTableLocalCache(tableId1);
                            Integer tableDataLen = tableDataMap.get(tableId1);
                            if (tableDataLen == null) {
                                Object[][] tableData = ruleService.getTableData(ruleService.getTableById(tableId1));
                                if (tableData != null) {
                                    tableDataLen = tableData.length;
                                    tableDataMap.put(tableId1, tableDataLen);
                                }
                            }
                            Map<String, String> columnNameCodeMap = tableColumnNameCodeMap.get(tableId1);
                            if (columnNameCodeMap == null) {
                                columnNameCodeMap = ruleService.getTableColumnNameCodeMap(entry1.getKey());
                                tableColumnNameCodeMap.put(tableId1, columnNameCodeMap);
                            }
                        }
                    }
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_COLUMN_NAME_CODE_MAP, tableColumnNameCodeMap);
                    AtomicInteger taskCurrentCount = new AtomicInteger(0);
                    final int taskTotalCount = taskCount;
                    for (Map.Entry<Integer, Map<Integer, List<String>>> entry : executeColumnCodeMap.entrySet()) {
                        Map<Integer, List<String>> tableColumnExpressMap = entry.getValue();
                        for (Map.Entry<Integer, List<String>> entry1 : tableColumnExpressMap.entrySet()) {
                            int curTaskCount = taskCurrentCount.incrementAndGet();
                            reportDto = new ReportDto<AtomicLong>().setTaskId(taskId)
                                    .setType(JobOperateTypeEnums.EXECUTE_RULE)
                                    .setTaskName(StringUtils.format(JobOperateTypeEnums.SAVE_DATA.getName() + StringUtils.format("-当前批次/总批次=>【{}/{}】", curTaskCount, taskTotalCount)))
                                    .setStatus(TaskStatusEnums.WILL_START).setStartTime(System.currentTimeMillis());
                            log.info("创建任务id：{}，hashCode：{}", taskId, reportDto.hashCode());
                            reportDto.setState(StateEnum.RUNNING.getCode());
                            reportDto.setBatchStatus(StateEnum.RUNNING);
                            TaskReport.put(reportDto);
                            ruleService.doExecuteRuleExpress(entry1.getKey(), entry1.getValue(), reportDto);
                            reportDto.setCost((System.currentTimeMillis() - reportDto.getStartTime())).setStatus(TaskStatusEnums.COMPLETE);
                            if (curTaskCount == taskTotalCount) {
                                reportDto.setBatchStatus(StateEnum.SUCCEEDED);
                            }
                        }
                    }
                } else {
                    reportDto.setCost((System.currentTimeMillis() - start)).setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
                }
            } catch (Exception e) {
                log.error("删除数据时规则计算出现异常！", e);
                if (e instanceof CompletionException || e instanceof ExecutionException) {
                    if (e.getCause() instanceof RuleExecutionException) {
                        String message = ExceptionUtil.getLocalizedMessage(e.getCause());
                        TaskUtil.updateTaskStatus(taskId, TaskStatusEnums.ERROR, message);
                        throw new RuleExecutionException(message);
                    }
                }
                String message = ExceptionUtil.getLocalizedMessage(e);
                TaskUtil.updateTaskStatus(taskId, TaskStatusEnums.ERROR, message);
                throw new RuleExecutionException(message);
            } finally {
                // 回收线程变量
                ThreadLocalUtils.clear();
            }
        }));
    }


    @EventListener
    public void listenLoadDataEvent(LoadDataEvent event) {
        taskExecutorAbort.execute(TtlRunnable.get(() -> {
            String taskId = event.getTaskId();
            ReportDto reportDto = TaskUtil.getTaskInfoCache(taskId, v -> {
                ReportDto reportDto1 = new ReportDto().setTaskId(taskId).setType(JobOperateTypeEnums.SAVE_DATA).setTaskName(JobOperateTypeEnums.SAVE_DATA.getName()).setStatus(TaskStatusEnums.WILL_START).setStartTime(System.currentTimeMillis());
                log.info("创建任务id：{}，hashCode：{}", taskId, reportDto1.hashCode());
                return reportDto1;
            });
//        ruleServiceHelper.execute(event.getTableId(), event.getTaskId());
            reportDto.setState(StateEnum.SUCCEEDED.getCode()).setStatus(TaskStatusEnums.COMPLETE);
        }));
    }

}
