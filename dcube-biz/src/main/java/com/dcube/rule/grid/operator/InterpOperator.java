package com.dcube.rule.grid.operator;

import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.util.ExpressUtils;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 线性插值
 * 自变量小于参数表最小值，直接取这个最小值
 * 自变量大于参数表最大值，直接取这个最大值
 * 中间的点就取最近的两个连线插值
 */
@Slf4j
public class InterpOperator extends Operator {
    private static final String POINT_LIST_KEY = "_POINT_LIST_";
    private static final String RET_MAP_KEY = "_RET_MAP_";

    /**
     * interp(o(\"期限\"),t(\"FTP参数表\",\"期限\"), t(\"FTP参数表\",\"ftp\"));
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length != 3) {
            throw new RuleExecutionException("interp规则定义的参数不正确");
        }
        if (!(list[1] instanceof Map) || !(list[2] instanceof Map)) {
            throw new RuleExecutionException("interp规则解析后类型不正确");
        }

        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        String value0 = String.valueOf(list[0]);

        // 获取结果缓存
        Map<String, BigDecimal> retMap = ThreadLocalUtils.getTtl(InterpOperator.class, RET_MAP_KEY + ruleId);
        if (retMap == null) {
            retMap = new ConcurrentHashMap<>();
            ThreadLocalUtils.setTtl(InterpOperator.class, RET_MAP_KEY + ruleId, retMap);
        }

        // 检查结果缓存
        BigDecimal cachedRet = retMap.get(value0);
        if (cachedRet != null) {
            return cachedRet;
        }

        BigDecimal target = BigDecimalUtils.convertDigDecimal(list[0]);
        Map<String, Object> map1 = (Map<String, Object>) list[1];
        Map<String, Object> map2 = (Map<String, Object>) list[2];

        // 获取排序后的点列表
        List<Point> pointList = getSortedPointList(ruleId, map1, map2);

        // 边界检查：小于最小值
        if (target.compareTo(pointList.get(0).x) <= 0) {
            BigDecimal ret = pointList.get(0).y;
            retMap.put(value0, ret);
            return ret;
        }
        // 边界检查：大于最大值
        if (target.compareTo(pointList.get(pointList.size() - 1).x) >= 0) {
            BigDecimal ret = pointList.get(pointList.size() - 1).y;
            retMap.put(value0, ret);
            return ret;
        }

        // 二分查找定位区间
        int idx = Collections.binarySearch(pointList, new Point(target, null),
                Comparator.comparing((Point p) -> p.x));

        // 精确匹配
        if (idx >= 0) {
            BigDecimal ret = pointList.get(idx).y;
            retMap.put(value0, ret);
            return ret;
        }
        // 线性插值
        else {
            int insertionPoint = -idx - 1;
            Point left = pointList.get(insertionPoint - 1);
            Point right = pointList.get(insertionPoint);

            BigDecimal k = right.y.subtract(left.y)
                    .divide(right.x.subtract(left.x), 10, RoundingMode.HALF_UP);
            BigDecimal ret = target.subtract(left.x)
                    .multiply(k).add(left.y);

            retMap.put(value0, ret);
            return ret;
        }
    }

    // 获取排序后的点列表（带缓存）
    private List<Point> getSortedPointList(Long ruleId,
                                           Map<String, Object> map1,
                                           Map<String, Object> map2) {
        String cacheKey = POINT_LIST_KEY + ruleId;
        List<Point> pointList = ThreadLocalUtils.get(InterpOperator.class, cacheKey);

        if (pointList == null) {
            pointList = new ArrayList<>(map1.size());

            // 构建点列表
            for (Map.Entry<String, Object> entry : map1.entrySet()) {
                String key = entry.getKey();
                BigDecimal x = BigDecimalUtils.convertDigDecimal(entry.getValue());
                BigDecimal y = BigDecimalUtils.convertDigDecimal(MapUtils.getObject(map2, key));
                pointList.add(new Point(x, y));
            }

            // 按x坐标排序
            pointList.sort(Comparator.comparing(p -> p.x));
            ThreadLocalUtils.set(InterpOperator.class, cacheKey, pointList);
        }
        return pointList;
    }

    // 内部点类
    private static class Point {
        final BigDecimal x;
        final BigDecimal y;

        Point(BigDecimal x, BigDecimal y) {
            this.x = x;
            this.y = y;
        }
    }
}