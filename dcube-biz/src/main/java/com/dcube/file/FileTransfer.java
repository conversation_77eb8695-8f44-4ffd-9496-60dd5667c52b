package com.dcube.file;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.vo.ProcessVo;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUtils;
import com.dcube.framework.netty.MsgService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @创建人 zhouhx
 * @创建时间 2024/1/3 14:16
 * @描述
 */
@Slf4j
public class FileTransfer {

    public String sourceId;// 源数据Id
    public String filePath;// 文件路径

    public boolean skipFirstLine;//是否跳过第一行

    private int chunkSize; // 分片大小（字节）

    private int nThreads; // 线程数量

    private SourceConfigJson sourceConfig; //数据库连接信息

    public String tableName; //表名

    public List<String> columns;//元数据

    public String splitter;//分割符

    public MsgService msgService;//消息服务

    public Long userId;//用户Id

    private int totalCount = 0;

    private AtomicInteger progress = new AtomicInteger(0);

    FileTransfer(FileTransferBuilder builder) {
        this.sourceId = builder.sourceId;
        this.filePath = builder.filePath;
        this.skipFirstLine = builder.skipFirstLine;
        this.chunkSize = builder.chunkSize;
        this.nThreads = builder.nThreads;
        this.sourceConfig = builder.sourceConfig;
        this.tableName = builder.tableName;
        this.columns = builder.columns;
        this.splitter = builder.splitter;
        this.msgService = builder.msgService;
        this.userId = builder.userId;
    }

    /**
     * 转发操作，将文件按指定行数分割并行读取。
     */
    public void transfer() {
        // 线程池参数校验
        if (nThreads <= 0 || chunkSize <= 0) {
            log.error("线程数和分片大小必须大于0。");
            throw new ServiceException("线程数和分片大小必须大于0。");
        }

        ExecutorService executor = Executors.newFixedThreadPool(nThreads);

        File file = new File(filePath);
        long totalLines = FileUtil.getTotalLines(file);
        totalCount = (int) totalLines-2;
        long offset = 0;
        int chunkCount = (int) (totalLines / chunkSize) + (totalLines % chunkSize == 0 ? 0 : 1);

        /**
         * 遍历所有块并进行分块读取
         */
        for (int i = 0; i < chunkCount; i++) {
            long start = (i == 0 && skipFirstLine) ? 2 : offset;
            long end = Math.min(offset + chunkSize - 1, totalLines - 1);
            executor.submit(() -> shardReading(start, end, filePath));
            offset += chunkSize;
        }

        // 关闭线程池，等待所有任务完成
        try {
            executor.awaitTermination(1, TimeUnit.SECONDS); // 可以作为参数进行配置
        } catch (InterruptedException e) {
            executor.shutdownNow(); // 如果等待被中断，立即关闭线程池
            log.error("文件传输被中断。", e);
            throw new ServiceException("文件传输中断。");
        } finally {
            executor.shutdown(); // 确保线程池被关闭
        }
    }


    /**
     * 分片读取
     *
     * @param start    分片起始位置
     * @param end      分片结束位置
     * @param filePath 文件路径
     */
    private void shardReading(long start, long end, String filePath) {
        try {
            String content = FileUtils.readLinesByRange(start, end, filePath);
            persistence(content); // 持久化数据
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    /**
     * 持久化
     *
     * @param content 需要持久化的数据内容
     * @throws SQLException 如果出现SQL异常
     */
    private void persistence(String content) throws SQLException {
        List<Object> values = new ArrayList<>();
        if (StringUtils.isNotEmpty(content)) {
            List<Object> rows = new ArrayList<>(StrUtil.splitTrim(content, "\n"));
            for (int i = 0; i < rows.size(); i++) {
                List<Object> fieldValues = new ArrayList<>(StrUtil.split(rows.get(i).toString(), splitter));
                Map<Integer, Object> data = new LinkedHashMap<>();
                for (int j = 0; j < fieldValues.size(); j++) {
                    data.put(j, fieldValues.get(j));
                }
                values.add(data);
            }
        }
        try {
            JdbcUtils.batchInsert(sourceConfig, tableName, columns, values);
            progress.getAndAdd(values.size());
            BigDecimal process = NumberUtil.div(new BigDecimal(progress.get()), new BigDecimal(totalCount), 2, RoundingMode.HALF_UP);
//            if(Objects.nonNull(userId)){
//                msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("正在导入数据到表【%s】...", tableName), process, sourceId, false)), userId);
//            }
        } catch (Exception e) {
            log.info(String.format("数据写入错误：原因：%s", ExceptionUtil.getRootCauseMessage(e)));
            throw new ServiceException(e);
        }
    }


}
