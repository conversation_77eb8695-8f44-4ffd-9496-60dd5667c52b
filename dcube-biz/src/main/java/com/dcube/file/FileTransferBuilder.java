package com.dcube.file;

import com.dcube.biz.json.SourceConfigJson;
import com.dcube.framework.netty.MsgService;

import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2024/1/3 13:39
 * @描述 文件分片处理服务
 */
public class FileTransferBuilder {

    public String sourceId;// 源数据Id
    public String filePath;// 文件路径
    public int chunkSize; // 分片大小（字节）
    public boolean skipFirstLine;//是否跳过第一行
    public int nThreads; // 线程数量
    public SourceConfigJson sourceConfig; //数据库连接信息

    public String tableName; //表名

    public List<String> columns;//元数据
    public String splitter;//字段分割符
    public MsgService msgService;//消息服务
    public Long userId;//用户Id

    public FileTransferBuilder(String filePath) {
        this.filePath = filePath;
    }

    public FileTransferBuilder chunkSize(int chunkSize) {
        this.chunkSize = chunkSize;
        return this;
    }

    public FileTransferBuilder skipFirstLine(boolean skipFirstLine) {
        this.skipFirstLine = skipFirstLine;
        return this;
    }

    public FileTransferBuilder nThreads(int nThreads) {
        this.nThreads = nThreads;
        return this;
    }

    public FileTransferBuilder sourceConfig(SourceConfigJson sourceConfig) {
        this.sourceConfig = sourceConfig;
        return this;
    }

    public FileTransferBuilder tableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public FileTransferBuilder columns(List<String> columns) {
        this.columns = columns;
        return this;
    }

    public FileTransferBuilder splitter(String splitter) {
        this.splitter = splitter;
        return this;
    }

    public FileTransferBuilder sourceId(String sourceId) {
        this.sourceId = sourceId;
        return this;
    }

    public FileTransferBuilder msgService(MsgService msgService) {
        this.msgService = msgService;
        return this;
    }

    public FileTransferBuilder userId(Long userId)  {
        this.userId = userId;
        return this;
    }

    /**
     * 构造并返回一个FileTransfer对象
     *
     * @return FileTransfer对象
     */
    public FileTransfer build() {
        return new FileTransfer(this);
    }


}
