package com.dcube.ai.solution.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.annotation.TableField;
import com.dcube.ai.constants.enums.DataInsightTypeEnum;
import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import com.dcube.ai.constants.enums.QuestionTypeEnum;
import com.dcube.ai.database.ChatDatabase;
import com.dcube.ai.database.Question;
import com.dcube.ai.domain.AIChatLog;
import com.dcube.ai.domain.ModelConfig;
import com.dcube.ai.factory.AIChatFactory;
import com.dcube.ai.service.IAIChatLogService;
import com.dcube.ai.solution.enums.DataTypeEnum;
import com.dcube.ai.solution.utils.StringUtils;
import com.dcube.ai.sse.SseSession;
import com.dcube.biz.dto.FilterDto;
import com.dcube.biz.service.IDataFoundationTableService;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.biz.vo.SourceVO;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.exception.ai.AIServiceException;
import com.dcube.common.exception.ai.RemoteAIException;
import com.dcube.common.utils.DateUtils;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.ThreadLocalUtils;
import com.google.common.collect.Maps;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AiV1Service {

    @Autowired
    private TableService tableService;
    @Autowired
    private DataService dataService;
    @Autowired
    private IAIChatLogService aiChatLogService;
    @Autowired
    private GlobalExceptionProperties globalExceptionProperties;
    @Autowired
    private IDataFoundationTableService iDataFoundationTableService;
    @Autowired
    private ISourceService sourceService;


    public void processSteps(Integer tableId, String question, ModelConfig model, String chatId, String questionId, String suggestion, String clientId, String originalQuestionId, String previousSql, ModelConfig dataInsightModelConfig,Boolean fromDataFoundation,String filterList) {
        String jsonData = "";
        String finalSql = "";
        try {
            sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, "common", clientId);
            sendData("", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
            String tableStructure ="";
            // 1. 获取表结构
            if(Objects.isNull(fromDataFoundation) || !fromDataFoundation){
                tableStructure = tableService.getTableStructure(tableId);
            }else{
                tableStructure = iDataFoundationTableService.getTableStructure(tableId);
            }
            sendData("表结构获取成功", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
            boolean shouldGenerateSQL = shouldGenerateSQLV1(model, question, tableStructure, clientId);
            sendData("", shouldGenerateSQL ? "需要生成SQL" : "无需生成SQL", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
            if (shouldGenerateSQL) {
                sendData("", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
                // 2. 生成SQL
                Map<String, Object> sqlResult = generateSQLV1(tableStructure, question, null, model, suggestion, previousSql, clientId,fromDataFoundation,filterList);
                if (MapUtil.isNotEmpty(sqlResult)) {
                    sendData("SQL生成成功", (String) sqlResult.get("text"), "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
                    sendData("", "", "false", DataTypeEnum.SQL.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                } else {
                    log.warn("SQL生成失败：{}", sqlResult);
                    sendData("", "SQL生成失败", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                    return;
                }

                sendData("正在查询数据", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
                // 3. 执行查询
                List<Map<String, Object>> data = null;
                int retryCount = 0;
                boolean success = false;
                while (retryCount < 5 && !success) {
                    try {
                        String sql = (String) sqlResult.get("sql");
                        data = dataService.executeQuery(sql, tableId,fromDataFoundation,filterList);
                        finalSql = (String) sqlResult.get("sql");
                        success = true;
                        sendSqlExecResult("第" + (retryCount + 1) + "次生成SQL语句，执行成功！", (String) sqlResult.get("sql"), "false", DataTypeEnum.SQL.getCode(), "false", "false", "true", questionId, question, "false", "ask", chatId, clientId, "true");
                    } catch (Exception e) {
                        sendSqlExecResult("第" + (retryCount + 1) + "次生成SQL语句，执行出错！", (String) sqlResult.get("sql"), "false", DataTypeEnum.SQL.getCode(), "false", "false", "true", questionId, question, "false", "ask", chatId, clientId, "false");
                        retryCount++;
                        log.warn("查询失败，重试次数: {}", retryCount, e);
                        if (retryCount != 5) {
                            // 重新生成SQL
                            sqlResult = generateSQLV1(tableStructure, question, "上次生成的SQL不对，SQL为：" + sqlResult.get("sql") + "，错误原因为" + ExceptionUtil.getRootErrorMessage(e), model, suggestion, previousSql, clientId,fromDataFoundation,filterList);
                            if (MapUtil.isNotEmpty(sqlResult)) {
                                sendData("SQL生成成功", (String) sqlResult.get("text"), "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
                                sendData("", "", "false", DataTypeEnum.SQL.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                            } else {
                                log.warn("SQL生成失败：{}", sqlResult);
                                sendData("", "SQL生成失败", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                                return;
                            }
                        }
                    }
                }
                if (!success) {
                    sendHasRecords("", "查询失败，重试次数达到上限", "false", DataTypeEnum.TEXT.getCode(), "true", "true", "false", questionId, question, "false", "ask", chatId, clientId, "false");
                    return;
                }
                int totalSize = data.size();
                Integer checkDataLength = checkDataLength(data, ((dataInsightModelConfig.getMaxInputLength() - 20) * 1000) / 2);
                if (checkDataLength != null) {
                    if (checkDataLength <= 0) {
                        data = Collections.emptyList();
                        sendHasRecords("数据查询成功", "本语句共可查询出" + totalSize + "条数据，由于AI输入输出限制仅展现前0条", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId, "true");
                    } else if (checkDataLength == totalSize) {
                        sendHasRecords("数据查询成功", "本语句共可查询出" + totalSize + "条数据", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId, "true");
                    } else {
                        data = data.subList(0, checkDataLength);
                        sendHasRecords("数据查询成功", "本语句共可查询出" + totalSize + "条数据，由于AI输入输出限制仅展现前" + checkDataLength + "条", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId, "true");
                    }
                } else {
                    sendHasRecords("数据查询成功", "本语句共可查询出" + totalSize + "条数据", "false", DataTypeEnum.TEXT.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId, "true");
                }
                sendData("", "正在生成结果", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
                jsonData = getJsonData(data);
                sendData("结果生成完成", jsonData, "false", DataTypeEnum.JSON.getCode(), "true", "true", "false", questionId, question, "true", "ask", chatId, clientId);
//                sendMessageType("</end>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, "common");
//                // 4. 生成HTML
//                Map<String, Object> htmlResult = generateHTML(question, suggestion, jsonData, model);
//                if (MapUtil.isNotEmpty(htmlResult)) {
//                    sendData(writer, "结果生成完成", (String) htmlResult.get("html"), "false", DataTypeEnum.HTML.getCode(), "true", "true", "false", questionId, question, "true", "ask", chatId, clientId);
//                }

            } else {
                generateQueryCodeQuestionV1Stream(tableId, model, chatId, questionId, question, suggestion, clientId);
                return;
            }
            SseSession.complete(clientId);
        } catch (RemoteAIException e) {
            sendErrorResponse(clientId, String.format("AI服务调用失败：%s", e.getLocalizedMessage()), "ask");
            SseSession.completeWithError(clientId, e);
        } catch (Exception e) {
            log.error("处理流程出现异常：", e);
            sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", e.getLocalizedMessage()) : "系统出现异常，请联系系统管理员！", "ask");
            SseSession.completeWithError(clientId, e);
        } finally {
            // 结果存下来
            ChatDatabase.saveChatQuestion(chatId, questionId, jsonData, ModelUseSceneEnum.QUERY_CODE, originalQuestionId, question, suggestion, finalSql, JSON.toJSONString(Arrays.asList(model, dataInsightModelConfig)), "", QuestionTypeEnum.PROCESS_STEPS);
        }
    }

    private Integer checkDataLength(List<Map<String, Object>> data, Integer maxInputLength) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        int i = 0;
        int count = 0;
        boolean overflow = false;
        // 最多500条数据
        int size = Math.min(data.size(), 500);
        for (; i < size; i++) {
            Map<String, Object> item = data.get(i);
            for (Map.Entry<String, Object> entry : item.entrySet()) {
                count += entry.getKey().length();
                if (count > maxInputLength) {
                    overflow = true;
                    break;
                }
                Object value = entry.getValue();
                if (value != null) {
                    count += String.valueOf(value).length();
                    if (count > maxInputLength) {
                        overflow = true;
                        break;
                    }
                }
            }
            if (overflow) {
                break;
            }
        }
        if (overflow) {
            return i - 1;
        } else if (i == size) {
            return i;
        } else {
            return null;
        }
    }

    /**
     * @Author: yanghao
     * @Date: 2025-03-01 16:40:14
     * @Params: [chatId, questionId]
     * @Return: void
     * @Description: 数据洞察
     */
    public void processDataInsight(Integer tableId, ModelConfig model, String chatId, String questionId, String question, String suggestion, DataInsightTypeEnum dataInsightType, String clientId, String chatQuestionAnswer, List<String> dataInsightQuestionIds, String newQuestionId, String previousResult,Boolean fromDataFoundation) {
        String resultData = "";
        try {
            sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, "common", clientId);
            sendData("", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, clientId);
            resultData = dataInsightV1Stream(tableId, model, chatId, questionId, question, suggestion, dataInsightType, chatQuestionAnswer, clientId, newQuestionId, previousResult,fromDataFoundation);
        } catch (Exception e) {
            log.error("处理数据洞察流程异常, chatId: {}, questionId: {}", chatId, questionId, e);
            sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！", "dataInsight");
            SseSession.completeWithError(clientId, e);
        } finally {
            // 结果存下来
            String prompt = ThreadLocalUtils.get(ChatDatabase.class, "prompt");
            ChatDatabase.saveChatQuestion(chatId, newQuestionId, resultData, ModelUseSceneEnum.DATA_INSIGHT, dataInsightQuestionIds, questionId, question, suggestion, JSON.toJSONString(model), prompt, dataInsightType == DataInsightTypeEnum.SINGLE ? QuestionTypeEnum.DATA_INSIGHT_SINGLE : QuestionTypeEnum.DATA_INSIGHT_ALL);
        }
    }

    public boolean shouldGenerateSQLV1(ModelConfig model, String question, String tableStructure, String clientId) throws RemoteAIException {
        String systemPrompt = "请根据我提供的JSON格式表结构按照以下规则判断用户问题是否可以生成SQL查询语句，仅回答：是/否：" +
                "1.分析用户问题是否涉及数据库操作，例如检索、查询、搜索、分组计算、聚合计算、查找数据\n" +
                "2.检查用户问题是否包含以下一个或多个特征：\n" +
                "- 特征一：明确提及表结构中某个字段的\"name\"\n" +
                "- 特征二：包含数据筛选条件（如\"大于3000\"，\"2023年的\"等）\n" +
                "- 特征三：使用SQL相关动词（查询、查找、统计、汇总、筛选、分组、聚合等）\n" +
                "- 特征四：涉及分组计算（总和、求和、汇总、平均、加权平均、最大值、最小值、计数等）\n" +
                "- 特征五：存在明确的数据处理需求\n" +
                "- 特征六：可以通过SQL语法实现\n" +
                "- 特征七：如果命令以【问数返工】作为开头，则该问题一定需要生成SQL\n";
        String userPrompt = "表结构：" + tableStructure + "\n我的问题是：" + com.dcube.common.utils.StringUtils.replace(question, "<br />", "");
        Map<String, Object> response = callApiV1(systemPrompt, userPrompt, "needSql", question, null, model, 1, QuestionTypeEnum.SHOULD_GENERATE_SQL, clientId);
        return com.dcube.common.utils.StringUtils.equals(MapUtils.getString(response, "needSql"), "是");
    }

    public Map<String, Object> generateSQLV1(String tableStructure, String question, String tips, ModelConfig model, String suggestion, String previousSql, String clientId,Boolean fromDataFoundation,String filterList) throws RemoteAIException {
        String currentDate = DateUtils.getDate();
        tips = com.dcube.common.utils.StringUtils.defaultIfEmpty(tips, "");
        boolean suggest = com.dcube.common.utils.StringUtils.isNotEmpty(suggestion);
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();
        String extraTips = "";
        if(Objects.nonNull(dataFoundationSource)){
            extraTips = String.format("使用%s数据库语法，",dataFoundationSource.getSourceType());
        }
        String systemPrompt = "你是一位资深的SQL专家，"+extraTips+"你生成的SQL语句不要添加任何注释，也不需要做任何解释。\n" + tips +
                (com.dcube.common.utils.StringUtils.isNotEmpty(tips) ?
                        (suggest ? "请注意，本次对SQL纠错的用户动作是【问数返工】。被返工的SQL为：" +
                                "```sql\n" + previousSql + "```\n" +
                                "请结合用户问题需求、用户1次或多次的返工意见（请注意：此处包含历次返工意见，包括倒数第一次，如果历史返工意见跟倒数第一次意见有冲突，以倒数第一次的意见为准）、表结构、被返工SQL、本次生成的错误SQL、以及本次生成错误SQL的错误原因。重新生成可供执行的SQL语句。\n" :
                                "请结合用户问题需求、表结构、错误SQL、以及错误原因。重新生成可供执行的SQL语句。\n") :
                        (suggest ? "上一版完整需求（包含原始指令及历次返工意见，截止到倒数第二次返工，如仅有一次返工上一版完整需求就不包含返工意见）结合本表结构。上一版生成可供执行的SQL语句为：\n" +
                                "```sql\n" + previousSql + "```\n" +
                                "由于其执行结果不符合预期，需要将结果返工，请根据1次或多次的返工意见（请注意：此处包含历次返工意见，包括倒数第一次，如果历史返工意见跟倒数第一次意见有冲突，以倒数第一次的意见为准），重新生成SQL语句。\n" :
                                "请结合用户问题需求和表结构。生成可供执行的SQL语句。")) +
                "生成SQL时，"+extraTips+"若查询需要涉及当前日期或时间（SYSDATE），直接使用" + currentDate + "作为占位符。\n" +
                ((Objects.nonNull(fromDataFoundation) && fromDataFoundation)?("基础查询条件数据和结构为：\n"+filterList):StrUtil.EMPTY)+
                "此外，生成SQL语句的语法采用以下编码规范，编码规范以[SQL_RULE_START]开始，以[SQL_RULE_END]结束。" +
                "[SQL_RULE_START]\n" +
                ((Objects.nonNull(fromDataFoundation) && fromDataFoundation)?model.getDfInputExample():model.getInputExample())
                + "[SQL_RULE_END]";
        String userPrompt = "表结构：\n" + com.dcube.common.utils.StringUtils.appendIfMissing(tableStructure, "。") + "\n我的问题是：" + com.dcube.common.utils.StringUtils.appendIfMissing(com.dcube.common.utils.StringUtils.replace(question, "<br />", ""), "。");
//        if (com.dcube.common.utils.StringUtils.isNotEmpty(suggestion)) {
//            userPrompt += "\n你的输出结果需要改进，改进意见：" + com.dcube.common.utils.StringUtils.appendIfMissing(suggestion, "。");
//        }
        if (com.dcube.common.utils.StringUtils.isNotEmpty(tips)) {
            userPrompt += "\n" + tips;
        }
        return callApiV1(systemPrompt, userPrompt, "sql", "```sql", "```", model, 3, QuestionTypeEnum.GENERATE_SQL, getModelTemperature(model.getModelName()), clientId);
    }

    private double getModelTemperature(String modelName) {
        if (com.dcube.common.utils.StringUtils.startsWith(modelName, "qwen3")) {
            return 0.7d;
        } else {
            return 0.0d;
        }
    }

    public void generateQueryCodeQuestionV1Stream(Integer tableId, ModelConfig model, String chatId, String questionId, String question, String suggestion, String clientId) throws RemoteAIException {
//        sendMessageType(writer, "</end>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, "common");
        sendMessageType("<start>", "", "false", DataTypeEnum.MARKDOWN.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, "stream", clientId);
        sendData("", "", "false", DataTypeEnum.MARKDOWN.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, clientId);
        AIChatLog aiChatLog = new AIChatLog();
        aiChatLog.setChatId(Long.valueOf(chatId));
        aiChatLog.setQuestionId(Long.valueOf(questionId));
        aiChatLog.setSceneType(ModelUseSceneEnum.QUERY_CODE);
        aiChatLog.setQuestionType(QuestionTypeEnum.GENERATE_QUERY_CODE_QUESTION);
        aiChatLog.setModelConfig(JSON.toJSONString(model));
        String tableStructure = tableService.getTableStructure(tableId);
        // 定义同步锁
        CountDownLatch latch = new CountDownLatch(1);
        try {
            generateQueryCodeQuestionV1Stream(tableStructure, question, suggestion, model, new StreamingResponseHandler<AiMessage>() {
                private final List<String> batch = new ArrayList<>();
                private static final int BATCH_SIZE = 3;

                @Override
                public void onNext(String token) {
                    batch.add(token);
                    if (batch.size() >= BATCH_SIZE) {
                        sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                        batch.clear();
                    }
                }

                @Override
                public void onComplete(Response<AiMessage> complete) {
                    try {
                        if (!batch.isEmpty()) {
                            sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", questionId, question, "false", "ask", chatId, clientId);
                            batch.clear();
                        }
                        aiChatLog.setAiResult(complete.content().text());
                        sendData("结果生成完成", "", "false", DataTypeEnum.MARKDOWN.getCode(), "true", "false", "false", questionId, question, "true", "ask", chatId, clientId);
                        SseSession.complete(clientId);
                    } finally {
                        // 释放锁
                        latch.countDown();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    log.error("对话出现错误：", error);
                    try {
                        aiChatLog.setIsSuccess(0);
                        String rootErrorMessage = ExceptionUtil.getRootErrorMessage(error);
                        aiChatLog.setAiResult(rootErrorMessage);
                        sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", rootErrorMessage) : "系统出现异常，请联系系统管理员！", "ask");
                        SseSession.completeWithError(clientId, error);
                    } finally {
                        // 释放锁
                        latch.countDown();
                    }
                }
            }, aiChatLog);
            try {
                // 阻塞主线程等待异步完成
                latch.await();
            } catch (InterruptedException e) {
                log.error("阻塞主线程出现异常", e);
                throw new AIServiceException("等待AI返回时出现异常，详情请查看错误日志");
            }
        } finally {
            aiChatLogService.save(aiChatLog);
        }
    }

    public void generateQueryCodeQuestionV1Stream(String tableStructure, String question, String suggestion, ModelConfig model, StreamingResponseHandler<AiMessage> handler, AIChatLog aiChatLog) throws RemoteAIException {
        String systemPrompt = "请识别我的问题中的查询目标意图（统计/筛选/排序/比较/分组），并基于提供的JSON表结构，按以下规则生成3个修正问题：\n" +
                "1.必须明确包含列名（从name属性提取）\n" +
                "2.必须包含具体查询动作（SELECT/WHERE/GROUP BY/order by/having/sum/count等SQL关键词或函数对应的自然语言）\n" +
                "3.必须包含可量化的条件（数值/时间/文本匹配等，条件列从name属性提取，条件值从用户问题中提取）\n" +
                "4.若需自关联必须显式说明（如\"同一表中不同列比较\"）\n" +
                "5.保证每个修正问题必须能对应到唯一准确的SQL语句\n" + "6.输出内容以：“阿超无法准确识别你所问问题的查询意图，你是不是想了解：”这段话开头；然后换行，换行后逐行按1、2、3编号显示修正后的问题。";
        String userPrompt = "表结构：" + com.dcube.common.utils.StringUtils.appendIfMissing(tableStructure, "。") + "\n" + "我的问题是：" + com.dcube.common.utils.StringUtils.appendIfMissing(question, "。");
        List<ChatMessage> chatMessageList = new ArrayList<>(2);
        ChatMessage systemMessage = new SystemMessage(systemPrompt);
        chatMessageList.add(systemMessage);
        ChatMessage userMessage = new UserMessage(userPrompt);
        chatMessageList.add(userMessage);
        aiChatLog.setPrompt(chatMessageList.toString());
        StreamingChatLanguageModel chatLanguageModel = AIChatFactory.getAIChatFactory(model).getStreamingChatLanguageModel();
        try {
            chatLanguageModel.generate(chatMessageList, handler);
        } catch (Exception e) {
            log.error("API调用失败：", e);
            throw new RemoteAIException("API调用失败: " + e.getMessage());
        }
    }

    public String getJsonData(List<Map<String, Object>> data) throws RemoteAIException {
        List<Map<String, String>> list = data.stream()
//                .parallel()
                .map(map -> map.entrySet()
                        .stream()
//                        .parallel()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> {
                                    Object value = entry.getValue();
                                    if (value == null) {
                                        return "";
                                    } else if (value instanceof Date) {
                                        return DateUtil.formatDate((Date) value);
                                    } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                                        BigDecimal decimal = BigDecimalUtils.convertDigDecimal(value)
                                                .setScale(4, RoundingMode.HALF_UP);
                                        return decimal.toPlainString();
                                    } else {
                                        return String.valueOf(value);
                                    }
                                },
                                (v1, v2) -> v1,
                                LinkedHashMap::new
                        ))).collect(Collectors.toList());

        // 序列化配置
        JSONWriter.Context context = new JSONWriter.Context(
                JSONWriter.Feature.WriteBigDecimalAsPlain,  // BigDecimal 不使用科学计数法
                JSONWriter.Feature.WriteLongAsString      // 长整型转字符串
//                JSONWriter.Feature.PrettyFormat
        );

        // 设置日期格式
        context.setDateFormat("yyyy-MM-dd");

        return JSON.toJSONString(list, context);
    }


    private void sendMessageType(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String messageType, String clientId) {
        sendData(status, content, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, null, messageType, clientId);
    }


    private void sendMessageType(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, DataInsightTypeEnum dataInsightType, String messageType, String clientId) {
        sendData(status, content, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, dataInsightType, messageType, clientId);
    }


    private void sendData(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId) {
        sendData(status, content, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, null, "", clientId);
    }

    private void sendSqlExecResult(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId, String sqlExecSuccess) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\",\"sqlExecSuccess\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, "", "", clientId, sqlExecSuccess
        );
        SseSession.send(clientId, jsonPayload);
    }

    private void sendHasRecords(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId, String hasRecords) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\",\"hasRecords\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, "", "", clientId, hasRecords
        );
        SseSession.send(clientId, jsonPayload);
    }

    private void sendData(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, DataInsightTypeEnum dataInsightType, String clientId) {
        sendData(status, content, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, dataInsightType, "", clientId);
    }

    private void sendData(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, DataInsightTypeEnum dataInsightType, String messageType, String clientId) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, dataInsightType == null ? "" : dataInsightType.name(), messageType, clientId
        );
        SseSession.send(clientId, jsonPayload);
    }

    public String dataInsightV1Stream(Integer tableId, ModelConfig model, String chatId, String questionId, String question, String suggestion, DataInsightTypeEnum dataInsightType, String jsonData, String clientId, String newQuestionId, String previousResult,Boolean fromDataFoundation) throws RemoteAIException {
        sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, "stream", clientId);
        sendData("", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, clientId);
        StringBuilder resultData = new StringBuilder();
        // 定义同步锁
        CountDownLatch latch = new CountDownLatch(1);
        AIChatLog aiChatLog = new AIChatLog();
        ThreadLocalUtils.set(ChatDatabase.class, "aiChatLog", aiChatLog);
        dataInsightV1Stream(tableId, question, suggestion, jsonData, model, new StreamingResponseHandler<AiMessage>() {
            private final List<String> batch = new ArrayList<>();
            private static final int BATCH_SIZE = 3;

            @Override
            public void onNext(String token) {
                batch.add(token);
                if (batch.size() >= BATCH_SIZE) {
                    sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, clientId);
                    batch.clear();
                }
            }

            @Override
            public void onComplete(Response<AiMessage> complete) {
                try {
                    if (!batch.isEmpty()) {
                        sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", newQuestionId, question, "false", "dataInsight", chatId, dataInsightType, clientId);
                        batch.clear();
                    }
                    sendData("结果生成完成", "", "false", DataTypeEnum.MARKDOWN.getCode(), "true", "false", "false", newQuestionId, question, "true", "dataInsight", chatId, dataInsightType, clientId);
                    resultData.append(complete.content().text());

                    SseSession.complete(clientId);
                } finally {
                    // 释放锁
                    latch.countDown();
                }
            }

            @Override
            public void onError(Throwable error) {
                log.error("对话出现错误：", error);
                try {
                    aiChatLog.setIsSuccess(0);
                    String rootErrorMessage = ExceptionUtil.getRootErrorMessage(error);
                    aiChatLog.setAiResult(rootErrorMessage);
                    sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", rootErrorMessage) : "系统出现异常，请联系系统管理员！", "dataInsight");
                    SseSession.completeWithError(clientId, error);
                } finally {
                    // 释放锁
                    latch.countDown();
                }
            }
        }, previousResult,fromDataFoundation);
        try {
            // 阻塞主线程等待异步完成
            latch.await();
        } catch (InterruptedException e) {
            log.error("阻塞主线程出现异常", e);
            throw new AIServiceException("等待AI返回时出现异常，详情请查看错误日志");
        }
        return resultData.toString();
//        sendData("结果生成完成", "", "false", DataTypeEnum.MARKDOWN.getCode(), "true", "false", "false", newQuestionId, question, "true", "dataInsight", chatId, dataInsightType, clientId);
//        return newQuestionId;
    }

    public void dataInsightV1Stream(Integer tableId, String question, String suggestion, String jsonData, ModelConfig model, StreamingResponseHandler<AiMessage> handler, String previousResult,Boolean fromDataFoundation) throws RemoteAIException {
        boolean suggest = com.dcube.common.utils.StringUtils.isNotEmpty(suggestion);
        String systemPrompt = (suggest ?
                "上一版洞察报告需要完善，请根据提供的一个或多个JSON格式数据集、上一版洞察报告内容、以及用户洞察意见结合历次返工意见（请注意：如果历史返工意见跟最后一次意见有冲突，以最后一次意见为准）更新洞察报告内容，生成分析报告。分析过程采用以下分析框架进行数据的深度探索分析。" :
                "请根据提供的一个或多个JSON格式数据集，结合洞察意见内容，采用以下分析框架进行数据的深度探索分析。") +
                "分析的最终输出不要包含JSON等技术名词，可以用“你提供的数据”来指代“JSON数据”。\n" +
                "分析框架内容以[a_start]开始，以[a_end]结束。" +
                "[a_start]\n" +
                model.getInputExample()
                + "[a_end]";

        String tableStructure;
        if(Objects.isNull(fromDataFoundation) || !fromDataFoundation){
            tableStructure = tableService.getTableStructure(tableId);
        }else{
            tableStructure = iDataFoundationTableService.getTableStructure(tableId);
        }

        String userPrompt = "表结构：" + tableStructure + "\n" +
                "我的问题是：" + com.dcube.common.utils.StringUtils.replace(com.dcube.common.utils.StringUtils.appendIfMissing(question, "。"), "<br />", "") + "\n" +
                (suggest ? "上一版洞察报告内容为：" + com.dcube.common.utils.StringUtils.appendIfMissing(previousResult, "。") : "") + "\n" +
                "JSON数据为:" + com.dcube.common.utils.StringUtils.appendIfMissing(jsonData, "。");
        List<ChatMessage> chatMessageList = new ArrayList<>(2);
        ChatMessage systemMessage = new SystemMessage(systemPrompt);
        chatMessageList.add(systemMessage);
        ChatMessage userMessage = new UserMessage(userPrompt);
        chatMessageList.add(userMessage);
        ThreadLocalUtils.set(ChatDatabase.class, "prompt", chatMessageList.toString());
        StreamingChatLanguageModel streamingChatLanguageModel = AIChatFactory.getAIChatFactory(model).getStreamingChatLanguageModel();
        try {
            streamingChatLanguageModel.generate(chatMessageList, handler);
        } catch (Exception e) {
            log.error("数据洞察时发生异常：", e);
            throw new RemoteAIException("数据洞察时发生异常：" + e.getMessage());
        }
    }

    private Map<String, Object> callApiV1(String systemPrompt, String userPrompt, String key, String prefix, String suffix, ModelConfig model, int retryCount, QuestionTypeEnum questionType, String clientId) throws RemoteAIException {
        return callApiV1(systemPrompt, userPrompt, key, prefix, suffix, model, retryCount, questionType, getModelTemperature(model.getModelName()), clientId);
    }

    private Map<String, Object> callApiV1(String systemPrompt, String userPrompt, String key, String prefix, String suffix, ModelConfig model, int retryCount, QuestionTypeEnum questionType, Double temperature, String clientId) throws RemoteAIException {
        List<ChatMessage> chatMessageList = new ArrayList<>(2);
        ChatMessage systemMessage = new SystemMessage(systemPrompt);
        chatMessageList.add(systemMessage);
        ChatMessage userMessage = new UserMessage(com.dcube.common.utils.StringUtils.replace(userPrompt, "<br />", ""));
        chatMessageList.add(userMessage);
        AIChatLog aiChatLog = new AIChatLog();
        String chatId = ThreadLocalUtils.get(ChatDatabase.class, "chatId");
        String questionId = ThreadLocalUtils.get(ChatDatabase.class, "questionId");
        aiChatLog.setChatId(Long.valueOf(chatId));
        aiChatLog.setQuestionId(Long.valueOf(questionId));
        aiChatLog.setPrompt(chatMessageList.toString());
        aiChatLog.setSceneType(ModelUseSceneEnum.QUERY_CODE);
        aiChatLog.setQuestionType(questionType);
        aiChatLog.setModelConfig(JSON.toJSONString(model));
        try {
            StreamingChatLanguageModel chatLanguageModel = AIChatFactory.getAIChatFactory(model).getStreamingChatLanguageModel(temperature);
            StringBuilder textBuilder = new StringBuilder();
            // 定义同步锁
            CountDownLatch latch = new CountDownLatch(1);
            try {
                chatLanguageModel.generate(chatMessageList, new StreamingResponseHandler<AiMessage>() {
                    @Override
                    public void onNext(String token) {

                    }

                    @Override
                    public void onComplete(Response<AiMessage> response) {
                        try {
                            String text = response.content().text();
                            aiChatLog.setAiResult(text);
                            text = removeThinkContent(text);
                            textBuilder.append(text);
                        } finally {
                            // 释放锁
                            latch.countDown();
                        }
                    }

                    @Override
                    public void onError(Throwable error) {
                        log.error("对话出现错误：", error);
                        try {
                            aiChatLog.setIsSuccess(0);
                            String rootErrorMessage = ExceptionUtil.getRootErrorMessage(error);
                            aiChatLog.setAiResult(rootErrorMessage);
                            sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", rootErrorMessage) : "系统出现异常，请联系系统管理员！", "ask");
                            SseSession.completeWithError(clientId, error);
                        } finally {
                            // 释放锁
                            latch.countDown();
                        }
                    }
                });
            } catch (Exception e) {
                log.error("API调用失败：", e);
                throw new RemoteAIException("API调用失败，详情请查看错误日志");
            }
            try {
                // 阻塞主线程等待异步完成
                latch.await();
            } catch (InterruptedException e) {
                log.error("阻塞主线程出现异常", e);
                throw new AIServiceException("等待AI返回时出现异常，详情请查看错误日志");
            }
            return parseResponse(textBuilder.toString(), key, prefix, suffix);
        } finally {
            aiChatLogService.save(aiChatLog);
        }
    }

    /**
     * 删除think内容
     */
    public static String removeThinkContent(String text) {
        // 使用正则表达式匹配<think>和</think>之间的内容，包括换行符
        Pattern pattern = Pattern.compile("<think>.*?</think>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);
        // 替换所有匹配的内容为空字符串
        text = matcher.replaceAll("");
        text = com.dcube.common.utils.StringUtils.trim(text);
        text = com.dcube.common.utils.StringUtils.removeEnd(text, "。");
        text = com.dcube.common.utils.StringUtils.removeEnd(text, ";");
        return text;
    }

    private Map<String, Object> parseResponse(String text, String key, String prefix, String suffix) {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        result.put("text", text);
        result.put(key, com.dcube.common.utils.StringUtils.defaultIfBlank(StringUtils.extractContentBetween(text, prefix, suffix), com.dcube.common.utils.StringUtils.removeEnd(text, ";")));
        return result;
    }

    public void sendErrorResponse(String clientId, String msg, String questionType) {
        SseSession.send(clientId, String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\"}",
                "", msg, "false", DataTypeEnum.TEXT.getCode(), "true", "true", "false", "", "", "false", questionType, "", ""
        ));
    }

    public void startTextMsg(String questionId, String question, String chatId, String clientId) {
        sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", questionId, question, "false", "ask", chatId, "common", clientId);
    }

    public void sendReworkQuestion(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\",\"reworkFlag\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, "", "", clientId, true
        );
        SseSession.send(clientId, jsonPayload);
    }

    public void sendDataInsightQuestion(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId, DataInsightTypeEnum dataInsightType, boolean reworkFlag) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"dataInsightType\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\",\"dataInsightQuestionFlag\":\"%s\",\"reworkFlag\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, dataInsightType.name(), "", clientId, true, reworkFlag ? "true" : "false"
        );
        SseSession.send(clientId, jsonPayload);
    }

    public void sendMakeChartQuestion(String status, String content, String loading, String dataType, String stopMsg, String display, String retryFlag, String questionId, String question, String hasResult, String questionType, String chatId, String clientId, boolean reworkFlag) {
        String escapedContent = StringUtils.escapeJson(content);
        String jsonPayload = String.format(
                "{\"status\":\"%s\",\"content\":\"%s\",\"loading\":\"%s\",\"dataType\":\"%s\",\"stopMsg\":\"%s\",\"display\":\"%s\",\"retryFlag\":\"%s\",\"questionId\":\"%s\",\"question\":\"%s\",\"hasResult\":\"%s\",\"questionType\":\"%s\",\"chatId\":\"%s\",\"messageType\":\"%s\",\"clientId\":\"%s\",\"dataInsightQuestionFlag\":\"%s\",\"reworkFlag\":\"%s\",\"makeChartFlag\":\"%s\"}",
                status, escapedContent, loading, dataType, stopMsg, display, retryFlag, questionId, question, hasResult, questionType, chatId, "", clientId, false, reworkFlag ? "true" : "false", true
        );
        SseSession.send(clientId, jsonPayload);
    }


    public Map<String, Object> genTableList(String chatId, List<String> chatQuestionIds, ModelConfig modelConfig) {
        List<String> tableList = new ArrayList<>();
        int count = 0;
        int max = (modelConfig.getMaxInputLength() - 20) * 1000;
        StringBuilder chatQuestionAnswer = new StringBuilder();
        List<String> dataInsightQuestionIds = new ArrayList<>();
        for (String questionId : chatQuestionIds) {
            Question chatQuestionByQuestionId = ChatDatabase.getChatQuestionByQuestionId(chatId, questionId);
            if (chatQuestionByQuestionId == null) {
                throw new AIServiceException("未查询到原问题");
            }
            int resultSize;
            int reworkCount;
            String questionIdResult;
            String questionAnswer;
            if (CollectionUtils.isNotEmpty(chatQuestionByQuestionId.getReworkQuestionIds())) {
                reworkCount = chatQuestionByQuestionId.getReworkQuestionIds().size();
                questionIdResult = chatQuestionByQuestionId.getReworkQuestionIds().get(reworkCount - 1);
                Question reworkQuestion = ChatDatabase.getChatQuestionByQuestionId(chatId, questionIdResult);
                if (reworkQuestion == null) {
                    throw new AIServiceException("未查询到返工问题");
                }
                questionAnswer = ChatDatabase.getJsonData(chatId, reworkQuestion.getQuestionId());
            } else {
                questionIdResult = questionId;
                reworkCount = 0;
                questionAnswer = ChatDatabase.getJsonData(chatId, chatQuestionByQuestionId.getQuestionId());
            }
            resultSize = questionAnswer.length();
            count += resultSize;
            if (count > max) {
                break;
            }
            dataInsightQuestionIds.add(questionId);
            chatQuestionAnswer.append(questionAnswer);
            tableList.add(String.format("{\\\"originCommond\\\":\\\"%s\\\", \\\"revertCount\\\":\\\"%s\\\", \\\"resultSize\\\":\\\"%s\\\", \\\"questionId\\\":\\\"%s\\\"}", chatQuestionByQuestionId.getQuestion(), reworkCount, String.format("%.3f", (resultSize / 1000d)) + "k", questionIdResult));
        }
        String questionAnswerString = chatQuestionAnswer.toString();
        if (com.dcube.common.utils.StringUtils.isEmpty(questionAnswerString)) {
            throw new AIServiceException("洞察数据样本超出大模型限制");
        }
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        map.put("tableList", String.format("[%s]", java.lang.String.join(",", tableList)));
        map.put("chatQuestionAnswer", questionAnswerString);
        map.put("dataInsightQuestionIds", dataInsightQuestionIds);
        return map;
    }

    /**
     * 制作图表
     */
    public void makeChart(Integer tableId, ModelConfig model, String chatId, String questionId, String question, String suggestion, String clientId, String chatQuestionAnswer, List<String> dataInsightQuestionIds, String newQuestionId, String previousResult) {
        String resultData = "";
        try {
            sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "makeChart", chatId, "common", clientId);
            sendData("", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "makeChart", chatId, clientId);
            resultData = makeChartStream(tableId, model, chatId, questionId, question, suggestion, chatQuestionAnswer, clientId, newQuestionId, previousResult);
        } catch (Exception e) {
            log.error("处理制作图表流程异常, chatId: {}, questionId: {}", chatId, questionId, e);
            sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", e.getLocalizedMessage()) : "系统出现异常，请联系系统管理员！", "makeChart");
            SseSession.completeWithError(clientId, e);
        } finally {
            // 结果存下来
            String prompt = ThreadLocalUtils.get(ChatDatabase.class, "prompt");
            ChatDatabase.saveChatQuestion(chatId, newQuestionId, resultData, ModelUseSceneEnum.MAKE_CHART, dataInsightQuestionIds, questionId, question, suggestion, JSON.toJSONString(model), prompt, QuestionTypeEnum.MAKE_CHART);
        }
    }

    public String makeChartStream(Integer tableId, ModelConfig model, String chatId, String questionId, String question, String suggestion, String jsonData, String clientId, String newQuestionId, String previousResult) throws RemoteAIException {
        sendMessageType("<start>", "", "false", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "makeChart", chatId, "stream", clientId);
        sendData("", "", "true", DataTypeEnum.TEXT.getCode(), "false", "false", "false", newQuestionId, question, "false", "makeChart", chatId, clientId);
        StringBuilder resultData = new StringBuilder();
        // 定义同步锁
        CountDownLatch latch = new CountDownLatch(1);
        AIChatLog aiChatLog = new AIChatLog();
        ThreadLocalUtils.set(ChatDatabase.class, "aiChatLog", aiChatLog);
        makeChartStream(tableId, question, suggestion, jsonData, model, new StreamingResponseHandler<AiMessage>() {
            private final List<String> batch = new ArrayList<>();
            private static final int BATCH_SIZE = 3;

            @Override
            public void onNext(String token) {
                batch.add(token);
                if (batch.size() >= BATCH_SIZE) {
                    sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", newQuestionId, question, "false", "makeChart", chatId, clientId);
                    batch.clear();
                }
            }

            @Override
            public void onComplete(Response<AiMessage> complete) {
                try {
                    if (!batch.isEmpty()) {
                        sendData("", String.join("", batch), "false", DataTypeEnum.MARKDOWN.getCode(), "false", "true", "false", newQuestionId, question, "false", "makeChart", chatId, clientId);
                        batch.clear();
                    }
                    sendData("结果生成完成", "", "false", DataTypeEnum.MARKDOWN.getCode(), "true", "false", "false", newQuestionId, question, "true", "makeChart", chatId, clientId);
                    resultData.append(complete.content().text());

                    SseSession.complete(clientId);
                } finally {
                    // 释放锁
                    latch.countDown();
                }
            }

            @Override
            public void onError(Throwable error) {
                log.error("对话出现错误：", error);
                try {
                    aiChatLog.setIsSuccess(0);
                    String rootErrorMessage = ExceptionUtil.getRootErrorMessage(error);
                    aiChatLog.setAiResult(rootErrorMessage);
                    sendErrorResponse(clientId, globalExceptionProperties.isShowDetail() ? String.format("在对话时出现错误：%s", rootErrorMessage) : "系统出现异常，请联系系统管理员！", "makeChart");
                    SseSession.completeWithError(clientId, error);
                } finally {
                    // 释放锁
                    latch.countDown();
                }
            }
        }, previousResult);
        try {
            // 阻塞主线程等待异步完成
            latch.await();
        } catch (InterruptedException e) {
            log.error("阻塞主线程出现异常", e);
            throw new AIServiceException("等待AI返回时出现异常，详情请查看错误日志");
        }
        return resultData.toString();
//        sendData("结果生成完成", "", "false", DataTypeEnum.MARKDOWN.getCode(), "true", "false", "false", newQuestionId, question, "true", "makeChart", chatId, clientId);
//        return newQuestionId;
    }

    public void makeChartStream(Integer tableId, String question, String suggestion, String jsonData, ModelConfig model, StreamingResponseHandler<AiMessage> handler, String previousResult) throws RemoteAIException {
        boolean suggest = com.dcube.common.utils.StringUtils.isNotEmpty(suggestion);
        String systemPrompt = (suggest ?
                "上一版Echarts格式代码块需要完善，请根据提供的JSON格式数据集、上一版Echarts格式代码块内容、以及用户制作图表需求内容结合历次返工意见（请注意：如果历史返工意见跟最后一次意见有冲突，以最后一次意见为准）更新Echarts格式代码块，生成可视化分析图表。" :
                "根据提供的JSON格式数据集，结合制作图表需求内容，") +
                "采用以下分析框架制作标准Echarts格式代码块的分析图表，分析框架内容以[a_echart_start]开始，以[a_echart_end]结束。" +
                "[a_echart_start]\n" +
                model.getInputExample()
                + "[a_echart_end]。\n" +
                "分析的最终输出不要包含JSON等技术名词，可以用“你提供的数据”来指代“JSON数据”。\n";
        String userPrompt = "我的问题是：" + com.dcube.common.utils.StringUtils.replace(com.dcube.common.utils.StringUtils.appendIfMissing(question, "。"), "<br />", "") + "\n" +
                (suggest ? "上一版Echarts格式代码块内容为：" + com.dcube.common.utils.StringUtils.appendIfMissing(previousResult, "。") : "") + "\n" +
                "JSON数据为:" + com.dcube.common.utils.StringUtils.appendIfMissing(jsonData, "。");
        List<ChatMessage> chatMessageList = new ArrayList<>(2);
        ChatMessage systemMessage = new SystemMessage(systemPrompt);
        chatMessageList.add(systemMessage);
        ChatMessage userMessage = new UserMessage(userPrompt);
        chatMessageList.add(userMessage);
        ThreadLocalUtils.set(ChatDatabase.class, "prompt", chatMessageList.toString());
        StreamingChatLanguageModel streamingChatLanguageModel = AIChatFactory.getAIChatFactory(model).getStreamingChatLanguageModel();
        try {
            streamingChatLanguageModel.generate(chatMessageList, handler);
        } catch (Exception e) {
            log.error("制作图表时发生异常：", e);
            throw new RemoteAIException("制作图表时发生异常：" + e.getMessage());
        }
    }

    public String getDfConditionsInputExample(String filterList) {
        if(StrUtil.isEmpty(filterList)){
            return StrUtil.EMPTY;
        }
        //[{"columnCode":"baseDataDtStart","value":"2025-06-10","prefixVal":"-1","storageType":"TIMESTAMP","op":"and"},
        // {"columnCode":"baseDataDtEnd","value":"2025-06-10","prefixVal":"-1","storageType":"TIMESTAMP","op":"and"},
        // {"columnCode":"dateInterval","value":"DAY","prefixVal":"-1","storageType":"VARCHAR","op":"and"},
        // {"columnCode":"version","value":"备份1","prefixVal":"-1","storageType":"VARCHAR","op":"and"}]
        return "\n 1、基础查询条件数据和结构为："+filterList+"" +
                "\n (1)、当基础条件中的baseDataDtStart的value值不为空时，添加base_data_dt>= columnCode为baseDataDtStart的value值。" +
                "\n (2)、当基础条件中的baseDataDtEnd的value值不为空时，添加base_data_dt>= columnCode为baseDataDtEnd的value值。" +
                "\n (3)、当基础条件中dateInterval的value值不为空时，其中value值的取值范围时：DAY、MONTH、QUARTER、YEAR，" +
                        "MONTH表示需要取上面base_data_dt查询出来所有月份的最后一天的数据，" +
                        "QUARTER表示需要取上面base_data_dt查询出来所有月份所在的季度的最后一天的数据，" +
                        "YEAR表示需要取上面base_data_dt查询出来所有日期所在年的最后一天，" +
                        "DAY不作任何操作，以上MONTH、QUARTER、YEAR都是对base_data_dt字段作为查询条件。" +
                "\n (4)、当基础条件中的version的value值不为空时，添加version=columnCode为version的value值。" +
                "\n 以上是查询的基础条件，需要在生成sql时带上这些条件。";

    }
}
