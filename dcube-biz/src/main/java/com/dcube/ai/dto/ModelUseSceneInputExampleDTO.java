package com.dcube.ai.dto;

import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class ModelUseSceneInputExampleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 输入示例
     */
    @Schema(description = "输入示例")
    private String inputExample;

    @Schema(description = "数据底表输入示例")
    private String dfInputExample;

    /**
     * 模型使用场景
     */
    @Schema(description = "模型使用场景")
    private ModelUseSceneEnum modelUseScene;

}
