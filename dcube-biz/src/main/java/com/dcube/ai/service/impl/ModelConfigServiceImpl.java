package com.dcube.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.ai.constants.enums.ModelTypeEnum;
import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import com.dcube.ai.domain.ModelConfig;
import com.dcube.ai.domain.ModelSceneSetting;
import com.dcube.ai.dto.ModelConfigDTO;
import com.dcube.ai.dto.ModelUseSceneInputExampleDTO;
import com.dcube.ai.dto.ModelUseSceneSetDTO;
import com.dcube.ai.factory.AIChatFactory;
import com.dcube.ai.mapper.ModelConfigMapper;
import com.dcube.ai.service.IModelConfigService;
import com.dcube.ai.service.IModelSceneSettingService;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.system.domain.SysConfig;
import com.dcube.system.service.ISysConfigService;
import com.google.common.collect.Maps;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ModelConfigServiceImpl extends ServiceImpl<ModelConfigMapper, ModelConfig> implements IModelConfigService {

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private IModelSceneSettingService modelSceneSettingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ModelConfigDTO dto) {
        validateModelConfigDTO(dto);
        // 模型名重复检查
        ModelConfig exist = this.getOne(Wrappers.<ModelConfig>lambdaQuery()
                .eq(ModelConfig::getModelName, dto.getModelName())
        );
        if (exist != null) {
            throw new ServiceException("模型【" + dto.getModelName() + "】名称不能重复");
        }
        ModelConfig modelConfig = new ModelConfig();
        BeanUtils.copyProperties(dto, modelConfig);
        this.save(modelConfig);
    }

    private void validateModelConfigDTO(ModelConfigDTO dto) {
        Assert.notNull(dto.getContextLength(), "上下文长度不能为空");
        Assert.notNull(dto.getMaxInputLength(), "最大输入不能为空");
        Assert.isTrue(dto.getContextLength() > 20, "上下文长度需要大于20");
        Assert.isTrue(dto.getMaxInputLength() > 20, "最大输入需要大于20");
        Assert.isTrue(dto.getContextLength() >= dto.getMaxInputLength(), "上下文长度需要大于最大输入");
        int maxTokens = dto.getContextLength() - dto.getMaxInputLength();
        Assert.isTrue(maxTokens >= 1, "“上下文长度”减去“最大输入”不得小于1K");
        Assert.isTrue(maxTokens <= 8, "“上下文长度”减去“最大输入”不得大于8K");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ModelConfigDTO dto) {
        Assert.notNull(dto.getId(), "id不能为空");
        validateModelConfigDTO(dto);
        // 模型名重复检查
        ModelConfig dbModelConfig = this.getById(dto.getId());
        if (dbModelConfig == null) {
            return;
        }
        if (!StringUtils.equals(dbModelConfig.getModelName(), dto.getModelName())) {
            // 模型名重复检查
            ModelConfig exist = this.getOne(Wrappers.<ModelConfig>lambdaQuery()
                    .eq(ModelConfig::getModelName, dto.getModelName())
                    .ne(ModelConfig::getId, dto.getId())
            );
            if (exist != null) {
                throw new ServiceException("模型【" + dto.getModelName() + "】名称不能重复");
            }
        }
        ModelConfig modelConfig = new ModelConfig();
        BeanUtils.copyProperties(dto, modelConfig);
        this.updateById(modelConfig);
    }

    @Override
    public List<Map<String, Object>> queryModelType() {
        return Arrays.stream(ModelTypeEnum.values()).map(modelTypeEnum -> {
            Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
            map.put("type", modelTypeEnum.name());
            map.put("desc", modelTypeEnum.getDesc());
            map.put("needUrl", modelTypeEnum.isNeedUrl());
            return map;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> queryUseScene() {
        List<ModelUseSceneEnum> modelUseSceneEnums = Arrays.asList(ModelUseSceneEnum.values());
        List<ModelUseSceneEnum> modelUseSceneEnumsRemove = new ArrayList<>(modelUseSceneEnums);

        List<Long> modelIds = new ArrayList<>(modelUseSceneEnums.size());
        Map<String, Long> modelConfigMap = Maps.newHashMapWithExpectedSize(modelUseSceneEnums.size());
        List<ModelSceneSetting> modelSceneSettings = modelSceneSettingService.list(Wrappers.<ModelSceneSetting>lambdaQuery()
                .in(ModelSceneSetting::getSceneType, StreamUtils.toList(modelUseSceneEnums, ModelUseSceneEnum::getType))
                .and(wrapper -> wrapper.isNotNull(ModelSceneSetting::getModelId))
        );
        if (CollectionUtils.isNotEmpty(modelSceneSettings)) {
            Map<ModelUseSceneEnum, Long> modelUseSceneMap = StreamUtils.toMap(modelSceneSettings, ModelSceneSetting::getSceneType, ModelSceneSetting::getModelId);
            modelUseSceneEnumsRemove.removeIf(modelUseSceneMap::containsKey);
            modelIds.addAll(modelUseSceneMap.values());
            for (Map.Entry<ModelUseSceneEnum, Long> entry : modelUseSceneMap.entrySet()) {
                modelConfigMap.put(entry.getKey().name(), entry.getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(modelUseSceneEnumsRemove)) {
            List<SysConfig> sysConfigs = sysConfigService.list(Wrappers.<SysConfig>lambdaQuery()
                    .in(SysConfig::getConfigKey, StreamUtils.toList(modelUseSceneEnumsRemove, ModelUseSceneEnum::name))
                    .and(wrapper -> wrapper.isNotNull(SysConfig::getConfigValue).ne(SysConfig::getConfigValue, ""))
            );
            if (CollectionUtils.isNotEmpty(sysConfigs)) {
                Map<String, Long> configMap = StreamUtils.toMap(sysConfigs, SysConfig::getConfigKey, sysConfig -> Long.valueOf(sysConfig.getConfigValue()));
                modelIds.addAll(configMap.values());
                modelConfigMap.putAll(configMap);
            }
        }
        List<ModelConfig> list;
        if (CollectionUtils.isEmpty(modelIds)) {
            list = Collections.emptyList();
        } else {
            list = this.listByIds(modelIds);
        }
        Map<Long, ModelConfig> sceneEnumModelConfigMap = StreamUtils.toMap(list, ModelConfig::getId, Function.identity());
        List<Map<String, Object>> result = new ArrayList<>(modelUseSceneEnums.size());
        for (ModelUseSceneEnum modelUseSceneEnum : modelUseSceneEnums) {
            Map<String, Object> map = Maps.newLinkedHashMapWithExpectedSize(3);
            map.put("code", modelUseSceneEnum.name());
            map.put("data", MapUtils.getObject(sceneEnumModelConfigMap, MapUtils.getObject(modelConfigMap, modelUseSceneEnum.name())));
            map.put("name", modelUseSceneEnum.getDesc());
            result.add(map);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setUseScene(ModelUseSceneSetDTO dto) {
        Assert.notNull(dto.getId(), "id不能为空");
        modelSceneSettingService.setUseScene(dto);
    }

    @Override
    public ModelConfig getModelConfigByUseScene(ModelUseSceneEnum modelUseScene) {
        ModelSceneSetting modelSceneSetting = modelSceneSettingService.queryModelUseSceneSetting(modelUseScene);
        if (modelSceneSetting == null || modelSceneSetting.getModelId() == null) {
            return null;
        }
        ModelConfig modelConfig = this.getById(modelSceneSetting.getModelId());
        if (modelConfig == null) {
            return null;
        }
        modelConfig.setInputExample(modelSceneSetting.getInputExample());
        modelConfig.setDfInputExample(modelSceneSetting.getDfInputExample());
        return modelConfig;
    }

    @Override
    public AjaxResult test(ModelConfigDTO dto) {
        ModelConfig modelConfig;
        if (Objects.nonNull(dto.getId())) {
            modelConfig = this.getById(dto.getId());
            Assert.notNull(modelConfig, "未查询到模型");
        } else {
            modelConfig = new ModelConfig();
        }
        BeanUtils.copyProperties(dto, modelConfig);
        StreamingChatLanguageModel chatLanguageModel = AIChatFactory.getAIChatFactory(modelConfig).getStreamingChatLanguageModel();
        List<ChatMessage> chatMessageList = new ArrayList<>(1);
        ChatMessage userMessage = new UserMessage(dto.getTestQuestion());
        chatMessageList.add(userMessage);
        // 定义同步锁
        CountDownLatch latch = new CountDownLatch(1);
        AtomicBoolean errorFlag = new AtomicBoolean(false);
        StringBuilder result = new StringBuilder();
        chatLanguageModel.generate(chatMessageList, new StreamingResponseHandler<AiMessage>() {
            @Override
            public void onNext(String token) {
            }

            @Override
            public void onComplete(Response<AiMessage> response) {
                try {
                    result.append(response.content().text());
                } finally {
                    // 释放锁
                    latch.countDown();
                }
            }

            @Override
            public void onError(Throwable error) {
                try {
                    log.error("模型调用出现异常", error);
                    errorFlag.compareAndSet(false, true);
                } finally {
                    // 确保异常时也释放锁
                    latch.countDown();
                }
            }
        });
        try {
            // 阻塞主线程等待异步完成
            latch.await();
            if (errorFlag.get()) {
                return AjaxResult.error("模型调用失败");
            } else {
                return AjaxResult.success(result);
            }
        } catch (InterruptedException e) {
            log.error("阻塞主线程出现异常", e);
            return AjaxResult.error("模型调用出现异常，详情请查看错误日志");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUseSceneInputExample(ModelUseSceneInputExampleDTO dto) {
        ModelSceneSetting modelSceneSetting = modelSceneSettingService.queryModelUseSceneSetting(dto.getModelUseScene());
        if (modelSceneSetting == null) {
            modelSceneSetting = new ModelSceneSetting();
        }
        modelSceneSetting.setInputExample(dto.getInputExample());
        modelSceneSetting.setDfInputExample(dto.getDfInputExample());
        modelSceneSettingService.saveOrUpdate(modelSceneSetting);
    }

}
