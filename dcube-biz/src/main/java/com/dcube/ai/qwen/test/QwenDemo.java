package com.dcube.ai.qwen.test;

import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/20 14:09
 * @描述
 */
public class QwenDemo {

    public static String API_KEY = "sk-f8b6b84ab4b545959ed29b54cf9074d8";

    public static String API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";




    static class Message {
        String role;
        String content;

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }

    static class RequestBody {
        String model;
        Message[] messages;

        public RequestBody(String model, Message[] messages) {
            this.model = model;
            this.messages = messages;
        }
    }

    public static void main(String[] args) {
        try {
            String question = "{\n" +
                    "  \"schema\" : {\n" +
                    "    \"tables\" : [ {\n" +
                    "      \"table_name\" : \"users\",\n" +
                    "      \"columns\" : [ {\n" +
                    "        \"name\" : \"id\",\n" +
                    "        \"type\" : \"INT\",\n" +
                    "        \"comment\" : \"主键\"\n" +
                    "      }, {\n" +
                    "        \"name\" : \"name\",\n" +
                    "        \"type\" : \"VARCHAR(50)\",\n" +
                    "        \"comment\" : \"用户名\"\n" +
                    "      }, {\n" +
                    "        \"name\" : \"created_at\",\n" +
                    "        \"type\" : \"DATETIME\",\n" +
                    "        \"comment\" : \"创建时间\"\n" +
                    "      } ]\n" +
                    "    }, {\n" +
                    "      \"table_name\" : \"orders\",\n" +
                    "      \"columns\" : [ {\n" +
                    "        \"name\" : \"order_id\",\n" +
                    "        \"type\" : \"BIGINT\",\n" +
                    "        \"comment\" : \"订单ID\"\n" +
                    "      }, {\n" +
                    "        \"name\" : \"user_id\",\n" +
                    "        \"type\" : \"INT\",\n" +
                    "        \"comment\" : \"用户ID\"\n" +
                    "      }, {\n" +
                    "        \"name\" : \"amount\",\n" +
                    "        \"type\" : \"DECIMAL(10,2)\",\n" +
                    "        \"comment\" : \"订单金额\"\n" +
                    "      } ]\n" +
                    "    } ]\n" +
                    "  },\n" +
                    "  \"requirement\" : \"生成最近7天注册用户的订单总金额，按金额降序排列\",只生成sql，不需要解释和注释\n" +
                    "  \"dialect\" : \"MySQL\",\n" +
                    "  \"settings\" : {\n" +
                    "    \"strict_mode\" : true,\n" +
                    "    \"enable_join_detection\" : true\n" +
                    "  }\n" +
                    "}";

            // 创建请求体
            RequestBody requestBody = new RequestBody(
                    "qwen-plus",
                    new Message[] {
                            new Message("system", question),
//                            new Message("system", "You are a helpful assistant."),
//                            new Message("user", "你是谁？")
                    }
            );

            // 将请求体转换为 JSON
            Gson gson = new Gson();
            String jsonInputString = gson.toJson(requestBody);

            // 创建 URL 对象
            URL url = new URL(API_URL);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 POST
            httpURLConnection.setRequestMethod("POST");
            httpURLConnection.setRequestProperty("Content-Type", "application/json; utf-8");
            httpURLConnection.setRequestProperty("Accept", "application/json");

            // 若没有配置环境变量，请用百炼API Key将下行替换为：String apiKey = "sk-xxx";
            String auth = "Bearer " + API_KEY;
            httpURLConnection.setRequestProperty("Authorization", auth);

            // 启用输入输出流
            httpURLConnection.setDoOutput(true);

            // 写入请求体
            try (OutputStream os = httpURLConnection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 获取响应码
            int responseCode = httpURLConnection.getResponseCode();
            System.out.println("正在向阿里千问提问: " + question);

            // 读取响应体
            try (BufferedReader br = new BufferedReader(new InputStreamReader(httpURLConnection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                System.out.println("回答: " + response);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.exit(0);
        }
    }
}
