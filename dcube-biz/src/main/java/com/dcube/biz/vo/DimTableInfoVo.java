package com.dcube.biz.vo;

import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimTable;
import com.dcube.biz.domain.Ind;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DimTableInfoVo extends DimTable {

    /**
     * 关联指标对象集合
     */
    @Schema(description = "关联指标对象集合(实例)")
    private List<Ind> indList;
    /**
     * 关联指标对象集合
     */
    @Schema(description = "关联指标对象集合(分组)")
    private List<Ind> indGroupList;

    /**
     * 关联维度对象集合
     */
    @Schema(description = "关联维度对象集合")
    private List<DimDirectory> dimList;

    /**
     * 默认行维
     */
    @Schema(description = "默认行维")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer defaultDimId;

    /**
     * 默认列维
     */
    @Schema(description = "默认列维")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer defaultColumnDimId;

    /**
     * 维度表布局
     */
    @Schema(description = "维度表布局")
    private LayoutVO layout;
}
