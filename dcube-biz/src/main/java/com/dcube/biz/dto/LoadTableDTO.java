package com.dcube.biz.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class LoadTableDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "二维表Id")
    @NotNull(message = "二维表Id不能为空")
    private Integer id;

    @Schema(description = "数据视图Id")
    private String viewId;

    private List<TableDataLoadConfigDTO> tableDataLoadConfigs;


}
