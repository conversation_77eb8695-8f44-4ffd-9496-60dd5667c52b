package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.biz.constant.enums.TableBackupColumnTypeEnum;
import com.dcube.biz.constant.enums.TableBackupConfigTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("cube_2d_table_backup_config")
@EqualsAndHashCode(callSuper = true)
public class TableBackupConfig extends Model<TableBackupConfig> implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 二维表Id
     */
    @Schema(description = "二维表Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 备份表名前缀
     */
    @Schema(description = "备份表名前缀")
    private String backupTableNamePrefix;

    /**
     * 备份表名
     */
    @Schema(description = "备份表名")
    private String backupTableName;

    /**
     * 配置类型
     */
    @Schema(description = "配置类型")
    private TableBackupConfigTypeEnum configType;

    /**
     * 列名
     */
    @Schema(description = "列名")
    @TableField(exist = false)
    private String columnName;

    /**
     * 列编码
     */
    @Schema(description = "列编码")
    private String columnCode;

    /**
     * 备份字段名
     */
    @Schema(description = "备份字段名")
    private String backupColumnName;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    private TableBackupColumnTypeEnum columnType;

    /**
     * 字段长度
     */
    @Schema(description = "字段长度")
    private Integer columnLength;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否已存在该列
     */
    @TableField(exist = false)
    private boolean isColumnExist;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
