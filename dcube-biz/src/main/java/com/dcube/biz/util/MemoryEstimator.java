package com.dcube.biz.util;

import cn.hutool.core.date.StopWatch;
import lombok.extern.slf4j.Slf4j;

import java.lang.instrument.Instrumentation;
import java.lang.reflect.Array;

/**
 * @创建人 zhouhx
 * @创建时间 2024/3/29 11:01
 * @描述
 */
@Slf4j
public class MemoryEstimator {

    private static Instrumentation instrumentation;

    public static void premain(String agentArgs, Instrumentation inst) {
        instrumentation = inst;
    }

    public static long estimateMultiDimensionalArrayMemoryUsage(Object multiDimArray) {
        StopWatch stopWatch = null;
        if (log.isDebugEnabled()) {
            stopWatch = new StopWatch();
            stopWatch.start();
        }

        if (multiDimArray == null) {
            return 0;
        }

        Class<?> componentType = multiDimArray.getClass().getComponentType();

        if (!componentType.isArray()) {
            throw new IllegalArgumentException("Input must be a multidimensional array");
        }

        int outerLength = Array.getLength(multiDimArray);

        long totalSize = ObjectHeaderSize + Integer.BYTES; // 外层数组对象头与长度信息

        for (int i = 0; i < outerLength; i++) {
            Object innerArray = Array.get(multiDimArray, i);
            totalSize += estimateSingleDimensionArrayMemoryUsage(innerArray);
        }
        if (log.isDebugEnabled() && stopWatch != null) {
            stopWatch.stop();
            log.debug("estimateMultiDimensionalArrayMemoryUsage cost time:{}", stopWatch.getTotalTimeSeconds());
        }
        return totalSize;
    }

    public static long estimateSingleDimensionArrayMemoryUsage(Object array) {
        if (array == null) {
            return 0;
        }
        if (instrumentation != null) {
            return instrumentation.getObjectSize(array);
        } else {
            Class<?> componentType = array.getClass().getComponentType();

            if (componentType.isPrimitive()) {
                int length = Array.getLength(array);
                int primitiveSize = getPrimitiveSize(componentType);
                return ObjectHeaderSize + Integer.BYTES + (long) length * primitiveSize;
            } else {
                int length = Array.getLength(array);
                return ObjectHeaderSize + Integer.BYTES + (long) length * ReferenceSize;
            }
        }
    }

    private static final int ObjectHeaderSize = 24/* 这里应填入具体的JVM上的对象头大小 */;
    private static final int ReferenceSize = 20/* 在64位JVM上，开启指针压缩通常是4字节 */;

    private static int getPrimitiveSize(Class<?> primitiveType) {
        if (primitiveType == boolean.class) {
            return Byte.BYTES;
        } else if (primitiveType == byte.class || primitiveType == short.class || primitiveType == char.class) {
            return Integer.BYTES; // 在某些JVM实现中，可能是机器字长
        } else if (primitiveType == int.class || primitiveType == float.class) {
            return Integer.BYTES * 2;
        } else if (primitiveType == long.class || primitiveType == double.class) {
            return Integer.BYTES * 4;
        } else {
            throw new IllegalArgumentException("Not a primitive type");
        }
    }
}
