package com.dcube.biz.util;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Semaphore;

public class KeyLock<K> {

    private final ConcurrentMap<K, Semaphore> map = new ConcurrentHashMap<>();

    private final ThreadLocal<Map<K, LockInfo>> local = ThreadLocal.withInitial(HashMap::new);

    public boolean tryLock(K key) {
        if (key == null) {
            return false;
        }
        Semaphore hit = map.get(key);
        if (hit == null) {
            this.lock(key);
            return true;
        } else {
            return false;
        }
    }

    public void lock(K key) {
        if (key == null) {
            return;
        }
        LockInfo info = local.get().get(key);
        if (info == null) {
            Semaphore current = new Semaphore(1);
            current.acquireUninterruptibly();
            Semaphore previous = map.put(key, current);
            if (previous != null) {
                previous.acquireUninterruptibly();
            }
            local.get().put(key, new LockInfo(current));
        } else {
            info.lockCount++;
        }
    }

    public void unlock(K key) {
        if (key == null) {
            return;
        }
        LockInfo info = local.get().get(key);
        if (info != null && --info.lockCount == 0) {
            info.current.release();
            map.remove(key, info.current);
            local.get().remove(key);
        }
        if (info == null) {
            map.remove(key);
        }
    }

    public void lock(K[] keys) {
        if (keys == null) {
            return;
        }
        for (K key : keys) {
            lock(key);
        }
    }

    public void unlock(K[] keys) {
        if (keys == null) {
            return;
        }
        for (K key : keys) {
            unlock(key);
        }
    }

    private static class LockInfo {
        private final Semaphore current;
        private int lockCount;

        private LockInfo(Semaphore current) {
            this.current = current;
            this.lockCount = 1;
        }
    }
}