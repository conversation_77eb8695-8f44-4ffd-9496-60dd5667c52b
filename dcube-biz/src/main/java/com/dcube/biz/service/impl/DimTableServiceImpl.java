package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.constant.enums.DimTypeEnum;
import com.dcube.biz.domain.*;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.DimTableMapper;
import com.dcube.biz.query.DimTableDataQuery;
import com.dcube.biz.query.DimTableInstanceQuery;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.service.*;
import com.dcube.biz.vo.*;
import com.dcube.common.annotation.ThreadLocalCache;
import com.dcube.common.aspect.ThreadLocalCacheAspect;
import com.dcube.common.constant.enums.StateEnum;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.enums.ThreadLocalCacheType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.bean.BeanUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.common.utils.uuid.IdUtils;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.math.DoubleDouble;
import com.dcube.cube.spi.CubeMetaData;
import com.dcube.cube.spi.CubeSchema;
import com.dcube.cube.spi.CubeServer;
import com.dcube.rule.cube.constants.CubeRuleConstant;
import com.dcube.rule.cube.service.IDimRuleService;
import com.dcube.rule.cube.service.IDimRuleServiceHelperService;
import com.dcube.rule.grid.operator.OPCTOperator;
import com.dcube.tran.plugin.CubeGridReader;
import com.dcube.tran.plugin.CubeRdmsWriter;
import com.dcube.tran.task.TaskSingleContainer;
import com.dcube.workflow.define.constants.enums.WfDefineFlowTypeEnums;
import com.dcube.workflow.define.service.IWfDefineService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class DimTableServiceImpl extends ServiceImpl<DimTableMapper, DimTable> implements IDimTableService {

    @Autowired
    private IIndService indService;
    @Autowired
    private IDimTableRelService dimTableRelService;
    @Autowired
    private IDimDirectoryService dimDirectoryService;
    @Autowired
    private IDimInstanceService dimInstanceService;
    @Autowired
    private IGroupInstanceService groupInstanceService;
    @Autowired
    private ITableService tableService;
    @Autowired
    private IDimDimRelService dimDimRelService;
    @Autowired
    private IDimRuleService dimRuleService;
    @Autowired
    private IDimRuleServiceHelperService dimRuleServiceHelperService;
    @Autowired
    private IDimLayoutService dimLayoutService;
    @Autowired
    private IWfDefineService wfDefineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void layout(LayoutDto layoutDto) {
        if (layoutDto.getTableId() == null || Objects.isNull(layoutDto.getUserId())) {
            throw new ServiceException("多维表布局表主键或用户主键不能为空。");
        }
        if (CollectionUtil.isEmpty(layoutDto.getRowDimList()) || CollectionUtil.isEmpty(layoutDto.getColumnDimList())) {
            throw new ServiceException("多维表布局行维度或列维度不能为空。");
        }

        // rel
        dimLayoutService.remove(Wrappers.<DimLayout>lambdaQuery().eq(DimLayout::getTableId, layoutDto.getTableId()).eq(DimLayout::getUserId, layoutDto.getUserId()));

        DimLayout dimLayout = new DimLayout();
        dimLayout.setTableId(layoutDto.getTableId());
        dimLayout.setUserId(layoutDto.getUserId());
        dimLayout.setRowDim(JSON.toJSONString(layoutDto.getRowDimList()));
        dimLayout.setColumnDim(JSON.toJSONString(layoutDto.getColumnDimList()));
        dimLayout.setFilterDim(JSON.toJSONString(layoutDto.getFilterDimList()));
        dimLayoutService.save(dimLayout);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveExt(DimTableDto dimTableDto) {
        if ("INSTANCE".equals(dimTableDto.getType())) {
            if (CollectionUtil.isEmpty(dimTableDto.getDimIdList())) {
                throw new ServiceException("数方未关联维度。");
            }
            if (CollectionUtil.isEmpty(dimTableDto.getIndList())) {
                throw new ServiceException("数方未关联指标。");
            }
            if (dimTableDto.getDefaultDimId() == null || dimTableDto.getDefaultDimId() <= 0) {
                throw new ServiceException("数方未设置默认行维。");
            }
            // 校验指标和维度的名称不能重复
            List<DimDirectory> dimDirectories = dimDirectoryService.listByIds(dimTableDto.getDimIdList());
            Assert.notEmpty(dimDirectories, "未查询到关联的全部维度");
            Assert.isTrue(dimDirectories.size() == dimTableDto.getDimIdList().size(), "关联维度的查询结果不一致");
            Set<String> dimDirectoryNameSet = StreamUtils.toSet(dimDirectories, DimDirectory::getDimDirectoryName);
            for (IndDto indDto : dimTableDto.getIndList()) {
                if (dimDirectoryNameSet.contains(indDto.getIndName())) {
                    throw new ServiceException("维度【" + indDto.getIndName() + "】和指标【" + indDto.getIndName() + "】重名！");
                }
            }
        }

        DimTable entity = new DimTable();
        BeanUtils.copyProperties(dimTableDto, entity);

        // 验证名称或代码
        long count = this.count(entity);
        if (count > 0) {
            log.error("数方名称[{}]已存在。", entity.getTableName());
            throw new ServiceException(String.format("数方名称[%s]已存在。", entity.getTableName()));
        }

        // 兼容设置默认根根代码
        if (entity.getParentId() == null) {
            entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
        }

        this.save(entity);

        if ("INSTANCE".equals(dimTableDto.getType())) {
            // group id map
            Map<Integer, Integer> groupIdMap = new HashMap<>();
            Map<Integer, Ind> groupIndMap = new HashMap<>();
            Map<String, Integer> instanceIndMap = new HashMap<>();
            // group type
            int indexNo = 1;
            // list ind
            List<Ind> indEntityList = new ArrayList<>();
            List<Ind> indGroupEntityList = new ArrayList<>();
            for (IndDto indDto : dimTableDto.getIndList()) {
                Ind indEntity = new Ind();
                BeanUtils.copyProperties(indDto, indEntity);
                // record insert id
                indEntity.setId(null);
                indEntity.setIndexNo(indexNo);
                indEntity.setLevelNumber(1);
                if (indEntity.getParentId() != null && indEntity.getParentId() > 0) {
                    indEntity.setLevelNumber(groupIndMap.get(indEntity.getParentId()).getLevelNumber() + 1);
                    indEntity.setParentId(groupIdMap.get(indEntity.getParentId()));
                }
                indService.save(indEntity);
                if ("GROUP".equals(indDto.getIndType())) {
                    groupIdMap.put(indDto.getId(), indEntity.getId());
                    groupIndMap.put(indDto.getId(), indEntity);
                    indGroupEntityList.add(indEntity);
                } else {
                    instanceIndMap.put(indDto.getId().toString(), indEntity.getId());
                    indEntityList.add(indEntity);
                }
                indexNo++;
            }

            // support avg parma ind id
            for (Ind indEntity : indEntityList) {
                if (StringUtils.isEmpty(indEntity.getAvgParmaIndId())) {
                    continue;
                }
                indEntity.setAvgParmaIndId(instanceIndMap.get(indEntity.getAvgParmaIndId()).toString());
                indService.updateById(indEntity);
            }

            // save rel
            List<DimTableRel> dimTableRelList = new ArrayList<>();
            for (Integer dimId : dimTableDto.getDimIdList()) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(dimId);
                dimTableRelEntity.setRelType("dim");
                if (dimTableDto.getDefaultDimId() != null && dimTableDto.getDefaultDimId().intValue() == dimId) {
                    dimTableRelEntity.setIsDefaultDim(BizConstants.FLAG_TRUE);
                }
                if (dimTableDto.getDefaultColumnDimId() != null && dimTableDto.getDefaultColumnDimId().intValue() == dimId) {
                    dimTableRelEntity.setIsDefaultColumnDim(BizConstants.FLAG_TRUE);
                } else {
                    dimTableRelEntity.setIsDefaultColumnDim(BizConstants.FLAG_FALSE);
                }
                dimTableRelList.add(dimTableRelEntity);
            }
            for (Ind ind : indGroupEntityList) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(ind.getId());
                dimTableRelEntity.setRelType("ind-group");
                dimTableRelList.add(dimTableRelEntity);
            }
            for (Ind ind : indEntityList) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(ind.getId());
                dimTableRelEntity.setRelType("ind");
                dimTableRelList.add(dimTableRelEntity);
            }
            dimTableRelService.saveBatch(dimTableRelList);
        }
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateExt(DimTableDto dimTableDto) {
        if ("INSTANCE".equals(dimTableDto.getType())) {
            if (CollectionUtil.isEmpty(dimTableDto.getDimIdList())) {
                throw new ServiceException("数方未关联维度。");
            }
            if (CollectionUtil.isEmpty(dimTableDto.getIndList())) {
                throw new ServiceException("数方未关联指标。");
            }
            if (dimTableDto.getDefaultDimId() == null || dimTableDto.getDefaultDimId() <= 0) {
                throw new ServiceException("数方未设置默认行维。");
            }
            // 校验指标和维度的名称不能重复
            List<DimDirectory> dimDirectories = dimDirectoryService.listByIds(dimTableDto.getDimIdList());
            Assert.notEmpty(dimDirectories, "未查询到关联的全部维度");
            Assert.isTrue(dimDirectories.size() == dimTableDto.getDimIdList().size(), "关联维度的查询结果不一致");
            Set<String> dimDirectoryNameSet = StreamUtils.toSet(dimDirectories, DimDirectory::getDimDirectoryName);
            for (IndDto indDto : dimTableDto.getIndList()) {
                if (dimDirectoryNameSet.contains(indDto.getIndName())) {
                    throw new ServiceException("维度【" + indDto.getIndName() + "】和指标【" + indDto.getIndName() + "】重名！");
                }
            }
        }

        DimTable entity = new DimTable();
        BeanUtils.copyProperties(dimTableDto, entity);

        // 验证名称或代码
        long count = this.count(entity);
        if (count > 0) {
            log.error("数方名称[{}]已存在。", entity.getTableName());
            throw new ServiceException(String.format("数方名称[%s]已存在。", entity.getTableName()));
        }

        // 兼容设置默认根根代码
        if (entity.getParentId() == null) {
            entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
        }

        this.updateById(entity);
        if ("INSTANCE".equals(dimTableDto.getType())) {
            // 是否发生了上下移
            DimTableInfoVo dimTableInfoVo = this.getByIdExt(entity.getId());
            // 原始指标list
            List<Ind> originIndList = dimTableInfoVo.getIndList();

            // query table ind
            List<DimTableRel> relIndList = dimTableRelService.list(new QueryWrapper<DimTableRel>().lambda().eq(DimTableRel::getTableId, entity.getId()).in(DimTableRel::getRelType, Arrays.asList("ind", "ind-group")));
            Set<Integer> indIdSet = relIndList.stream().map(DimTableRel::getRelId).collect(Collectors.toSet());
            // delete rel
            dimTableRelService.remove(new QueryWrapper<DimTableRel>().lambda().eq(DimTableRel::getTableId, entity.getId()));

            // group id map
            Map<Integer, Integer> groupIdMap = new HashMap<>();
            Map<Integer, Ind> groupIndMap = new HashMap<>();
            Map<String, Integer> instanceIndMap = new HashMap<>();
            List<Integer> addIndIdList = new ArrayList<>();
            // group type
            int indexNo = 1;
            // list ind
            List<Ind> indEntityList = new ArrayList<>(dimTableDto.getIndList().size());
            List<Ind> indGroupEntityList = new ArrayList<>();
            List<Ind> newIndList = new ArrayList<>(dimTableDto.getIndList().size());
            for (IndDto indDto : dimTableDto.getIndList()) {
                Ind indEntity = new Ind();
                BeanUtils.copyProperties(indDto, indEntity);
                indEntity.setIndexNo(indexNo);
                indEntity.setLevelNumber(1);
                if (indEntity.getParentId() != null && indEntity.getParentId() > 0) {
                    indEntity.setLevelNumber(groupIndMap.get(indEntity.getParentId()).getLevelNumber() + 1);
                    indEntity.setParentId(groupIdMap.get(indEntity.getParentId()));
                }
                // record insert id
                if (indIdSet.contains(indDto.getId())) {
                    indIdSet.remove(indDto.getId());
                    indService.updateById(indEntity);
                } else {
                    indEntity.setId(null);
                    indService.save(indEntity);
                    addIndIdList.add(indEntity.getId());
                }

                if ("GROUP".equals(indDto.getIndType())) {
                    groupIdMap.put(indDto.getId(), indEntity.getId());
                    groupIndMap.put(indDto.getId(), indEntity);
                    indGroupEntityList.add(indEntity);
                } else {
                    instanceIndMap.put(indDto.getId().toString(), indEntity.getId());
                    indEntityList.add(indEntity);
                }
                newIndList.add(indEntity);
                indexNo++;
            }

            // support avg parma ind id
            for (Ind indEntity : indEntityList) {
                if (StringUtils.isEmpty(indEntity.getAvgParmaIndId())) {
                    continue;
                }
                indEntity.setAvgParmaIndId(instanceIndMap.get(indEntity.getAvgParmaIndId()).toString());
                indService.updateById(indEntity);
            }

            if (CollectionUtil.isNotEmpty(indIdSet)) {
                indService.removeBatchByIds(indIdSet);
            }

            // save rel
            List<DimTableRel> dimTableRelList = new ArrayList<>();
            for (Integer dimId : dimTableDto.getDimIdList()) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(dimId);
                dimTableRelEntity.setRelType("dim");
                if (dimTableDto.getDefaultDimId().intValue() == dimId) {
                    dimTableRelEntity.setIsDefaultDim(BizConstants.FLAG_TRUE);
                }
                if (dimTableDto.getDefaultColumnDimId() != null && dimTableDto.getDefaultColumnDimId().intValue() == dimId) {
                    dimTableRelEntity.setIsDefaultColumnDim(BizConstants.FLAG_TRUE);
                } else {
                    dimTableRelEntity.setIsDefaultColumnDim(BizConstants.FLAG_FALSE);
                }
                dimTableRelList.add(dimTableRelEntity);
            }
            for (Ind ind : indGroupEntityList) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(ind.getId());
                dimTableRelEntity.setRelType("ind-group");
                dimTableRelList.add(dimTableRelEntity);
            }
            for (Ind ind : indEntityList) {
                DimTableRel dimTableRelEntity = new DimTableRel();
                dimTableRelEntity.setTableId(entity.getId());
                dimTableRelEntity.setRelId(ind.getId());
                dimTableRelEntity.setRelType("ind");
                dimTableRelList.add(dimTableRelEntity);
            }
            dimTableRelService.saveBatch(dimTableRelList);

            // 放在最后防止事务回滚后出错
            if (CollectionUtil.isNotEmpty(indIdSet)) {
                CubeSchema.get().deleteTableInd(dimTableDto.getId().toString(), new ArrayList<>(indIdSet));
            }

            if (CollectionUtil.isNotEmpty(addIndIdList)) {
                CubeSchema.get().addTableInd(dimTableDto.getId().toString(), addIndIdList);
            }

            // 判断是否有上下移的指标
            // 原始指标的顺序和id关系
            Map<Integer, Integer> originIndIndexNoMap = StreamUtils.toMap(originIndList, Ind::getIndexNo, Ind::getId);
            Map<Integer, Integer> newIndIndexNoMap = Maps.newHashMapWithExpectedSize(originIndList.size());
            for (int i = 0, size = newIndList.size(); i < size; i++) {
                Integer newIndId = newIndList.get(i).getId();
                if (!Objects.equals(MapUtils.getInteger(originIndIndexNoMap, (i + 1)), newIndId)) {
                    newIndIndexNoMap.put(i, newIndId);
                }
            }
            // 更新列
            CubeSchema.get().updateTableInd(dimTableDto.getId().toString(), newIndIndexNoMap);
        }
        return entity.getId();
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public DimTableInfoVo getByIdExt(Integer id) {
        DimTableInfoVo vo = new DimTableInfoVo();
        DimTable entity = this.getById(id);
        BeanUtils.copyProperties(entity, vo);

        buildDimTableInfoVo(vo);
        return vo;
    }

    @Override
    public DimTableInfoVo getByIdExt(Integer id, Long userId) {
        DimTableInfoVo vo = new DimTableInfoVo();
        DimTable entity = this.getById(id);
        BeanUtils.copyProperties(entity, vo);

        buildDimTableInfoVo(vo);
        buildDimLayout(vo, userId);
        return vo;
    }

    private void buildDimTableInfoVo(DimTableInfoVo vo) {
        List<DimTableRel> relIdList = dimTableRelService.list(new QueryWrapper<DimTableRel>().lambda().eq(DimTableRel::getTableId, vo.getId()));
        List<Integer> indIdList = relIdList.stream().filter(x -> !"dim".equals(x.getRelType())).map(DimTableRel::getRelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(indIdList)) {
            vo.setIndList(indService.list(new QueryWrapper<Ind>().lambda().in(Ind::getId, indIdList).orderByAsc(Ind::getIndexNo)));
        }
        List<Integer> dimIdList = relIdList.stream().filter(x -> "dim".equals(x.getRelType())).map(DimTableRel::getRelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dimIdList)) {
            vo.setDimList(dimDirectoryService.listByIds(dimIdList));
        }
        Optional<DimTableRel> optionalDim = relIdList.stream().filter(x -> "dim".equals(x.getRelType()) && BizConstants.FLAG_TRUE.equals(x.getIsDefaultDim())).findFirst();
        if (optionalDim.isPresent()) {
            vo.setDefaultDimId(optionalDim.get().getRelId());
        } else {
            throw new ServiceException("数方未设置默认行维。");
        }
        Optional<DimTableRel> optionalColumn = relIdList.stream().filter(x -> "dim".equals(x.getRelType()) && BizConstants.FLAG_TRUE.equals(x.getIsDefaultColumnDim())).findFirst();
        optionalColumn.ifPresent(dimTableRel -> vo.setDefaultColumnDimId(dimTableRel.getRelId()));
    }

    private void buildDimLayout(DimTableInfoVo vo, Long userId) {
        DimLayout layout = dimLayoutService.getOne(new QueryWrapper<DimLayout>().lambda().eq(DimLayout::getTableId, vo.getId()).eq(DimLayout::getUserId, userId));
        if (layout == null) {
            // 如果没有保存过，使用默认配置的行维、列维、筛选维
            LayoutVO layoutVO = new LayoutVO();
            // 筛选维
            List<LayoutFilterDimDTO> filterDimObjects = new ArrayList<>();
            layoutVO.setTableId(vo.getId());
            Integer defaultDimId = vo.getDefaultDimId();
            List<LayoutDimDTO> rowDimList = new ArrayList<>(1);
            LayoutDimDTO rowDim = new LayoutDimDTO();
            rowDim.setId(defaultDimId);
            rowDim.setDimType(DimTypeEnum.DIM);
            rowDimList.add(rowDim);
            layoutVO.setRowDimList(rowDimList);
            boolean defaultColumnFlag;
            Integer defaultColumnDimId = vo.getDefaultColumnDimId();
            if (defaultColumnDimId != null) {
                defaultColumnFlag = true;
                // 设置了默认列维
                List<LayoutDimDTO> columnDimList = new ArrayList<>(1);
                LayoutDimDTO columnDim = new LayoutDimDTO();
                columnDim.setId(defaultColumnDimId);
                columnDim.setDimType(DimTypeEnum.DIM);
                columnDimList.add(columnDim);
                layoutVO.setColumnDimList(columnDimList);
                // 指标维需要添加到筛选维
                List<Ind> indList = vo.getIndList();
                LayoutFilterDimDTO filterDim = new LayoutFilterDimDTO();
                filterDim.setId(null);
                filterDim.setDimType(DimTypeEnum.IND);
                filterDim.setFilterValues(Collections.singletonList(String.valueOf(indList.get(0).getId())));
                filterDimObjects.add(filterDim);
            } else {
                defaultColumnFlag = false;
                // 使用指标作为列维
                List<LayoutDimDTO> columnDimList = new ArrayList<>(1);
                LayoutDimDTO columnDim = new LayoutDimDTO();
                columnDim.setId(null);
                columnDim.setDimType(DimTypeEnum.IND);
                columnDimList.add(columnDim);
                layoutVO.setColumnDimList(columnDimList);
            }
            vo.setLayout(layoutVO);
            // 设置筛选维
            // dimList里id不等于defaultDimId的就是筛选维
            List<DimDirectory> dimDirectories = vo.getDimList().stream().filter(dimDirectory -> {
                if (defaultColumnFlag) {
                    return !Objects.equals(dimDirectory.getId(), defaultColumnDimId) && !Objects.equals(dimDirectory.getId(), defaultDimId);
                } else {
                    return !Objects.equals(dimDirectory.getId(), defaultDimId);
                }
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dimDirectories)) {
                Set<Integer> dimDirectoryIds = StreamUtils.toSet(dimDirectories, DimDirectory::getId);
                List<DimInstance> dimInstances = dimInstanceService.listByDimDirectoryIds(dimDirectoryIds);
                Map<Integer, Integer> dimInstanceFirstMap = Maps.newHashMapWithExpectedSize(dimDirectories.size());
                for (DimInstance dimInstance : dimInstances) {
                    dimInstanceFirstMap.putIfAbsent(dimInstance.getDimDirectoryId(), dimInstance.getId());
                }
                for (DimDirectory dimDirectory : dimDirectories) {
                    LayoutFilterDimDTO filterDim = new LayoutFilterDimDTO();
                    filterDim.setId(dimDirectory.getId());
                    filterDim.setDimType(DimTypeEnum.DIM);
                    // 默认选中第一个值
                    String select = MapUtils.getString(dimInstanceFirstMap, dimDirectory.getId());
                    if (StringUtils.isNotBlank(select)) {
                        filterDim.setFilterValues(Collections.singletonList(select));
                    } else {
                        filterDim.setFilterValues(Collections.emptyList());
                    }
                    filterDimObjects.add(filterDim);
                }
            }
            layoutVO.setFilterDimList(filterDimObjects);
        } else {
            LayoutVO layoutVO = new LayoutVO();
            layoutVO.setTableId(layout.getTableId());
            layoutVO.setRowDimList(JSON.parseArray(layout.getRowDim(), LayoutDimDTO.class));
            layoutVO.setColumnDimList(JSON.parseArray(layout.getColumnDim(), LayoutDimDTO.class));
            layoutVO.setFilterDimList(JSON.parseArray(layout.getFilterDim(), LayoutFilterDimDTO.class));
            vo.setLayout(layoutVO);
        }
    }

    @Override
    public void loading(Integer id) {
        DimTableInfoVo tableInfoVo = this.getByIdExt(id);
        List<String> dimNames = tableInfoVo.getDimList().stream().map(DimDirectory::getDimDirectoryName).collect(Collectors.toList());
        List<String> indNames = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(Ind::getIndName).collect(Collectors.toList());
        CubeSchema.get().addMockTable(tableInfoVo.getTableName(), dimNames, indNames);
    }

    private List<DimTableHeaderVo> parseIndGroup(List<DimTableHeaderVo> headers, List<Ind> indList, String rowOrColumn, int maxLevelNumber, DimTableHeaderVo parentHeader, boolean isRoot) {
        List<DimTableHeaderVo> ps = new ArrayList<>();
        for (DimTableHeaderVo headerVo : headers) {
            if ("ind".equals(headerVo.getType())) {
                DimTableHeaderVo root = this.indGroup(headers, indList, rowOrColumn, maxLevelNumber);
                ps = root.getHeaders();
                break;
            }
            if (CollectionUtil.isNotEmpty(headerVo.getHeaders())) {
                headerVo.setHeaders(this.parseIndGroup(headerVo.getHeaders(), indList, rowOrColumn, maxLevelNumber, headerVo, false));
            }
            ps.add(headerVo);
        }
        return ps;
    }

    public DimTableHeaderVo indGroup(List<DimTableHeaderVo> initHeaders, List<Ind> indList, String rowOrColumn, int maxLevelNumber) {
        Map<Integer, DimTableHeaderVo> map = new HashMap<>();
        Map<Integer, Integer> levelNumberMap = new HashMap<>();
        for (Ind ind : indList) {
            DimTableHeaderVo vo = new DimTableHeaderVo();
            vo.setId(ind.getId());
            vo.setRelId(ind.getId());
            vo.setLabel(ind.getIndName());
            vo.setRowSpan(0);
            vo.setColSpan(0);
            vo.setIsLeaf(BizConstants.FLAG_TRUE);
            if ("GROUP".equals(ind.getIndType())) {
                vo.setType("ind-group");
            } else {
                vo.setType("ind");
                levelNumberMap.put(ind.getId(), ind.getLevelNumber());
            }
            map.put(ind.getId(), vo);
        }

        for (Map.Entry<Integer, DimTableHeaderVo> entry : map.entrySet()) {
            DimTableHeaderVo v = entry.getValue();
            if ("ind-group".equals(v.getType())) {
                // nothing
            }
            if ("ind".equals(v.getType())) {
                for (DimTableHeaderVo initHeader : initHeaders) {
                    if (initHeader.getId().equals(v.getId())) {
                        v.setHeaders(initHeader.getHeaders());
                        v.setColSpan(initHeader.getColSpan());
                        v.setRowSpan(initHeader.getRowSpan());
                        v.setIsLeaf(initHeader.getIsLeaf());
                        break;
                    }
                }
                if ("row".equals(rowOrColumn)) {
                    v.setColSpan(maxLevelNumber - levelNumberMap.get(entry.getKey()) + 1);
                } else {
                    v.setRowSpan(maxLevelNumber - levelNumberMap.get(entry.getKey()) + 1);
                }
            }
        }

        DimTableHeaderVo root = new DimTableHeaderVo();
        root.setRowSpan(0);
        root.setColSpan(0);
        for (Ind ind : indList) {
            DimTableHeaderVo parent = ind.getParentId() == null || ind.getParentId() == 0 ? root : map.get(ind.getParentId());
            if (parent.getHeaders() == null) {
                parent.setHeaders(new ArrayList<>());
            }
            parent.getHeaders().add(map.get(ind.getId()));
            if ("row".equals(rowOrColumn)) {
                parent.setRowSpan(parent.getRowSpan() + map.get(ind.getId()).getRowSpan());
                parent.setColSpan(1);
            } else {
                parent.setRowSpan(1);
                parent.setColSpan(parent.getColSpan() + map.get(ind.getId()).getColSpan());
            }

            if (parent.getParentId() != null && parent.getParentId() > 0) {
                if ("row".equals(rowOrColumn)) {
                    map.get(parent.getParentId()).setRowSpan(map.get(parent.getParentId()).getRowSpan() + map.get(parent.getId()).getRowSpan());
                } else {
                    map.get(parent.getParentId()).setColSpan(map.get(parent.getParentId()).getColSpan() + map.get(parent.getId()).getColSpan());
                }
            }
        }
        return root;
    }

    @Override
    public DimTableDataVo listTableData(DimTableDataQuery query) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("1.参数转换");

        DimTableInfoVo tableInfoVo = this.getByIdExt(query.getTableId());

        // 定义结果集转置顺序集合
        List<String> transRowSeqList = new ArrayList<>();
        List<String> transColumnSeqList = new ArrayList<>();
        // 定义全量维度和指标聚合参数
//        List<DimDirectory> allDim = new ArrayList<>();
        List<Ind> allInd = new ArrayList<>();
        Set<Integer> queryAllDimIds = tableInfoVo.getDimList().stream().map(DimDirectory::getId).collect(Collectors.toSet());
        Map<Integer, Ind> indInstanceIdMap = tableInfoVo.getIndList().stream()
                .filter(ind -> StringUtils.equals("INSTANCE", ind.getIndType()))
                .collect(Collectors.toMap(Ind::getId, Function.identity()));
        boolean transRowHasIndFlag = false;
        boolean transColumnHasIndFlag = false;
        for (int i = 0, size = query.getRowDimIds().size(); i < size; i++) {
            Integer rowDimId = query.getRowDimIds().get(i);
            if (query.getRowDimTypes().get(i) == DimTypeEnum.DIM) {
                if (queryAllDimIds.contains(rowDimId)) {
//                    allDim.add(dimDirectory);
                    transRowSeqList.add(String.valueOf(rowDimId));
                }
            } else if (query.getRowDimTypes().get(i) == DimTypeEnum.IND) {
                Ind ind = MapUtils.getObject(indInstanceIdMap, rowDimId);
                if (ind != null) {
                    allInd.add(ind);
                    if (!transRowHasIndFlag) {
                        transRowHasIndFlag = true;
                        transRowSeqList.add("ind");
                    }
                }
            }
        }
        for (int i = 0, size = query.getColumnDimIds().size(); i < size; i++) {
            Integer columnDimId = query.getColumnDimIds().get(i);
            if (query.getColumnDimTypes().get(i) == DimTypeEnum.DIM) {
                if (queryAllDimIds.contains(columnDimId)) {
//                    allDim.add(dimDirectory);
                    transColumnSeqList.add(String.valueOf(columnDimId));
                }
            } else if (query.getColumnDimTypes().get(i) == DimTypeEnum.IND) {
                Ind ind = MapUtils.getObject(indInstanceIdMap, columnDimId);
                if (ind != null) {
                    allInd.add(ind);
                    if (!transColumnHasIndFlag) {
                        transColumnHasIndFlag = true;
                        transColumnSeqList.add("ind");
                    }
                }
            }
        }

        if (CollectionUtil.isEmpty(query.getShowDimList())) {
            query.setShowDimList(new ArrayList<>());
        }

        // 处理维度关联
        List<DimDimRel> dimRelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(query.getFilterList())) {
            List<Integer> tryDimRelResIds = new ArrayList<>();
            query.getFilterList().forEach(x -> {
                if (x.getDimType() == DimTypeEnum.DIM && queryAllDimIds.contains(Integer.valueOf(x.getColumnCode()))) {
                    tryDimRelResIds.add(Integer.valueOf(x.getValue()));
                }
            });
            if (CollectionUtil.isNotEmpty(tryDimRelResIds)) {
                dimRelList = dimDimRelService.list(new QueryWrapper<DimDimRel>().lambda().eq(DimDimRel::getTableId, query.getTableId()).in(DimDimRel::getResInstanceId, tryDimRelResIds));
            }
        }

        stopWatch.stop();
        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());

        stopWatch.start("2.表头处理");

        List<DimInstance> viewDimInstanceList = dimInstanceService.list(new QueryWrapper<DimInstance>().lambda().in(DimInstance::getDimDirectoryId, queryAllDimIds).orderByDesc(DimInstance::getDimDirectoryId).orderByAsc(DimInstance::getAncestors));

        List<RowSidDto> rowSidDtoList = new ArrayList<>();
        List<RowSidDto> columnSidDtoList = new ArrayList<>();
        List<DimTableHeaderVo> rowHeaders = this.parseHeader(transRowSeqList, allInd, viewDimInstanceList, 0, "row", rowSidDtoList, new RowSidDto(), query.getShowDimList(), dimRelList);
        List<DimTableHeaderVo> columnHeaders = this.parseHeader(transColumnSeqList, allInd, viewDimInstanceList, 0, "col", columnSidDtoList, new RowSidDto(), query.getShowDimList(), dimRelList);

        stopWatch.stop();
        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());

        stopWatch.start("3.指标分组");

        // 追加指标分组
        if (CollectionUtil.isNotEmpty(rowHeaders) && CollectionUtil.isNotEmpty(columnHeaders)) {
            int maxLevelNumber = tableInfoVo.getIndList().stream().max(Comparator.comparingInt(Ind::getLevelNumber)).get().getLevelNumber();
            //Map<Integer, Ind> indMap = tableInfoVo.getIndList().stream().collect(Collectors.toMap(Ind::getId, x -> x, (v1, v2) -> v1));
            if (maxLevelNumber > 1) {
                rowHeaders = this.parseIndGroup(rowHeaders, tableInfoVo.getIndList(), "row", maxLevelNumber, null, true);
                columnHeaders = this.parseIndGroup(columnHeaders, tableInfoVo.getIndList(), "column", maxLevelNumber, null, true);
            }
        }

        stopWatch.stop();
        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());

        // 查询数据
        CubeServer cubeServer = CubeSchema.get().getTable(tableInfoVo.getId().toString());
        if (cubeServer == null) {
            List<String> dimIdList = tableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            List<String> indIdList = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            CubeMetaData cubeMetaData = new CubeMetaData(tableInfoVo.getId().toString(), dimIdList, indIdList);
            cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);
        }
        // 定义返回数据集合对象
        List<DimRowDataVo> rows = new ArrayList<>();
        if (cubeServer != null && CollectionUtil.isNotEmpty(rowSidDtoList) && CollectionUtil.isNotEmpty(columnSidDtoList)) {

            stopWatch.start("4.查询数据");

            // 构建数据格式集合
            Map<Integer, TableMetaJson> jsonsMap = this.getFormatJson(allInd);
            // 构建过滤列表
            Map<String, List<String>> filterDims = new HashMap<>();
            AtomicReference<Boolean> dimAllLeaf = new AtomicReference<>(true);
            // 映射过滤维度下标
            List<Integer> offset = new ArrayList<>();
            AtomicReference<String> filterIndId = new AtomicReference<>();
            if (CollectionUtil.isNotEmpty(query.getFilterList())) {
                List<Integer> dimInstanceIds = new ArrayList<>();
                CubeServer finalCubeServer = cubeServer;
                query.getFilterList().forEach(x -> {
                    if (x.getDimType() == DimTypeEnum.DIM && queryAllDimIds.contains(Integer.valueOf(x.getColumnCode()))) {
                        if (filterDims.containsKey(x.getColumnCode())) {
                            filterDims.get(x.getColumnCode()).add(x.getColumnCode());
                        } else {
                            List<String> list = new ArrayList<>();
                            list.add(x.getValue());
                            offset.add(finalCubeServer.factTable.getDimIndex(x.getColumnCode()));
                            filterDims.put(x.getColumnCode(), list);
                        }
                        dimInstanceIds.add(Integer.valueOf(x.getValue()));
                    } else {
                        // 兼容错误参数
                        filterIndId.set(x.getValue());
                    }
                });
                if (CollectionUtil.isNotEmpty(dimInstanceIds)) {
                    List<DimInstance> instances = dimInstanceService.listByIds(dimInstanceIds);
                    instances.forEach(x -> {
                        if (BizConstants.FLAG_FALSE.equals(x.getIsLeaf())) {
                            dimAllLeaf.set(false);
                        }
                    });
                }
            }

            // 兼容指标筛选维
            if (CollectionUtil.isEmpty(jsonsMap)) {
                jsonsMap = this.getFormatJson(tableInfoVo.getIndList().stream().filter(x -> StringUtils.equals(filterIndId.get(), String.valueOf(x.getId()))).collect(Collectors.toList()));
            }

            // 指标规则配置 单元格不可编辑
            Map<String, Boolean> indToRuleMap = dimRuleService.getIndHasRule(tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(Ind::getId).collect(Collectors.toList()));
            // 单元格是不汇总，可以编辑
            Map<String, Boolean> indNotSummarizeMap = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).collect(Collectors.toMap(ind -> String.valueOf(ind.getId()), x -> StringUtils.equals(BizConstants.NOT_SUMMARIZE, x.getFunctionName())));

            // 维度父级ID标记集合
            List<DimInstance> dimInstanceList = dimInstanceService.list(new QueryWrapper<DimInstance>().lambda().in(DimInstance::getDimDirectoryId, queryAllDimIds));
            Set<Integer> parentIdSet = dimInstanceList.stream().map(DimInstance::getParentId).collect(Collectors.toSet());

            List<FactTable.Record> recordList = cubeServer.filterRecord(filterDims);

            stopWatch.stop();
            log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());

            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start("5.数据匹配");

            stopWatch.start("5-0.数据缓存");

            // cache
            Map<String, FactTable.Record> recordMap = Maps.newHashMapWithExpectedSize(recordList.size());
            for (FactTable.Record record : recordList) {
                List<String> recordDim = Arrays.asList(record.getDimOfFact());
                List<String> valueRecordSorts = IntStream.range(0, recordDim.size()).filter(i -> !offset.contains(i)).mapToObj(recordDim::get).map(Integer::valueOf).sorted(Comparator.comparingInt(o -> o)).map(String::valueOf).collect(Collectors.toList());
                recordMap.put(String.join("-", valueRecordSorts), record);
            }

            stopWatch.stop();
            log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());

            int index = 0;

            for (RowSidDto row : rowSidDtoList) {
                DimRowDataVo rowDataVo = new DimRowDataVo();
                for (RowSidDto column : columnSidDtoList) {

                    if (index % 444 == 0) {
                        stopWatch.start("5-1.匹配前置");
                    }

                    DimCellDataVo cellDataVo = new DimCellDataVo();
                    String indSid = StringUtils.isNotEmpty(filterIndId.get()) ? filterIndId.get() : StringUtils.EMPTY;
                    if (StringUtils.isEmpty(indSid)) {
                        indSid = row.getColumnKey() == null || row.getColumnKey() < 1 ? String.valueOf(column.getColumnKey()) : String.valueOf(row.getColumnKey());
                    }
                    if (StringUtils.isEmpty(indSid)) {
                        throw new ServiceException("数方查询数据未设置行列指标维或筛选维指标没有指定叶子实例。");
                    }
                    // 规则指标不可编辑赋值
                    boolean indIsRule = MapUtils.getBooleanValue(indToRuleMap, indSid);
                    boolean notSummarizeFlag = MapUtils.getBooleanValue(indNotSummarizeMap, indSid);

                    Map<Integer, Integer> dimSids = new LinkedHashMap<>();
                    dimSids.putAll(row.getRowDimKeyMap());
                    dimSids.putAll(column.getRowDimKeyMap());
                    // 单元格编辑属性
                    boolean isLeaf = true;
                    for (int dimId : dimSids.values()) {
                        if (parentIdSet.contains(dimId)) {
                            isLeaf = false;
                            break;
                        }
                    }

                    if (index % 444 == 0) {
                        stopWatch.stop();
                        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());
                    }

                    if (index % 444 == 0) {
                        stopWatch.start("5-2.key值处理");
                    }
                    List<String> valueSidSorts = dimSids.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(x -> String.valueOf(x.getValue())).collect(Collectors.toList());
                    if (index % 444 == 0) {
                        stopWatch.stop();
                        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());
                    }

                    if (index % 444 == 0) {
                        stopWatch.start("5-3.命中缓存");
                    }
                    FactTable.Record record = recordMap.get(String.join("-", valueSidSorts));
                    if (index % 444 == 0) {
                        stopWatch.stop();
                        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());
                    }

                    if (index % 444 == 0) {
                        stopWatch.start("5-5.数据处理");
                    }
                    if (record != null) {
                        cubeServer.factTable.getIndIndex(indSid);
                        DoubleDouble dd = record.getInd(indSid);
                        if (dd != null) {
                            try {
                                cellDataVo.setOriginalValue(dd.doubleValue());
                                cellDataVo.setCellValue(cellDataVo.getOriginalValue());
                                cellDataVo.setDisplayValue(tableService.getFormatValue(jsonsMap.get(Integer.valueOf(indSid)), cellDataVo.getOriginalValue()));
                                cellDataVo.setAlign(jsonsMap.get(Integer.valueOf(indSid)).getDataFormat() == null ? "right" : jsonsMap.get(Integer.valueOf(indSid)).getDataFormat().getContentAlign());
                            } catch (Exception e) {
                                log.error("数据格式转换错误. ", e);
                                cellDataVo.setDisplayValue(String.valueOf(cellDataVo.getOriginalValue()));
                            }
                        }
                    }
                    if (index % 444 == 0) {
                        stopWatch.stop();
                        log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());
                    }

                    cellDataVo.setIsEdite((notSummarizeFlag || (dimAllLeaf.get() && isLeaf)) && !indIsRule);
                    rowDataVo.getCells().add(cellDataVo);

                    index++;
                }
                rows.add(rowDataVo);
            }

            stopWatch1.stop();
            log.info("{} 执行了 >>>> {} 毫秒", stopWatch.getLastTaskName(), stopWatch.getLastTaskTimeMillis());
        }

        DimTableDataVo tableDataVo = new DimTableDataVo();
        tableDataVo.setRowHeaders(rowHeaders);
        tableDataVo.setColumnHeaders(columnHeaders);
        tableDataVo.setRows(rows);
        return tableDataVo;
    }

    @Override
    public void addData(DimTableDataDto dimTableDataDto) {
        ThreadLocalCacheAspect.init();
        ThreadLocalCacheAspect.initTtl();
        try {
            DimTableInfoVo tableInfoVo = SpringUtils.getAopProxy(this).getByIdExt(dimTableDataDto.getTableId());
            List<String> dimIdList = tableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            List<String> indIdList = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());

            CubeMetaData cubeMetaData = new CubeMetaData(tableInfoVo.getId().toString(), dimIdList, indIdList);
            CubeServer cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);

            List<DimInstance> dimInstanceList = dimInstanceService.listByDimDirectoryIds(StreamUtils.toList(tableInfoVo.getDimList(), DimDirectory::getId));
            Map<String, List<DimInstance>> toChildMap = dimInstanceList.stream().collect(Collectors.groupingBy(x -> x.getDimDirectoryId() + ":" + x.getParentId()));
            Map<String, Integer> toParentMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + tableInfoVo.getId());
            if (toParentMap == null) {
                toParentMap = dimInstanceList.stream().filter(x -> x.getParentId() != null && x.getParentId() > 0).collect(Collectors.toMap(x -> x.getDimDirectoryId() + ":" + x.getId(), DimInstance::getParentId));
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + tableInfoVo.getId(), toParentMap);
            }
            Map<Integer, Ind> indMap = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).collect(Collectors.toMap(Ind::getId, Function.identity()));
            Map<Integer, Boolean> indSummarizeFlagMap = Maps.newHashMapWithExpectedSize(indMap.size());

            // TODO 临时兼容前端参数问题
            List<DimTableDataCellDto> tempList = new ArrayList<>();
            for (DimTableDataCellDto cellDto : dimTableDataDto.getDimTableDataCellList()) {
                boolean isFilter = false;
                for (int i = 0, size = cellDto.getRelDimIdList().size(); i < size; i++) {
                    if (cellDto.getOriginalValue() == null) {
                        isFilter = true;
                        break;
                    }
                    Integer indId = Integer.valueOf(cellDto.getIndId());
                    Ind ind = indMap.get(indId);
                    boolean notSummarizeFlag = indSummarizeFlagMap.computeIfAbsent(indId, k -> StringUtils.equals(BizConstants.NOT_SUMMARIZE, ind.getFunctionName()));
                    String dimId = cellDto.getRelDimIdList().get(i).toString();
                    if (!notSummarizeFlag && toChildMap.containsKey(dimId + ":" + cellDto.getDimInstanceIdList().get(i))) {
                        isFilter = true;
                        break;
                    }
                }
                if (!isFilter) {
                    tempList.add(cellDto);
                }
            }
            dimTableDataDto.setDimTableDataCellList(tempList);

            // 用队列替代递归，批量处理所有需要向上汇总的节点
            Queue<DimTableDataDto> queue = new LinkedList<>();
            queue.offer(dimTableDataDto);
            Map<String, Double> parentSumCache = new HashMap<>(); // 缓存父节点的sum结果
            while (!queue.isEmpty()) {
                DimTableDataDto currentDto = queue.poll();
                boolean currentIsParent = (currentDto != dimTableDataDto);
                List<DimTableDataCellDto> parentCellList = new ArrayList<>();
                for (DimTableDataCellDto cellDto : currentDto.getDimTableDataCellList()) {
                    if (CollectionUtil.isEmpty(cellDto.getRelDimIdList()) && cellDto.getRelDimIdList().size() != dimIdList.size()) {
                        log.error("数据关联维度ID参数错误。{}", cellDto.getRelDimIdList());
                        throw new ServiceException("数据关联维度ID参数错误。");
                    }
                    if (cellDto.getOriginalValue() == null && !"delete".equals(cellDto.getOpType())) {
                        continue;
                    }
                    Map<String, List<String>> filterDims = new HashMap<>();
                    Integer indId = Integer.valueOf(cellDto.getIndId());
                    Ind ind = indMap.get(indId);
                    boolean notSummarizeFlag = indSummarizeFlagMap.computeIfAbsent(indId, k -> StringUtils.equals(BizConstants.NOT_SUMMARIZE, ind.getFunctionName()));
                    String[] dimOfFact = new String[cubeServer.factTable.meta.getDimColumnNames().size()];
                    for (int i = 0, size = cellDto.getRelDimIdList().size(); i < size; i++) {
                        String dimId = cellDto.getRelDimIdList().get(i).toString();
                        int index = cubeServer.factTable.getDimIndex(dimId);
                        dimOfFact[index] = cellDto.getDimInstanceIdList().get(i);
                        if (currentIsParent && dimId.equals(cellDto.getParentDimId())) {
                            filterDims.put(dimId, toChildMap.get(dimId + ":" + dimOfFact[index]).stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList()));
                        } else {
                            filterDims.put(dimId, Collections.singletonList(cellDto.getDimInstanceIdList().get(i)));
                        }
                        if (!notSummarizeFlag && toParentMap.containsKey(dimId + ":" + dimOfFact[index])) {
                            DimTableDataCellDto dto = new DimTableDataCellDto();
                            dto.setRelDimIdList(new ArrayList<>(cellDto.getRelDimIdList()));
                            dto.setDimInstanceIdList(new ArrayList<>(cellDto.getDimInstanceIdList()));
                            dto.setOpType("add");
                            dto.setIndId(cellDto.getIndId());
                            dto.setOriginalValue((double) 0);
                            dto.setParentDimId(dimId);
                            dto.setParentDimInstanceId(toParentMap.get(dimId + ":" + dimOfFact[index]).toString());
                            dto.getDimInstanceIdList().set(i, toParentMap.get(dimId + ":" + dimOfFact[index]).toString());
                            parentCellList.add(dto);
                        }
                    }
                    double sumVal = 0L;
                    if (currentIsParent) {
                        if (!notSummarizeFlag) {
                            String cacheKey = filterDims + "-" + cellDto.getIndId();
                            if (parentSumCache.containsKey(cacheKey)) {
                                sumVal = parentSumCache.get(cacheKey);
                            } else {
                                List<FactTable.Record> recordList = cubeServer.filterRecord(filterDims);
                                sumVal = getSummaryInd(cubeServer, recordList, ind, filterDims.get(cellDto.getParentDimId()).size());
                                parentSumCache.put(cacheKey, sumVal);
                            }
                        }
                        filterDims.put(cellDto.getParentDimId(), Collections.singletonList(cellDto.getParentDimInstanceId()));
                    }
                    FactTable.Record record = cubeServer.hitRecordWithInstance(filterDims);
                    if (record == null) {
                        DoubleDouble[] indOfFact = new DoubleDouble[cubeServer.factTable.meta.getIndColumnNames().size()];
                        int index = cubeServer.factTable.getIndIndex(cellDto.getIndId());
                        if (currentIsParent && !notSummarizeFlag) {
                            indOfFact[index] = new DoubleDouble(sumVal);
                        } else {
                            indOfFact[index] = new DoubleDouble(Double.parseDouble(cellDto.getOriginalValue().toString()));
                        }
                        record = cubeServer.factTable.new Record(CubeSchema.get().getTableNextKey(tableInfoVo.getId().toString()));
                        record.setDimOfFact(dimOfFact);
                        record.setIndOfFact(indOfFact);
                        CubeSchema.get().addTableData(cubeServer, record);
                    } else {
                        DoubleDouble[] indOfFact = record.getIndOfFact();
                        int index = cubeServer.factTable.getIndIndex(cellDto.getIndId());
                        if ("delete".equals(cellDto.getOpType())) {
                            indOfFact[index] = new DoubleDouble();
                        } else {
                            if (currentIsParent && !notSummarizeFlag) {
                                indOfFact[index] = new DoubleDouble(sumVal);
                            } else {
                                indOfFact[index] = new DoubleDouble(cellDto.getOriginalValue());
                            }
                        }
//                    record.setIndOfFact(indOfFact);
//                    CubeSchema.get().updateTableData(cubeServer, record);
                    }
                }
                // 只在原始数据处理后执行一次
                if (currentDto == dimTableDataDto) {
                    dimRuleServiceHelperService.execute(currentDto.getTableId(), parentCellList);
                }
                if (CollectionUtil.isNotEmpty(parentCellList)) {
                    DimTableDataDto parentDimTableDataDto = new DimTableDataDto();
                    parentDimTableDataDto.setTableId(currentDto.getTableId());
                    parentDimTableDataDto.setDimTableDataCellList(parentCellList);
                    queue.offer(parentDimTableDataDto);
                }
            }
        } finally {
            ThreadLocalUtils.clear();
        }
    }

    @Override
    public List<DimTable> list(DimTableListQuery query) {
        QueryWrapper<DimTable> qw = new QueryWrapper<>();
        qw.lambda().like(CharSequenceUtil.isNotBlank(query.getTableName()), DimTable::getTableName, query.getTableName());
        qw.lambda().eq(query.getParentId() != null, DimTable::getParentId, query.getParentId());
        qw.lambda().orderByAsc(DimTable::getTableName);
        qw.lambda().orderByDesc(DimTable::getCreateTime);
        return this.list(qw);
    }

    @Override
    public List<DimTableVO> queryList(DimTableListQuery query) {
        List<DimTable> list = this.list(query);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DimTableVO> dimTableVOS = BeanUtil.copyToList(list, DimTableVO.class);
        Set<Integer> dimTableIds = list.stream()
                .filter(dimTable -> StringUtils.equals(dimTable.getType(), "INSTANCE"))
                .map(DimTable::getId)
                .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(dimTableIds)) {
            return dimTableVOS;
        }
        List<DimTableRel> relIdList = dimTableRelService.list(new QueryWrapper<DimTableRel>().lambda().in(DimTableRel::getTableId, dimTableIds));
        Map<String, List<DimTableRel>> relTypeMap = relIdList.stream().collect(Collectors.groupingBy(DimTableRel::getRelType));
        List<DimTableRel> indList = MapUtils.getObject(relTypeMap, "ind");
        if (CollectionUtils.isNotEmpty(indList)) {
            Map<Integer, List<DimTableRel>> dimTableIndListMap = StreamUtils.groupByKey(indList, DimTableRel::getTableId);
            dimTableVOS.forEach(dimTableVO -> {
                if (StringUtils.equals(dimTableVO.getType(), "INSTANCE")) {
                    dimTableVO.setIndSize(MapUtils.getObject(dimTableIndListMap, dimTableVO.getId(), Collections.emptyList()).size());
                }
            });
        }
        List<DimTableRel> dimList = MapUtils.getObject(relTypeMap, "dim");
        if (CollectionUtils.isNotEmpty(dimList)) {
            Map<Integer, List<DimTableRel>> dimTableDimListMap = StreamUtils.groupByKey(dimList, DimTableRel::getTableId);
            List<Integer> dimIdList = dimList.stream().map(DimTableRel::getRelId).collect(Collectors.toList());
            List<DimInstance> dimInstanceList = dimInstanceService.listByDimDirectoryIds(dimIdList);
            if (CollectionUtil.isNotEmpty(dimInstanceList)) {
                Map<Integer, List<DimInstance>> dimInstanceMap = StreamUtils.groupByKey(dimInstanceList, DimInstance::getDimDirectoryId);
                for (DimTableVO dimTableVO : dimTableVOS) {
                    if (!StringUtils.equals(dimTableVO.getType(), "INSTANCE")) {
                        continue;
                    }
                    int dimSize = 1;
                    List<DimTableRel> dimRelList = MapUtils.getObject(dimTableDimListMap, dimTableVO.getId());
                    if (CollectionUtil.isNotEmpty(dimRelList)) {
                        for (DimTableRel dimTableRel : dimRelList) {
                            List<DimInstance> dimInstances = MapUtils.getObject(dimInstanceMap, dimTableRel.getRelId(), Collections.emptyList());
                            dimSize *= dimInstances.size();
                        }
                    }
                    dimTableVO.setDimSize(dimSize);
                }
            }
        }
        // 预处理并收集所有INSTANCE类型对象
        List<DimTableVO> instanceList = dimTableVOS.stream()
                .peek(vo -> {
                    String type = vo.getType();
                    if (StringUtils.equals("INSTANCE", type)) {
                        vo.setCellSize(vo.getDimSize() * vo.getIndSize());
                    } else {
                        vo.setDimSize(null);
                        vo.setIndSize(null);
                        vo.setCellSize(0);
                    }
                })
                .filter(vo -> StringUtils.equals("INSTANCE", vo.getType()))
                .collect(Collectors.toList());

        // 创建ID映射表
        Map<Integer, DimTableVO> dimTableVOMap = dimTableVOS.stream()
                .collect(Collectors.toMap(DimTableVO::getId, Function.identity()));

        // 仅处理INSTANCE类型对象
        instanceList.forEach(vo -> updateParentGroupCellSize(dimTableVOMap, vo));
        return dimTableVOS;
    }

    private void updateParentGroupCellSize(Map<Integer, DimTableVO> dimTableVOMap, DimTableVO dimTableVO) {
        Integer parentId = dimTableVO.getParentId();
        while (parentId != null) {
            DimTableVO parentVO = dimTableVOMap.get(parentId);
            if (parentVO == null) break;
            if (StringUtils.equals("GROUP", parentVO.getType())) {
                parentVO.setCellSize(parentVO.getCellSize() + dimTableVO.getCellSize());
            }
            parentId = parentVO.getParentId();
        }
    }


    @Override
    public DimTableInfoVo getByTableName(String tableName) {
        DimTable dimTable = this.getOne(Wrappers.<DimTable>lambdaQuery().eq(DimTable::getTableName, tableName).eq(DimTable::getType, "INSTANCE"));
        if (dimTable == null) {
            return null;
        }
        DimTableInfoVo dimTableInfoVo = new DimTableInfoVo();
        BeanUtils.copyProperties(dimTable, dimTableInfoVo);
        buildDimTableInfoVo(dimTableInfoVo);
        return dimTableInfoVo;
    }

    private List<DimTableHeaderVo> parseHeader
            (List<String> transSeqList, List<Ind> allInd, List<DimInstance> allDimInstance,
             int currentSeqIndex, String type, List<RowSidDto> rowSidDtoList, RowSidDto
                     currentRowSidDto, List<DimTableInstanceQuery> dimTableInstanceQueryList, List<DimDimRel> dimRelList) {
        int totalSeqIndex = transSeqList.size();
        if (currentSeqIndex == totalSeqIndex) {
            return null;
        }
        List<DimTableHeaderVo> headerList = new ArrayList<>();
        if ("ind".equals(transSeqList.get(currentSeqIndex))) {
            for (Ind ind : allInd) {
                DimTableHeaderVo headerVo = new DimTableHeaderVo();
                headerVo.setId(ind.getId());
                headerVo.setRelId(ind.getId());
                headerVo.setParentId(ind.getParentId());
                headerVo.setLabel(ind.getIndName());
                headerVo.setType("ind");
                headerVo.setRowSpan(1);
                headerVo.setColSpan(1);
                //指标没有N
                headerVo.setIsLeaf(BizConstants.FLAG_TRUE);
                currentRowSidDto.setColumnKey(headerVo.getId());
                if (currentSeqIndex == totalSeqIndex - 1) {
                    // 使用构造器方法进行深复制
                    HashMap<Integer, Integer> deepCopyMap = new HashMap<>(currentRowSidDto.getRowDimKeyMap());
                    Integer deepCopyInteger = null;
                    if (currentRowSidDto.getColumnKey() != null) {
                        deepCopyInteger = Integer.valueOf(currentRowSidDto.getColumnKey().intValue());
                    }

                    // 注入ROW-SID
                    RowSidDto copyRow = new RowSidDto();
                    copyRow.setRowDimKeyMap(deepCopyMap);
                    copyRow.setColumnKey(deepCopyInteger);
                    copyRow.setLeaf(currentRowSidDto.isLeaf());
                    rowSidDtoList.add(copyRow);

                    // 重置currentRowSidDto
                    currentRowSidDto.setLeaf(true);
                } else {
                    headerVo.setHeaders(this.parseHeader(transSeqList, allInd, allDimInstance, currentSeqIndex + 1, type, rowSidDtoList, currentRowSidDto, dimTableInstanceQueryList, dimRelList));
                    int spanCount = 0;
                    for (DimTableHeaderVo count : headerVo.getHeaders()) {
                        if ("row".equals(type)) {
                            spanCount = spanCount + count.getRowSpan();
                            headerVo.setRowSpan(spanCount);
                            headerVo.setColSpan(1);
                        } else {
                            spanCount = spanCount + count.getColSpan();
                            headerVo.setRowSpan(1);
                            headerVo.setColSpan(spanCount);
                        }
                    }
                }
                headerList.add(headerVo);
            }
        } else {
            List<DimTableInstanceQuery> dimInstanceShow = dimTableInstanceQueryList.stream().filter(x -> x.getDimId() == Integer.parseInt(transSeqList.get(currentSeqIndex))).collect(Collectors.toList());
            List<Integer> dimInstanceIds = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(dimInstanceShow)) {
                dimInstanceIds = dimInstanceShow.get(0).getInstanceIds();
            }

            List<DimDimRel> dimInstanceRel = dimRelList.stream().filter(x -> x.getTarDimDirectoryId() == Integer.parseInt(transSeqList.get(currentSeqIndex))).collect(Collectors.toList());
            List<Integer> dimInstanceRelIds = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(dimInstanceRel)) {
                dimInstanceRelIds = dimInstanceRel.stream().map(DimDimRel::getTarInstanceId).collect(Collectors.toList());
            }

            List<Integer> finalDimInstanceIds = dimInstanceIds;
            List<Integer> finalDimInstanceRelIds = dimInstanceRelIds;
            List<DimInstance> itemList = allDimInstance.stream().filter(x -> {
                        boolean v1 = x.getDimDirectoryId() == Integer.parseInt(transSeqList.get(currentSeqIndex));
                        boolean v2 = true;
                        boolean v3 = true;
                        if (CollectionUtil.isNotEmpty(finalDimInstanceIds)) {
                            v2 = finalDimInstanceIds.contains(x.getId());
                        }
                        if (CollectionUtil.isNotEmpty(finalDimInstanceRelIds)) {
                            v3 = finalDimInstanceIds.contains(x.getId());
                        }
                        return v1 && v2 && v3;
                    }
            ).collect(Collectors.toList());

            itemList.forEach(v -> {
                DimTableHeaderVo headerVo = new DimTableHeaderVo();
                headerVo.setId(v.getId());
                headerVo.setRelId(v.getDimDirectoryId());
                headerVo.setParentId(v.getParentId());
                headerVo.setLabel(v.getDimName());
                headerVo.setType("dim");
                headerVo.setRowSpan(1);
                headerVo.setColSpan(1);
                if (BizConstants.FLAG_FALSE.equals(v.getIsLeaf())) {
                    currentRowSidDto.setLeaf(false);
                }
                currentRowSidDto.getRowDimKeyMap().put(headerVo.getRelId(), headerVo.getId());
                headerVo.setIsLeaf(v.getIsLeaf());
                if (currentSeqIndex == totalSeqIndex - 1) {

                    // 使用构造器方法进行深复制
                    HashMap<Integer, Integer> deepCopyMap = new HashMap<>(currentRowSidDto.getRowDimKeyMap());
                    Integer deepCopyInteger = null;
                    if (currentRowSidDto.getColumnKey() != null) {
                        deepCopyInteger = Integer.valueOf(currentRowSidDto.getColumnKey().intValue());
                    }

                    // 注入ROW-SID
                    RowSidDto copyRow = new RowSidDto();
                    copyRow.setRowDimKeyMap(deepCopyMap);
                    copyRow.setColumnKey(deepCopyInteger);
                    copyRow.setLeaf(Boolean.valueOf(currentRowSidDto.isLeaf()));
                    rowSidDtoList.add(copyRow);

                    // 重置currentRowSidDto
                    currentRowSidDto.setLeaf(true);
                } else {
                    headerVo.setHeaders(this.parseHeader(transSeqList, allInd, allDimInstance, currentSeqIndex + 1, type, rowSidDtoList, currentRowSidDto, dimTableInstanceQueryList, dimRelList));
                    int spanCount = 0;
                    for (DimTableHeaderVo count : headerVo.getHeaders()) {
                        if ("row".equals(type)) {
                            spanCount = spanCount + count.getRowSpan();
                            headerVo.setRowSpan(spanCount);
                            headerVo.setColSpan(1);
                        } else {
                            spanCount = spanCount + count.getColSpan();
                            headerVo.setRowSpan(1);
                            headerVo.setColSpan(spanCount);
                        }
                    }
                }
                headerList.add(headerVo);
            });
        }
        return headerList;
    }

    private long count(DimTable entity) {
        return this.count(new QueryWrapper<DimTable>().lambda()
                .ne(entity.getId() != null, DimTable::getId, entity.getId())
                .eq(DimTable::getType, entity.getType())
                .eq(DimTable::getTableName, entity.getTableName()));
    }

    private Map<Integer, TableMetaJson> getFormatJson(List<Ind> indList) {
        // 数据格式ID集合
        List<String> dataFormatIdList = indList.stream().map(Ind::getDataFormatId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 定义列元数据绑定的数据格式对象集合
        List<GroupInstance> groupInstanceList;
        if (CollectionUtil.isNotEmpty(dataFormatIdList)) {
            groupInstanceList = groupInstanceService.list(new QueryWrapper<GroupInstance>().lambda().in(GroupInstance::getId, dataFormatIdList));
        } else {
            groupInstanceList = Collections.emptyList();
        }
        // 默认数据格式
        GroupInstance defaultFormat = groupInstanceService.getOne(new QueryWrapper<GroupInstance>().lambda().eq(GroupInstance::getGroupInstanceName, "无"));

        Map<Integer, TableMetaJson> jsonsMap = Maps.newHashMapWithExpectedSize(indList.size());
        for (Ind ind : indList) {
            TableMetaJson json = new TableMetaJson();
            // 注入数据格式对象
            for (GroupInstance instance : groupInstanceList) {
                if (StringUtils.isNotEmpty(ind.getDataFormatId()) && ind.getDataFormatId().equals(instance.getId())) {
                    GroupInstanceDto dto = new GroupInstanceDto();
                    BeanUtils.copyProperties(instance, dto);
                    json.setDataFormat(dto);
                    json.setDataFormatId(ind.getDataFormatId());
                    break;
                }
            }
            // 注入默认数据格式对象
            if (json.getDataFormat() == null) {
                GroupInstanceDto dto = new GroupInstanceDto();
                BeanUtils.copyProperties(defaultFormat, dto);
                json.setDataFormat(dto);
                json.setDataFormatId(defaultFormat.getId());
            }
            jsonsMap.put(ind.getId(), json);
        }
        return jsonsMap;
    }

    public double getSummaryInd(CubeServer cubeServer, List<FactTable.Record> recordList, Ind ind,
                                int dimInstanceNum) {
        double summaryVal = 0L;
        double weightVal = 0L;
        if ("最大值".equals(ind.getFunctionName())) {
            summaryVal = -99999999999L;
        }
        if ("最小值".equals(ind.getFunctionName())) {
            summaryVal = 99999999999L;
        }
        int indexMain = cubeServer.factTable.getIndIndex(ind.getId().toString());
        int indexSlave = StringUtils.isNotEmpty(ind.getAvgParmaIndId()) ? cubeServer.factTable.getIndIndex(ind.getAvgParmaIndId()) : 0;
        for (FactTable.Record record : recordList) {
            DoubleDouble[] indOfFact = record.getIndOfFact();
            if (indOfFact[indexMain] == null) {
                continue;
            }
            if ("汇总".equals(ind.getFunctionName())) {
                summaryVal = summaryVal + indOfFact[indexMain].doubleValue();
            }
            if ("平均".equals(ind.getFunctionName())) {
                summaryVal = summaryVal + indOfFact[indexMain].doubleValue();
            }
            if ("计数".equals(ind.getFunctionName())) {
                summaryVal = summaryVal + 1;
            }
            if ("最大值".equals(ind.getFunctionName())) {
                summaryVal = Math.max(summaryVal, indOfFact[indexMain].doubleValue());
            }
            if ("最小值".equals(ind.getFunctionName())) {
                summaryVal = Math.min(summaryVal, indOfFact[indexMain].doubleValue());
            }
            if ("加权平均".equals(ind.getFunctionName())) {
                if (indOfFact[indexSlave] == null) {
//                    log.error("指标[%s]加权列[%s]值为空. ");
                    continue;
                }
                summaryVal = summaryVal + indOfFact[indexMain].doubleValue() * indOfFact[indexSlave].doubleValue();
                weightVal = weightVal + indOfFact[indexSlave].doubleValue();
            }
        }
        if ("平均".equals(ind.getFunctionName())) {
            summaryVal = summaryVal / dimInstanceNum;
        }
        if ("加权平均".equals(ind.getFunctionName())) {
            if (weightVal == 0) {
                summaryVal = 0;
            } else {
                summaryVal = summaryVal / weightVal;
            }
        }
        return summaryVal;
    }

    @Override
    public void backup(Integer id) {
        ReportDto reportDto = new ReportDto();
        try {
            DimTableInfoVo dimTableInfoVo = getByIdExt(id);
            reportDto.setTaskId(IdUtils.getSnowflakeNextIdStr()).setState(StateEnum.RUNNING.getCode()).setTaskName("备份至数据库-").setStartTime(System.currentTimeMillis());
            // 注册报告对象
            TaskReport.put(reportDto);
            String tableName = dimTableInfoVo.getId().toString();
            CubeServer cubeServer = CubeSchema.get().getOrCreateTable(tableName, k -> {
                List<String> dimIdList = dimTableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                List<String> indIdList = dimTableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                CubeMetaData cubeMetaData = new CubeMetaData(tableName, dimIdList, indIdList);
                FactTable.FactTableBuilder builder = new FactTable.FactTableBuilder().build(tableName)
                        .addDimColumns(cubeMetaData.getDimIds())
                        .addIndColumns(cubeMetaData.getIndIds());
                return new CubeServer(builder.done());
            });
            // 创建读对象
            CubeGridReader reader = new CubeGridReader(dimTableInfoVo, cubeServer, reportDto, null);
            // 创建写对象
            CubeRdmsWriter writer = new CubeRdmsWriter(dimTableInfoVo, cubeServer, reportDto, null);
            // 创建数据处理任务
            TaskSingleContainer container = new TaskSingleContainer(reportDto.getTaskId(), 1, reader, writer, null);
            // 启动数据处理任务
            container.run();
        } catch (Exception e) {
            log.error("备份时出现异常", e);
        } finally {
            TaskReport.finished(reportDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rename(DimTableDto dto) {
        DimTable dimTable = new DimTable();
        dimTable.setId(dto.getId());
        dimTable.setTableName(dto.getTableName());
        dimTable.setType(dto.getType());
        // 验证名称或代码
        long count = this.count(dimTable);
        if (count > 0) {
            throw new ServiceException(String.format("数方名称[%s]已存在。", dto.getTableName()));
        }
        return this.update(Wrappers.<DimTable>lambdaUpdate()
                .set(DimTable::getTableName, dto.getTableName())
                .eq(DimTable::getId, dto.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(Integer[] ids) {
        List<Integer> idList = Arrays.asList(ids);
        this.removeByIds(idList);
        // 删除流程
        wfDefineService.deleteWfDefine(idList, WfDefineFlowTypeEnums.CUBE);
    }

}
