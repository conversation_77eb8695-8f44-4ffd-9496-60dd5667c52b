package com.dcube.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.DataFoundationTable;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.DataFoundationDataVersionQuery;
import com.dcube.biz.query.DataFoundationTableQuery;
import com.dcube.biz.query.SubTableListQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.vo.*;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.exception.ai.TableStructureException;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据底表服务
 */
public interface IDataFoundationTableService extends IService<DataFoundationTable> {

    DataFoundationTableVo get(Integer id);

    List<TableMetaJson> preview(DataFoundationTableViewDto viewDto);

    Map<String, Object> loadData(DataFoundationTableViewDto viewDto);

    Boolean testConnection(DataFoundationTableViewDto viewDto);

    DataFoundationTable saveOrUpdate(DataFoundationTableDto dto);

    void setColumn(DataFoundationTableDto table);

    long count(Integer id, String name);

    List<DataFoundationTable> list(DataFoundationTableQuery query);

    boolean remove(Integer id);

    PageInfo<MemTableDataVo> queryTableData(DataFoundationTableQuery query);

    Map<String, Object> avgAndSum(DataFoundationTableQuery query);

    Map<String, Object> loadViewData(LoadViewDto dto);

    Boolean generateView(GenerateViewDto dto);

    List<TableVersionSelectVo> queryDataVersion(DataFoundationDataVersionQuery query);

    String getTableStructure(Integer tableId);

    String buildRequiredConditionSql(LoadViewDto dto, DataFoundationTable table, SourceVO dataFoundationSource, String sql);
}
