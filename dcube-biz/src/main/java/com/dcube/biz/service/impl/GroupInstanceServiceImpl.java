package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Group;
import com.dcube.biz.domain.GroupInstance;
import com.dcube.biz.dto.GroupInstanceDto;
import com.dcube.biz.mapper.GroupInstanceMapper;
import com.dcube.biz.query.GroupInstanceListQuery;
import com.dcube.biz.service.IGroupInstanceService;
import com.dcube.biz.service.IGroupService;
import com.dcube.biz.vo.GroupInstanceTreeVo;
import com.dcube.common.enums.BooleanEnum;
import com.dcube.common.enums.ValueType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GroupInstanceServiceImpl extends ServiceImpl<GroupInstanceMapper, GroupInstance> implements IGroupInstanceService {

    @Autowired
    private IGroupService groupService;

    @Override
    public List<GroupInstanceTreeVo> tree(GroupInstanceListQuery query, String excludeGroupName) {

        List<GroupInstanceTreeVo> treeVoList = new ArrayList<>();

        QueryWrapper<Group> groupQw = new QueryWrapper<>();
        groupQw.lambda().like(StringUtils.isNotEmpty(query.getGroupName()), Group::getGroupName, query.getGroupName());
        groupQw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), Group::getStatus, query.getStatus());
        groupQw.lambda().ne(StringUtils.isNotEmpty(excludeGroupName), Group::getGroupName, excludeGroupName);
        groupQw.lambda().orderByDesc(Group::getCreateTime);
        List<Group> groupList = groupService.list(groupQw);

        Map<String, List<GroupInstanceTreeVo>> tMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(groupList)) {
            List<String> groupIdList = groupList.stream().map(Group::getId).collect(Collectors.toList());

            QueryWrapper<GroupInstance> qw = new QueryWrapper<>();
            qw.lambda().in(GroupInstance::getGroupId, groupIdList);
            qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), GroupInstance::getStatus, query.getStatus());
            qw.lambda().orderByDesc(GroupInstance::getGroupId).orderByDesc(GroupInstance::getCreateTime);
            List<GroupInstance> groupInstanceList = this.list(qw);

            if (CollectionUtil.isNotEmpty(groupInstanceList)) {
                List<GroupInstanceTreeVo> tList = new ArrayList<>();
                for (GroupInstance groupInstance : groupInstanceList) {
                    GroupInstanceTreeVo treeVo = new GroupInstanceTreeVo();
                    BeanUtils.copyProperties(groupInstance, treeVo);
                    treeVo.setParentId(groupInstance.getGroupId());
                    tList.add(treeVo);
                }
                tMap = tList.stream().collect(Collectors.groupingBy(GroupInstanceTreeVo::getGroupId));
            }
        }

        for (Group group : groupList) {
            GroupInstanceTreeVo treeVo = new GroupInstanceTreeVo();
            treeVo.setId(group.getId());
            treeVo.setGroupInstanceName(group.getGroupName());
            treeVo.setParentId(BizConstants.TREE_ROOT_ID);
            treeVoList.add(treeVo);
            if (tMap.containsKey(group.getId())) {
                treeVoList.addAll(tMap.get(group.getId()));
            }
        }
        return treeVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(GroupInstanceDto dto) {
        GroupInstance domain = new GroupInstance();
        BeanUtils.copyProperties(dto, domain);
        // 验证名称
        long count = this.count(domain.getId(), domain.getGroupId(), domain.getGroupInstanceName());
        if (count > 0) {
            log.error(String.format("分组实例名称[%s]已存在。", domain.getGroupInstanceName()));
            throw new ServiceException(String.format("分组实例名称[%s]已存在。", domain.getGroupInstanceName()));
        }

        if (CharSequenceUtil.isBlank(domain.getId())) {
            this.save(domain);
        } else {
            this.updateById(domain);
        }

        return true;
    }

    private long count(String id, String groupId, String name) {
        return this.count(new QueryWrapper<GroupInstance>().lambda()
                .ne(CharSequenceUtil.isNotBlank(id), GroupInstance::getId, id)
                .eq(GroupInstance::getGroupId, groupId)
                .eq(GroupInstance::getGroupInstanceName, name));
    }

    @Override
    public List<GroupInstanceDto> queryByStorageTypes(ValueType[] valueTypes) {
        List<GroupInstanceDto> groupInstanceDtos = new ArrayList<>();
        List<GroupInstance> groupInstances = this.list(new LambdaQueryWrapper<GroupInstance>()
                .in(GroupInstance::getStorageType, valueTypes)
                .eq(GroupInstance::getDicFlag, BooleanEnum.N.getCode())
        );
        if (CollectionUtil.isNotEmpty(groupInstances)) {
            for (GroupInstance groupInstance : groupInstances) {
                Group group = groupService.getById(groupInstance.getGroupId());
                if (Objects.nonNull(group)) {
                    GroupInstanceDto dto = new GroupInstanceDto();
                    BeanUtils.copyProperties(groupInstance, dto);
                    groupInstanceDtos.add(dto);
                    continue;
                }
            }
        }
        return groupInstanceDtos;
    }
}
