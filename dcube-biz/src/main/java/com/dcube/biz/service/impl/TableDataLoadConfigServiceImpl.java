package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.domain.TableDataLoadConfig;
import com.dcube.biz.dto.TableDataLoadConfigDTO;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.TableDataLoadConfigMapper;
import com.dcube.biz.service.ITableDataLoadConfigService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.service.IViewService;
import com.dcube.biz.vo.TableDataLoadConfigVO;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.enums.CreateMode;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TableDataLoadConfigServiceImpl extends ServiceImpl<TableDataLoadConfigMapper, TableDataLoadConfig> implements ITableDataLoadConfigService {

    @Autowired
    private ITableService tableService;

    @Autowired
    private IViewService viewService;

    @Override
    public List<TableDataLoadConfigVO> query(Long tableId, String viewId) {
        TableVo tableVo = tableService.get(Math.toIntExact(tableId));
        Assert.notNull(tableVo, "二维表未找到");
        List<TableMetaJson> tableMetaJsons = tableVo.getTableMetaJson();
        if (CollectionUtils.isEmpty(tableMetaJsons)) {
            return Collections.emptyList();
        }
        List<TableDataLoadConfig> list = this.list(Wrappers.<TableDataLoadConfig>lambdaQuery()
                .eq(TableDataLoadConfig::getTableId, tableId)
                .eq(TableDataLoadConfig::getViewId, viewId));
        // 如果通过数据源视图建的表，用户打开直接就能看到视图以及各列的对应关系
        if (StringUtils.equals(CreateMode.AUTO.getCode(), tableVo.getCreateMode()) && StringUtils.equals(viewId, tableVo.getViewId()) && CollectionUtils.isEmpty(list)) {
            List<TableMetaJson> viewMetaJsons = viewService.loadById(tableVo.getViewId());
            Set<String> viewMetaJsonCodes = StreamUtils.toSet(viewMetaJsons, TableMetaJson::getCode);
            return tableMetaJsons.stream()
                    .map(tableMetaJson -> {
                        TableDataLoadConfigVO tableDataLoadConfigVO = new TableDataLoadConfigVO();
                        tableDataLoadConfigVO.setTableColumnCode(tableMetaJson.getCode());
                        tableDataLoadConfigVO.setTableColumnName(tableMetaJson.getName());
                        if (CollectionUtils.isNotEmpty(viewMetaJsonCodes) && viewMetaJsonCodes.contains(tableMetaJson.getCode())) {
                            tableDataLoadConfigVO.setViewColumnCode(tableMetaJson.getCode());
                        }
                        return tableDataLoadConfigVO;
                    })
                    .collect(Collectors.toList());
        }
        Map<String, TableDataLoadConfig> map = StreamUtils.toMap(list, TableDataLoadConfig::getTableColumnCode, Function.identity());
        return tableMetaJsons.stream()
                .map(tableMetaJson -> {
                    TableDataLoadConfigVO tableDataLoadConfigVO = new TableDataLoadConfigVO();
                    tableDataLoadConfigVO.setTableColumnCode(tableMetaJson.getCode());
                    tableDataLoadConfigVO.setTableColumnName(tableMetaJson.getName());
                    TableDataLoadConfig mappedConfig = MapUtils.getObject(map, tableMetaJson.getCode());
                    if (mappedConfig != null) {
                        tableDataLoadConfigVO.setId(mappedConfig.getId());
                        tableDataLoadConfigVO.setViewColumnCode(mappedConfig.getViewColumnCode());
                    }
                    return tableDataLoadConfigVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(List<TableDataLoadConfigDTO> tableDataLoadConfigDTOS) {
        if (CollectionUtils.isEmpty(tableDataLoadConfigDTOS)) {
            return;
        }
        List<TableDataLoadConfig> tableDataLoadConfigs = BeanUtil.copyToList(tableDataLoadConfigDTOS, TableDataLoadConfig.class);
        // 优化性能
        Map<Boolean, List<TableDataLoadConfig>> groupedMap = tableDataLoadConfigs.stream()
                .collect(Collectors.partitioningBy(tableDataLoadConfig -> tableDataLoadConfig.getId() != null));
        List<TableDataLoadConfig> updateList = groupedMap.get(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        List<TableDataLoadConfig> saveList = groupedMap.get(Boolean.FALSE);
        if (CollectionUtils.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }

    }


}
