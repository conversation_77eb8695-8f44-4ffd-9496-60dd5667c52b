package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.Source;
import com.dcube.biz.dto.*;
import com.dcube.biz.query.SourceListQuery;
import com.dcube.biz.vo.SourceVO;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Map;

public interface ISourceService extends IService<Source> {

    List<Source> queryList(SourceListQuery sourceListQuery);

    List<SourceVO> queryListVO(SourceListQuery sourceListQuery);

    SourceDto getSourceById(String id);

    SourceVO getSourceVOById(String id, CryptoDTO cryptoDTO);

    Boolean saveSource(SourceAddDto source);

    List<SourceDetailDto> resolving(SourceResolvingDto source);

    Boolean updateSource(SourceDto source);

    Boolean saveOrUpdate(SourceDto dto);

    Boolean remove(@PathVariable String[] ids);

    List<Map<String, Object>> sourceTypeList();

    List<Source> queryTextSource();

    Boolean setDataFoundation(String id);

    SourceVO getDataFoundationSource();
}
