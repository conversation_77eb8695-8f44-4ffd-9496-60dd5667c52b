package com.dcube.biz.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.config.DataSourceConfig;
import com.dcube.biz.constant.enums.TableBackupConfigTypeEnum;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.domain.TableBackupVersion;
import com.dcube.biz.dto.TableBackupVersionSaveDTO;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.TableBackupVersionMapper;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.service.ITableBackupConfigService;
import com.dcube.biz.service.ITableBackupVersionService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.SourceConfigJsonVO;
import com.dcube.biz.vo.SourceVO;
import com.dcube.biz.vo.TableBackupSuffixTypeVO;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.constant.enums.*;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.*;
import com.dcube.common.utils.bean.BeanUtils;
import com.dcube.quartz.util.TaskUtil;
import com.dcube.tran.backup.AbstractBackupRepository;
import com.dcube.tran.backup.BackupRepositoryFactory;
import com.dcube.tran.plugin.BackupRdbmsWriter;
import com.dcube.tran.plugin.GridReader;
import com.dcube.tran.task.TaskSingleContainer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
public class TableBackupVersionServiceImpl extends ServiceImpl<TableBackupVersionMapper, TableBackupVersion> implements ITableBackupVersionService {

    @Autowired
    private ITableService tableService;
    @Autowired
    private DataSourceConfig backupDataSourceConfig;
    @Autowired
    private ITableBackupConfigService tableBackupConfigService;
    @Autowired
    @Qualifier("threadPoolTaskExecutor")
    private Executor threadPoolTaskExecutor;

    @Autowired
    private ISourceService sourceService;

    @Override
    public TableBackupVersion queryByTableId(Long tableId) {
        TableBackupVersion tableBackupVersion = this.getOne(Wrappers.<TableBackupVersion>lambdaQuery().eq(TableBackupVersion::getTableId, tableId));
        if (tableBackupVersion == null) {
            tableBackupVersion = new TableBackupVersion();
            tableBackupVersion.setTableId(tableId);
        }
        if (tableBackupVersion.getSuffixType() == null) {
            tableBackupVersion.setSuffixType(TableBackupSuffixTypeEnum.N);
        }
        tableBackupVersion.setSuffixTypeName(tableBackupVersion.getSuffixType().getDesc());
        return tableBackupVersion;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveTableBackupVersion(TableBackupVersionSaveDTO tableBackupVersionSaveDto) {
        Assert.hasLength(tableBackupVersionSaveDto.getVersionName(), "版本名称不能为空");
        Assert.isTrue(StringUtils.length(tableBackupVersionSaveDto.getVersionName()) <= 50, "版本名称长度不能超过50");
        if (tableBackupVersionSaveDto.getIsOverwrite() == null) {
            tableBackupVersionSaveDto.setIsOverwrite(YNEnum.N);
        }
        TableBackupVersion tableBackupVersion = new TableBackupVersion();
        BeanUtils.copyProperties(tableBackupVersionSaveDto, tableBackupVersion);
        if (tableBackupVersion.getId() != null) {
            this.updateById(tableBackupVersion);
        } else {
            this.save(tableBackupVersion);
        }
        String taskId = TaskUtil.putTableCache(Math.toIntExact(tableBackupVersionSaveDto.getTableId()), JobOperateTypeEnums.BACKUP);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 异步处理备份数据库
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    threadPoolTaskExecutor.execute(() -> {
                        try {
                            SecurityUtils.setLoginUser(loginUser);
                            backup(tableBackupVersion);
                        } finally {
                            SecurityUtils.remove();
                        }
                    });
                }
            });
        } else {
            threadPoolTaskExecutor.execute(() -> {
                SecurityUtils.setLoginUser(loginUser);
                try {
                    backup(tableBackupVersion);
                } finally {
                    SecurityUtils.remove();
                }
            });
        }
        return TaskUtil.getTaskIdAjaxResult(taskId);
    }

    @Override
    public List<TableBackupSuffixTypeVO> getTableBackupSuffixType() {
        return Arrays.stream(TableBackupSuffixTypeEnum.values())
                .map(value -> {
                    TableBackupSuffixTypeVO tableBackupSuffixTypeVo = new TableBackupSuffixTypeVO();
                    tableBackupSuffixTypeVo.setCode(value.name());
                    tableBackupSuffixTypeVo.setDesc(value.getDesc());
                    return tableBackupSuffixTypeVo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void backup(TableBackupVersion tableBackupVersion) {
        ReportDto reportDto = TaskUtil.getTableCache(Math.toIntExact(tableBackupVersion.getTableId()), JobOperateTypeEnums.BACKUP);
        try {
            Assert.notNull(reportDto, "当前表的任务为空，无法执行备份操作！");
            List<TableBackupConfig> tableBackupConfigs = tableBackupConfigService.list(Wrappers.<TableBackupConfig>lambdaQuery()
                    .eq(TableBackupConfig::getTableId, tableBackupVersion.getTableId())
            );
            Assert.notEmpty(tableBackupConfigs, "当前超算表格未设置数据库备份表!");
            TableBackupConfig tableBackupConfigTable = tableBackupConfigs.stream().filter(tableBackupConfig -> tableBackupConfig.getConfigType() == TableBackupConfigTypeEnum.TABLE).findFirst().orElseThrow(() -> new ServiceException("二维表未设置数据库备份表!"));
            TableVo table = tableService.get(Math.toIntExact(tableBackupVersion.getTableId()));
            Assert.notNull(table, "二维表不存在");
            // 校验二维表的列是否都配置了
            Set<String> tableMetaCodes = StreamUtils.toSet(table.getTableMetaJson(), TableMetaJson::getCode);
            Set<String> columnCodeSet = tableBackupConfigs.stream()
                    .filter(tableBackupConfig -> tableBackupConfig.getConfigType() == TableBackupConfigTypeEnum.COLUMN)
                    .map(TableBackupConfig::getColumnCode)
                    .collect(Collectors.toSet());
            tableMetaCodes.removeAll(columnCodeSet);
            if (CollectionUtils.isNotEmpty(tableMetaCodes)) {
                throw new ServiceException("请通过修改备份表配置所有的列");
            }
            long count = MemGridUtils.getTotalSize(table.getMemTableName());
            if (count == 0) {
                throw new ServiceException("二维表数据为空");
            }
            List<String> columnList = tableBackupConfigs.stream()
                    .filter(tableBackupConfig -> tableBackupConfig.getConfigType() == TableBackupConfigTypeEnum.COLUMN)
                    .map(TableBackupConfig::getBackupColumnName)
                    .collect(Collectors.toList());
            // 添加备份版本号
            columnList.add("backup_version_name");
            //添加数据日期
            columnList.add("base_data_dt");
            String backupTableName = tableBackupConfigTable.getBackupTableNamePrefix() + tableBackupConfigTable.getBackupTableName();
            long start = System.currentTimeMillis();
            //判断是否设置了数据底座，如果设置了则取数据底座的数据源配置，否则取数据库备份配置
            SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//数据底座数据源配置
            SourceConfigJson sourceConfigJson = new SourceConfigJson(backupDataSourceConfig.getBackupUrl(), backupDataSourceConfig.getBackupUsername(), backupDataSourceConfig.getBackupPassword(), backupDataSourceConfig.getBackupDriverClassName(), null);
            if(Objects.nonNull(dataFoundationSource) && Objects.nonNull(dataFoundationSource.getSourceConfig())){
                //将columnList中的字符串都转换为小写，clickhouse区分大小写，表中的字段名都是小写
                columnList = columnList.stream().map(String::toLowerCase).collect(Collectors.toList());
                SourceConfigJsonVO sourceConfig = dataFoundationSource.getSourceConfig();
                sourceConfigJson = new SourceConfigJson(sourceConfig.getUrl(), sourceConfig.getUsername(), sourceConfig.getPassword(), sourceConfig.getDriverClass(), null);
            }
            AbstractBackupRepository tarRepository = BackupRepositoryFactory.getBackupRepository(sourceConfigJson, backupTableName, null, table.getTableLevel(), table.getTableName());
            if (tableBackupVersion.getIsOverwrite() == YNEnum.Y) {
                tarRepository.getAbstractRepository().executeSql(String.format("ALTER TABLE %s DELETE WHERE backup_version_name = '%s' and base_data_dt='%s'", backupTableName.toLowerCase(), tableBackupVersion.getVersionAllName(),DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,tableBackupVersion.getBaseDataDt())));
            }
            reportDto.setState(StateEnum.RUNNING.getCode()).setTaskName("备份至数据库-" + table.getTableName()).setTotalRecord(count).setCurrentRecord(0L).setStartTime(start).setStatus(TaskStatusEnums.START);
            // 注册报告对象
            TaskReport.put(reportDto);
            // 创建读对象
            GridReader reader = new GridReader(table.getMemTableName(), reportDto);
            // 创建写对象
            BackupRdbmsWriter writer = new BackupRdbmsWriter(tarRepository, columnList, reportDto, tableBackupVersion.getVersionAllName(),tableBackupVersion.getBaseDataDt());
            // 创建数据处理任务
            TaskSingleContainer container = new TaskSingleContainer(reportDto.getTaskId(), 1, reader, writer, null);
            // 启动数据处理任务
            container.run();
            reportDto.setStatus(TaskStatusEnums.COMPLETE).setCost(System.currentTimeMillis() - start);
            //成功后写备份版本
            if (StateEnum.SUCCEEDED.getCode().equals(reportDto.getState())) {
                List<TableMetaJson> tableMetaJsons = table.getTableMetaJson().stream()
                        .filter(tableMetaJson -> columnCodeSet.contains(tableMetaJson.getCode()))
                        .collect(Collectors.toList());
                tableService.addTableVersion(table.getId(), table.getMemTableName(), backupTableName, JSON.toJSONString(tableMetaJsons), tableBackupVersion.getUpdateBy(), tableBackupVersion.getVersionAllName(), tableBackupVersion.getBaseDataDt());
            }
        } catch (Exception e) {
            TaskUtil.updateTaskStatus(reportDto.getTaskId(), TaskStatusEnums.ERROR, ExceptionUtil.getLocalizedMessage(e));
        }
    }

}
