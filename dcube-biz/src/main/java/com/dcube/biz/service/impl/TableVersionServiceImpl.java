package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.config.DataSourceConfig;
import com.dcube.biz.domain.TableVersion;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.mapper.TableVersionMapper;
import com.dcube.biz.service.ITableVersionService;
import com.dcube.biz.vo.TableVersionSelectVo;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/16 20:55
 * @描述 二维表版本服务
 */
@Transactional
@Service
public class TableVersionServiceImpl extends ServiceImpl<TableVersionMapper, TableVersion> implements ITableVersionService {

    //    @Value("${spring.datasource.druid.master.url}")
//    private String url;
//
//    @Value("${spring.datasource.driverClassName}")
//    private String driverClassName;
//
//    @Value("${spring.datasource.druid.master.username}")
//    private String username;
//
//    @Value("${spring.datasource.druid.master.password}")
//    private String password;
    @Autowired
    private DataSourceConfig backupDataSourceConfig;

    @Override
    public TableVersion get(Integer id) {
        return this.getById(id);
    }

    @Override
    public boolean insert(TableVersion tableVersion) {
        return this.save(tableVersion);
    }

    @Override
    public List<TableVersionSelectVo> queryByTableId(Integer tableId) {
        List<TableVersion> list = this.list(new LambdaQueryWrapper<TableVersion>().eq(TableVersion::getTableId, tableId));
        return getTableVersionSelect(list);
    }

    public List<TableVersionSelectVo> getTableVersionSelect(List<TableVersion> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 按version分组，合并相同version的数据
        Map<String, List<TableVersion>> versionMap = list.stream()
                .collect(Collectors.groupingBy(TableVersion::getVersion));

        return versionMap.entrySet().stream()
                .map(entry -> {
                    TableVersionSelectVo vo = new TableVersionSelectVo();
                    // 设置id和tableId
                    vo.setId(entry.getValue().get(0).getId());
                    vo.setTableId(entry.getValue().get(0).getTableId());
                    vo.setVersion(entry.getKey());

                    // 提取所有不重复的base_data_dt，过滤掉为null的值
                    List<Date> dates = entry.getValue().stream()
                            .map(TableVersion::getBaseDataDt)
                            .filter(Objects::nonNull) // 过滤掉为null的base_data_dt
                            .distinct()
                            .collect(Collectors.toList());

                    // 设置versionDates，即使dates为空也返回空数组
                    vo.setVersionDates(dates);

                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean versionExists(Integer tableId, String version) {
        if (StringUtils.isEmpty(version)) {
            throw new ServiceException("版本号不能为空。");
        }
        return CollectionUtil.isNotEmpty(this.lambdaQuery().eq(TableVersion::getTableId, tableId).eq(TableVersion::getVersion, version).list());
    }

    @Override
    public boolean deleteVersions(List<TableVersion> todoDeletes) {
        SourceConfigJson sourceConfigJson = new SourceConfigJson(backupDataSourceConfig.getBackupUrl(), backupDataSourceConfig.getBackupUsername(), backupDataSourceConfig.getBackupPassword(), backupDataSourceConfig.getBackupDriverClassName(), null);
        AbstractRepository repository = RepositoryFactory.getRepository(sourceConfigJson, null);
        for (TableVersion table : todoDeletes) {
            if (table.getDataVersion() == 1) {
                String deleteSql = "delete from " + table.getBackupTableName() + " where backup_version_name='" + table.getVersion() + "'";
                if(Objects.nonNull(table.getBaseDataDt())){
                    deleteSql +=" and base_data_dt='"+table.getBaseDataDt()+"'";
                }
                repository.executeSql(deleteSql);
            } else if (table.getDataVersion() == 0) {
                repository.executeSql("drop table if exists " + table.getBackupTableName());
            }
        }
        List<Integer> ids = todoDeletes.stream().map(TableVersion::getId).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
