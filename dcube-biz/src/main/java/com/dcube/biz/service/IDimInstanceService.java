package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.query.InstanceTreeQuery;
import com.dcube.common.constant.enums.MoveTypeEnum;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface IDimInstanceService extends IService<DimInstance> {

    DimInstance saveOrUpdateExt(DimInstance entity);

    void saveOrUpdateExt(List<DimInstance> entityList);

    boolean remove(Integer id);

    List<DimInstance> list(InstanceTreeQuery query);

    List<DimInstance> listByDimDirectoryIds(Collection<? extends Serializable> dimDirectoryIds);

    Boolean move(Long id, MoveTypeEnum moveType);

}
