package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.mapper.DimInstanceMapper;
import com.dcube.biz.query.InstanceTreeQuery;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.common.annotation.ThreadLocalCache;
import com.dcube.common.constant.enums.MoveTypeEnum;
import com.dcube.common.enums.ThreadLocalCacheType;
import com.dcube.common.exception.ServiceException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DimInstanceServiceImpl extends ServiceImpl<DimInstanceMapper, DimInstance> implements IDimInstanceService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DimInstance saveOrUpdateExt(DimInstance entity) {
        if (entity.getDimDirectoryId() == null || entity.getDimDirectoryId() <= 0) {
            log.error("维度目录ID属性值不能为空。");
            throw new ServiceException("维度目录ID属性值不能为空。");
        }

        // 验证名称或代码
        long count = this.count(entity);
        if (count > 0) {
            log.error(String.format("维度成员名称[%s]已存在。", entity.getDimName()));
            throw new ServiceException(String.format("维度成员名称[%s]已存在。", entity.getDimName()));
        }

        // 兼容设置默认根根代码
        if (entity.getParentId() == null || entity.getParentId() == 0) {
            entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
            entity.setAncestors("0");
        } else {
            DimInstance entityParent = this.getById(entity.getParentId());
            entity.setAncestors(entityParent.getAncestors());

            // 更新父节点叶子属性值
            entityParent.setIsLeaf(BizConstants.FLAG_FALSE);
            this.updateById(entityParent);
        }

        if (entity.getId() == null || entity.getId() <= 0) {
            // 显示序号
            DimInstance maxIndexNo = this.baseMapper.getMaxIndexNo(entity.getDimDirectoryId(), entity.getParentId());
            if (Objects.nonNull(maxIndexNo)) {
                if (Objects.nonNull(maxIndexNo.getIndexNo())) {
                    entity.setIndexNo(maxIndexNo.getIndexNo() + 1);
                }
            }
            if (entity.getIndexNo() == null) {
                entity.setIndexNo(0);
            }
            this.save(entity);
            entity.setIsLeaf(BizConstants.FLAG_TRUE);
        } else {
            // 同步修改之前叶子属性值
            DimInstance entitySelf = this.getById(entity.getParentId());
            if (entitySelf != null) {
                entity.setIsLeaf(entitySelf.getIsLeaf());
                this.updateById(entity);
            }
        }
        // 追加当前ID至节点路径ancestors
        entity.setAncestors(entity.getAncestors() + "," + entity.getId());
        this.updateById(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateExt(List<DimInstance> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            log.error("维度实例集合不能为空。");
            throw new ServiceException("维度实例集合不能为空。");
        }

        Map<Integer, DimInstance> pmap = new HashMap<>();
        Set<Integer> aid = new HashSet<>();
        for (DimInstance entity : entityList) {
            pmap.put(entity.getParentId(), null);
            aid.add(entity.getId());
        }

        DimInstance root = null;
        for (DimInstance entity : entityList) {
            if (!aid.contains(entity.getParentId())) {
                entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
                root = entity;
            }
        }
        if (root == null) {
            log.error("维度实例集合无主节点。");
            throw new ServiceException("维度实例集合无主节点。");
        }

        for (DimInstance entity : entityList) {
            Integer bid = entity.getId();
            Integer bpid = entity.getParentId();
            if (BizConstants.TREE_ROOT_ID_INT.intValue() == entity.getParentId()) {
                // 根节点
                entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
                entity.setAncestors("0");
                entity.setIsLeaf(BizConstants.FLAG_FALSE);
            } else if (pmap.containsKey(bid)) {
                // 父节点
                DimInstance pEntity = pmap.get(bpid);
                entity.setParentId(pEntity.getId());
                entity.setAncestors(pEntity.getAncestors());
                entity.setIsLeaf(BizConstants.FLAG_FALSE);
            } else {
                // 子节点
                DimInstance pEntity = pmap.get(bpid);
                entity.setParentId(pEntity.getId());
                entity.setAncestors(pEntity.getAncestors());
                entity.setIsLeaf(BizConstants.FLAG_TRUE);
            }

            this.saveOrUpdateExt(entity);

            // 追加当前ID至节点路径ancestors
            entity.setAncestors(entity.getAncestors() + "," + entity.getId());
            this.updateById(entity);

            // 保存新增父节点镜像
            if (BizConstants.TREE_ROOT_ID_INT.intValue() == entity.getParentId() || pmap.containsKey(bid)) {
                pmap.put(bid, entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Integer id) {
        DimInstance entity = this.getById(id);
        if (entity.getParentId() != null && entity.getParentId() != 0) {
            // 更新父节点isLeaf
            DimInstance entityParent = this.getById(entity.getParentId());
            if (entityParent != null) {
                long count = this.count(new QueryWrapper<DimInstance>().lambda()
                        .eq(DimInstance::getDimDirectoryId, entity.getDimDirectoryId())
                        .eq(DimInstance::getParentId, entity.getParentId()));
                if (count == 1) {
                    entityParent.setIsLeaf(BizConstants.FLAG_TRUE);
                    this.updateById(entityParent);
                }
            }
        }

        Set<Integer> deleteIds = new HashSet<>();
        deleteIds.add(id);
        // 删除子节点
        List<DimInstance> subList = this.baseMapper.selectSubById(id);
        if (CollectionUtils.isNotEmpty(subList)) {
            deleteIds.addAll(subList.stream().map(DimInstance::getId).collect(Collectors.toSet()));
        }
        return this.removeByIds(deleteIds);
    }

    @Override
    public List<DimInstance> list(InstanceTreeQuery query) {
        QueryWrapper<DimInstance> qw = new QueryWrapper<>();
        qw.lambda().like(CharSequenceUtil.isNotBlank(query.getDimName()), DimInstance::getDimName, query.getDimName());
        qw.lambda().eq(query.getDimDirectoryId() != null, DimInstance::getDimDirectoryId, query.getDimDirectoryId());
        qw.lambda().eq(query.getParentId() != null, DimInstance::getParentId, query.getParentId());
        qw.lambda().orderByAsc(DimInstance::getIndexNo).orderByAsc(DimInstance::getAncestors);
        return this.list(qw);
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public List<DimInstance> listByDimDirectoryIds(Collection<? extends Serializable> dimDirectoryIds) {
        return this.list(new QueryWrapper<DimInstance>().lambda().in(DimInstance::getDimDirectoryId, dimDirectoryIds).orderByDesc(DimInstance::getDimDirectoryId).orderByAsc(DimInstance::getAncestors));
    }

    private long count(DimInstance entity) {
        return this.count(new QueryWrapper<DimInstance>().lambda()
                .ne(entity.getId() != null, DimInstance::getId, entity.getId())
                .eq(DimInstance::getDimDirectoryId, entity.getDimDirectoryId())
                .eq(DimInstance::getDimName, entity.getDimName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean move(Long id, MoveTypeEnum moveType) {
        if (moveType == MoveTypeEnum.UP) {
            return moveUp(id);
        } else {
            return moveDown(id);
        }
    }

    public Boolean moveUp(Long id) {
        DimInstance dimInstance = this.getById(id);
        Assert.notNull(dimInstance, "当前维度不存在");
        Integer currentOrder = dimInstance.getIndexNo();
        Assert.notNull(currentOrder, "当前维度序号为空，不支持上移");
        DimInstance previousDimRule = this.baseMapper.getPreviousDimInstanceByAncestorAndIndexNo(dimInstance.getDimDirectoryId(), dimInstance.getParentId(), currentOrder);
        Assert.notNull(previousDimRule, "当前维度无法上移");
        swapSortOrder(dimInstance, previousDimRule);
        return Boolean.TRUE;
    }

    public Boolean moveDown(Long id) {
        DimInstance dimInstance = this.getById(id);
        Assert.notNull(dimInstance, "当前维度不存在");
        Integer currentOrder = dimInstance.getIndexNo();
        Assert.notNull(currentOrder, "当前维度序号为空，不支持下移");
        DimInstance nextDimRule = this.baseMapper.getNextDimInstanceByAncestorAndIndexNo(dimInstance.getDimDirectoryId(), dimInstance.getParentId(), currentOrder);
        Assert.notNull(nextDimRule, "当前维度无法下移");
        swapSortOrder(dimInstance, nextDimRule);
        return Boolean.TRUE;
    }

    private void swapSortOrder(DimInstance dimRule1, DimInstance dimRule2) {
        Integer tempOrder = dimRule1.getIndexNo();
        dimRule1.setIndexNo(dimRule2.getIndexNo());
        dimRule2.setIndexNo(tempOrder);
        this.updateById(dimRule1);
        this.updateById(dimRule2);
    }


}
