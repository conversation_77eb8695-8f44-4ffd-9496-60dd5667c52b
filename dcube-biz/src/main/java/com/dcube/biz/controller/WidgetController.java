package com.dcube.biz.controller;

import com.dcube.biz.dto.BatchReportDto;
import com.dcube.biz.dto.BatchWidgetDto;
import com.dcube.biz.dto.WidgetDto;
import com.dcube.biz.dto.WidgetParamDto;
import com.dcube.biz.query.WidgetListQuery;
import com.dcube.biz.query.WidgetViewListQuery;
import com.dcube.biz.service.IWidgetService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/14 15:35
 * @描述
 */
@RestController
@RequestMapping("/widget")
@Tag(name = "DCUBE-可视化-组件", description = "DCUBE-可视化-组件")
@Slf4j
public class WidgetController extends BaseController {

    @Autowired
    private IWidgetService widgetService;

    @GetMapping
    @Operation(summary = "根据dashboardId查询组件列表")
    public AjaxResult getWidgetList(WidgetListQuery query) {
        return AjaxResult.success(widgetService.getWidgetsByDashboardId(query));
    }

    @Operation(summary = "主键查询")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(widgetService.get(id));
    }

    @Log(title = "组件", businessType = BusinessType.INSERT)
    @Operation(summary = "新增/更新组件")
    @PostMapping
    public AjaxResult add(@RequestBody WidgetDto dto) {
        return AjaxResult.success().put("id", widgetService.saveOrUpdate(dto));
    }

    @Log(title = "批量新增/更新组件", businessType = BusinessType.INSERT)
    @Operation(summary = "批量新增/更新组件")
    @PostMapping("/batchSaveOrUpdate")
    public AjaxResult batchSaveOrUpdate(@RequestBody BatchWidgetDto widgetDto) {
        widgetService.batchSaveOrUpdate(widgetDto);
        return AjaxResult.success();
    }

    @Log(title = "批量新增/更新报告", businessType = BusinessType.INSERT)
    @Operation(summary = "批量新增/更新报告")
    @PostMapping("/batchSaveOrUpdateReport")
    public AjaxResult batchSaveOrUpdateReport(@RequestBody BatchReportDto reportDto) {
        widgetService.batchSaveOrUpdateReport(reportDto);
        return AjaxResult.success();
    }

    @Log(title = "组件", businessType = BusinessType.DELETE)
    @Operation(summary = "主键数组删除")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(widgetService.removeWidget(id));
    }

    @Operation(summary = "预览组件数据")
    @PostMapping("/preview")
    public AjaxResult preview(@RequestBody WidgetParamDto dto) {
        return AjaxResult.success(widgetService.preview(dto));
    }

    @GetMapping("/queryViewList")
    @Operation(summary = "查询视图列表")
    public AjaxResult queryViewList(WidgetViewListQuery query) {
        return AjaxResult.success(widgetService.queryList(query));
    }

    @GetMapping("/aggregationType")
    @Operation(summary = "统计函数")
    public AjaxResult aggregationType() {
        return AjaxResult.success(widgetService.aggregationTypeList());
    }
}
