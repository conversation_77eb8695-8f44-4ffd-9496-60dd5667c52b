package com.dcube.biz.controller;

import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.service.IViewService;
//import com.dcube.biz.util.DBUtil;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.vo.SourceConfigJsonVO;
import com.dcube.common.annotation.Anonymous;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.grid.MemorySchema;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.gson.Gson;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.SchemaPlus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Anonymous
@RestController
@RequestMapping("test")
@Tag(name = "DCUBE-测试", description = "DCUBE-测试")
public class TestController extends BaseController {

    @Autowired
    private IViewService viewService;
    @Autowired
    private ISourceService sourceService;
    private final Gson gson = new Gson();

    @GetMapping("sql")
    @Operation(summary = "SQL执行")
    public AjaxResult sql(@RequestParam("sql") String sql) {
        AbstractRepository repository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, viewService.get("1").getSourceConfig(), "hr.employee");
        List<Map<String, Object>> rs = repository.executeQuerySql(sql);
        return AjaxResult.success(rs);
    }

    @GetMapping("sql1")
    @Operation(summary = "SQL1执行")
    public AjaxResult sql1(@RequestParam("sql") String sql) throws SQLException, ClassNotFoundException {
        Class.forName("org.apache.calcite.jdbc.Driver");
        Properties info = new Properties();
        info.setProperty("lex", "JAVA");
        info.setProperty("fun", "oracle");
        Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
        CalciteConnection calciteConnection = connection.unwrap(CalciteConnection.class);
        SchemaPlus rootSchema = calciteConnection.getRootSchema();
        Schema schema = MemorySchema.INSTANCE;
        rootSchema.add("grid", schema);
        Statement statement = calciteConnection.createStatement();
        ResultSet resultSet = statement.executeQuery(sql);

        /**
         * 遍历 SQL 执行结果
         */
        while (resultSet.next()) {
            for (int i = 1; i <= resultSet.getMetaData().getColumnCount(); i++) {
                System.out.print(resultSet.getMetaData().getColumnName(i) + ":" + resultSet.getObject(resultSet.getMetaData().getColumnName(i)));
                System.out.print(" | ");
            }
            System.out.println();
        }
        resultSet.close();
        statement.close();
        connection.close();
        return AjaxResult.success();
    }
//
//    @GetMapping("insertBatch")
//    @Operation(summary = "SQL执行")
//    public AjaxResult insertBatch(@RequestParam("count") Integer count) {
//        DBUtil.batchInsert(count);
//        return AjaxResult.success();
//    }
}
