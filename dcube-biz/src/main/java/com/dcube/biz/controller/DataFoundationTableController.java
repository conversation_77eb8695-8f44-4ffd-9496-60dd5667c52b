package com.dcube.biz.controller;

import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DataFoundationTable;
import com.dcube.biz.dto.DataFoundationTableDto;
import com.dcube.biz.dto.DataFoundationTableViewDto;
import com.dcube.biz.dto.GenerateViewDto;
import com.dcube.biz.dto.LoadViewDto;
import com.dcube.biz.query.DataFoundationDataVersionQuery;
import com.dcube.biz.query.DataFoundationTableQuery;
import com.dcube.biz.service.IDataFoundationTableService;
import com.dcube.common.annotation.Anonymous;
import com.dcube.common.annotation.Log;
import com.dcube.common.annotation.RepeatSubmit;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Anonymous
@RestController
@RequestMapping("/dataFoundationTable")
@Tag(name = "DCUBE-数据底表", description = "DCUBE-数据底表")
@Slf4j
public class DataFoundationTableController extends BaseController {

    @Autowired
    private IDataFoundationTableService dataFoundationTableService;

    @GetMapping("/list")
    @Operation(summary = "查询数据底表列表")
    public AjaxResult list(DataFoundationTableQuery query) {
        return AjaxResult.success(dataFoundationTableService.list(query));
    }

    @Operation(summary = "主键查询")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(dataFoundationTableService.get(id));
    }

    @Log(title = "数据底表", businessType = BusinessType.INSERT)
    @Operation(summary = "新增数据底表")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@RequestBody DataFoundationTableDto dto) {
        dto.setStatus(BizConstants.STATUS_ENABLE);
        DataFoundationTable table = dataFoundationTableService.saveOrUpdate(dto);
        return AjaxResult.success(table.getId());
    }

    @Log(title = "数据底表-设置列", businessType = BusinessType.INSERT)
    @Operation(summary = "数据底表-设置列")
    @PostMapping("/setColumn")
    public AjaxResult setColumn(@RequestBody DataFoundationTableDto dto) {
        dataFoundationTableService.setColumn(dto);
        return AjaxResult.success();
    }

    @Log(title = "数据底表", businessType = BusinessType.UPDATE)
    @Operation(summary = "主键修改")
    @PostMapping("/put")
    public AjaxResult edit(@RequestBody DataFoundationTableDto dto) {
        dataFoundationTableService.saveOrUpdate(dto);
        return AjaxResult.success();
    }

    @Operation(summary = "运行sql")
    @PostMapping("/loadData")
    public AjaxResult loadData(@RequestBody DataFoundationTableViewDto viewDto) {
        return AjaxResult.success(dataFoundationTableService.loadData(viewDto));
    }

    @Operation(summary = "测试数据源连接")
    @PostMapping("/testConnection")
    public AjaxResult connection(@RequestBody DataFoundationTableViewDto viewDto) {
        Boolean success = dataFoundationTableService.testConnection(viewDto);
        return success ? AjaxResult.success("数据库连接成功。") : AjaxResult.error("连接失败,请检查连接配置。");
    }

    @Log(title = "数据底表", businessType = BusinessType.DELETE)
    @Operation(summary = "删除数据底表")
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable("id")Integer id) {
        return AjaxResult.success(dataFoundationTableService.remove(id));
    }

    @PostMapping("/listData")
    @Operation(summary = "查询数据底表数据")
    public TableDataInfo listData(@RequestBody DataFoundationTableQuery query) {
        return getDataTable(dataFoundationTableService.queryTableData(query));
    }

    @PostMapping("/avgAndSum")
    @Operation(summary = "平均值与求和")
    public AjaxResult avgAndSum(@RequestBody DataFoundationTableQuery query) {
        return AjaxResult.success(dataFoundationTableService.avgAndSum(query));
    }

    @Operation(summary = "预览视图")
    @PostMapping("/loadViewData")
    public AjaxResult loadViewData(@RequestBody LoadViewDto dto) {
        return AjaxResult.success(dataFoundationTableService.loadViewData(dto));
    }

    @Operation(summary = "生成视图")
    @PostMapping("/generateView")
    public AjaxResult generateView(@RequestBody GenerateViewDto dto) {
        return AjaxResult.success(dataFoundationTableService.generateView(dto));
    }

    @Operation(summary = "查询数据版本")
    @PostMapping("/queryDataVersion")
    public AjaxResult queryDataVersion(@RequestBody DataFoundationDataVersionQuery query) {
        return AjaxResult.success(dataFoundationTableService.queryDataVersion(query));
    }

    @Operation(summary = "获取数据底表结构")
    @GetMapping("/getTableStructure/{id}")
    public AjaxResult getTableStructure(@PathVariable("id") Integer id) {
        return AjaxResult.success(dataFoundationTableService.getTableStructure(id));
    }
}
