package com.dcube.biz.controller;

import com.dcube.biz.service.ITableDataLoadConfigService;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/2dTableDataLoadConfig")
@Tag(name = "DCUBE-二维表数据加载配置表", description = "DCUBE-二维表数据加载配置表")
public class TableDataLoadConfigController extends BaseController {

    @Autowired
    private ITableDataLoadConfigService tableDataLoadConfigService;

    @Operation(summary = "查询二维表数据加载配置表")
    @GetMapping("/query")
    public AjaxResult query(@RequestParam("tableId") Long tableId, @RequestParam("viewId") String viewId) {
        return AjaxResult.success(tableDataLoadConfigService.query(tableId, viewId));
    }

//    @Operation(summary = "保存二维表数据加载配置表")
//    @Log(title = "保存二维表数据加载配置表", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult saveTableBackupConfig(@RequestBody List<TableDataLoadConfigDTO> tableDataLoadConfigDTOS) {
//        tableDataLoadConfigService.save(tableDataLoadConfigDTOS);
//        return AjaxResult.success("保存成功");
//    }

}
