package com.dcube.web.controller.system;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.utils.StringUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
@Tag(name = "首页")
@RestController
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private DCubeConfig dCubeConfig;

    /**
     * 访问首页，提示语
     */
    @Operation(summary = "首页")
    @RequestMapping("/")
    public String index()
    {
        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", dCubeConfig.getName(), dCubeConfig.getVersion());
    }
}
