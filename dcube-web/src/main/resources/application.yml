# jasypt 密码加密配置
jasypt:
  encryptor:
    # 加密盐值
    #password:
    # 加密算法设置 3.0.0 以后
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
# 项目相关配置
dcube:
  # 名称
  name: 超算力即时分析平台
  # 版本
  version: 1.0
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  profile: ./uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: char
  # 导入用户时的默认密码
  userDefaultPassWord: 123456
  rsa:
    private-key: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKX03Xr6hEBBbiRhmbqTDw/9DW5NV7oUBg/BQSul+jXas6YBUZ/VVRvV/IMlMfEukDv55YheWJ5dSQlchy+Cgb8Gu3yF7j1V2bd4LkxChPX9ISvCPGpNzMh/XXh0iYg5Cp3l8ypWwNF68Ge5mbR3J+N/OBjDUL+HUGP4A0G97v6hAgMBAAECgYEAmwO+XcDjB6IWX6VLExuKVkTo5r8X0pghkVXSqhK/RM6kP8u7krCSbw3ScC2uGLqFv4ANBvQZktNQBY6IN7iPt32SE9zO+Y3Ld53PSTt8WuF9Xcw01h0yhnfBLpzAsWjkWuScHmsLE6Cw0KXQLJ1llKBN4P2w/w6JhHqv8AxSdgECQQDb9Dm2tHapI0M+5KslR2t4VN8257LN8M9wQG0jkyuebN9Ox8J3j5vh49EUxwl07ukC3J951m9vTcx9igdT2AkdAkEAwSdFjrZFJjON4O/OTP2/p5+9UMXrewFtcMVdW3hgcdfw3qrsufNBdW8kZlMxcTfVIAKcbwGephen67wYCbpYVQJBALGCJdmXRnQ1qx1kueI6Azd3dzT+REUDZMW4mNuu9/LA9m7Ds+N9mFXy31efUbh23sGaf3F6ZzAyXyepb0RxAtkCQGXOfEQnYDUtcTfBThyYlp8UfCWJ3H9eFkAdVwKyZSs8JXJH5zRvue3a4fWpHeNOCWCw36FG2kczKsf+wTDHbm0CQQCMak9g7tH5gq/2zOq39oNvP2jmg+i1op1HtnMTBqTQkW751O085OCuEgwiwjbKggOAbhp0GG35pp5L9p8QRRaV
    public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCl9N16+oRAQW4kYZm6kw8P/Q1uTVe6FAYPwUErpfo12rOmAVGf1VUb1fyDJTHxLpA7+eWIXlieXUkJXIcvgoG/Brt8he49Vdm3eC5MQoT1/SErwjxqTczIf114dImIOQqd5fMqVsDRevBnuZm0dyfjfzgYw1C/h1Bj+ANBve7+oQIDAQAB
  global-exception:
    show-detail: false

netty:
  port: 8999
  path: /dcube/ws
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8090
  port: 8090
  servlet:
    # 应用的访问路径
    context-path: /dcube
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    keep-alive-timeout: 600000
    max-keep-alive-requests: 1000
  websocket:
    exporter: true # 宝兰德部署时改为false
# 日志配置
logging:
  level:
    com.dcube: debug
    org.springframework: warn
    dev.langchain4j: debug
    springfox: off

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # liquibase数据库版本管理
  liquibase:
    enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    #    active: postgres
    #    active: opengauss
    #      active: local
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 200MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    async:
      request-timeout: 600000
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcDefghijklmnopqrstUvwxyz123Dcube321AbcDeFGHIJKLMNOPQRSTuVWXYZ456Dcube654abcDefghijklmnopqrstUvwxyz789Dcube987AbcDeFGHIJKLMNOPQRSTuVWXYZ
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.dcube.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml


# 接口文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  packages-to-scan: com.dcube
knife4j:
  # 生产环境屏蔽
  production: false
  # Swagger的Basic认证功能,默认是false
  basic:
    enable: false
    # Basic认证用户名
    username: dcube
    # Basic认证密码
    password: dcube
  # 是否开启Knife4j增强模式,默认是 false
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 实体类列表

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

license:
  validate: false
  authCode: rO0ABXNyAChjb20uZGN1YmUuZnJhbWV3b3JrLmxpY2Vuc2Uudm8uTGljZW5zZVZvvFSHnL/ZNNkCAAdMAAhjdXN0b21lcnQAK0xjb20vZGN1YmUvZnJhbWV3b3JrL2xpY2Vuc2Uvdm8vQ3VzdG9tZXJWbztMAApleHBpcmVEYXRldAAQTGphdmEvdXRpbC9EYXRlO0wAD2V4cGlyZVRpbWVTdGFtcHQAEExqYXZhL2xhbmcvTG9uZztMAAJpZHQAEkxqYXZhL2xhbmcvU3RyaW5nO0wACWlzc3VlRGF0ZXEAfgACTAAOaXNzdWVUaW1lU3RhbXBxAH4AA0wACm1hY0FkZHJlc3N0AA9MamF2YS91dGlsL1NldDt4cHNyACljb20uZGN1YmUuZnJhbWV3b3JrLmxpY2Vuc2Uudm8uQ3VzdG9tZXJWb1HzQJPhNan3AgAGTAAEY2l0eXEAfgAETAAMY3VzdG9tZXJOYW1lcQB+AARMAApjdXN0b21lck5vcQB+AARMAAJpZHEAfgAETAAIaW5kdXN0cnlxAH4ABEwACHByb3ZpbmNlcQB+AAR4cHQABuatpuaxiXQADOS8geeul+aVsOenkXQABFFTU0t0ACRmNTVmMDlkYi1iN2MwLTQ4MDItOTM3Zi1kYzRiYmE3M2RjNzl0AAbnp5HmioB0AAbmuZbljJdzcgAOamF2YS51dGlsLkRhdGVoaoEBS1l0GQMAAHhwdwgAAAGV623WmHhzcgAOamF2YS5sYW5nLkxvbmc7i+SQzI8j3wIAAUoABXZhbHVleHIAEGphdmEubGFuZy5OdW1iZXKGrJUdC5TgiwIAAHhwAAABlett1ph0ACRiMWFmOWMwMS1mNTQ1LTQ1YjAtYWIyNC0yMDhmYmMyN2ZiODlzcQB+AA93CAAAAZXrfOEMeHNxAH4AEQAAAZXrfOEMc3IAEWphdmEudXRpbC5IYXNoU2V0ukSFlZa4tzQDAAB4cHcMAAAAAj9AAAAAAAABdAARYjY6YjI6MmY6M2Q6NmM6YTV4