package com.dcube.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Map;

@Slf4j
@Data
@EqualsAndHashCode
public class SourceConfigJsonVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连接串
     */
    @Schema(description = "连接串")
    private String url;

    /**
     * 用户
     */
    @Schema(description = "用户")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 驱动
     */
    @Schema(description = "驱动")
    private String driverClass;

    /**
     * 属性集合
     */
    @Schema(description = "属性集合")
    private Map<String, String> properties;

    public SourceConfigJsonVO() {
    }

    public SourceConfigJsonVO(String url, String username, String password, String driverClass, Map<String, String> properties) {
        this.url = url;
        this.username = username;
        this.password = password;
        this.driverClass = driverClass;
        this.properties = properties;
    }

//    public void setUrl(String url) {
//        this.url = SpringUtils.getBean(Sm4Util.class).encryptHex(url);
//    }
//
//    public void setUsername(String username) {
//        this.username = SpringUtils.getBean(Sm4Util.class).encryptHex(username);
//    }
//
//    public void setPassword(String password) {
//        this.password = SpringUtils.getBean(Sm4Util.class).encryptHex(password);
//    }

}
