package com.dcube.biz.vo;

import com.dcube.biz.dto.ColumnDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/15 14:41
 * @描述
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class WidgetDataVo implements Serializable {

   private static final long serialVersionUID = 1L;

   private List<ColumnDto> dimColumns;

   private List<ColumnDto> legendColumns;

   private List<ColumnDto> dataColumns;

   private List<ColumnDto> filterColumns;

   private List<List<Object>> rows;

   private String sqlScript;

   private int totalPage;

   private long totalRows;
}
