package com.dcube.biz.vo;

import com.dcube.biz.base.BaseVo;
import com.dcube.biz.dto.WidgetDto;
import com.dcube.biz.json.DashboardConfigJson;
import com.dcube.biz.json.TableMetaJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DashboardVo extends BaseVo {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "父id")
    private String parentId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "配置")
    private String config;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "组件")
    private List<WidgetDto> widgets;

    @Schema(description = "配置")
    private DashboardConfigJson dashboardConfig;
}
