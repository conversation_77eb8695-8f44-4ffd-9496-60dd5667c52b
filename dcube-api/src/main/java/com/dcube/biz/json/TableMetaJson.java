package com.dcube.biz.json;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.dto.DicItemDto;
import com.dcube.biz.dto.GroupInstanceDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 表元数据
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Data
public class TableMetaJson extends BaseDto {

    /**
     * 列序号
     */
    @Schema(description = "列序号")
    private Integer no;

    /**
     * 列编码
     */
    @Schema(description = "列编码")
    private String code;

    /**
     * 列名称
     */
    @Schema(description = "列名称")
    private String name;

    /**
     * 原列数据类型
     */
    @Schema(description = "原列数据类型")
    private String oldColumnType;

    @Schema(description = "原始字段名称")
    private String originalColumnCode;

    /**
     * 新列数据类型
     */
    @Schema(description = "新列数据类型")
    private String newColumnType;

    @Schema(description = "唯一编号")
    private String sid;

    @Schema(description = "筛选")
    private Boolean condition;

    @Schema(description = "筛选+是否可空")
    private String conditionIsEmpty;

    @Schema(description = "筛选+模糊查询")
    private String conditionLike;

    @Schema(description = "筛选+数据范围")
    private String conditionRange;

    @Schema(description = "系统参数")
    private String systemProperties;

    @Schema(description = "系统参数+编码")
    private String systemKey;

    @Schema(description = "系统参数+值")
    private String systemValue;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "列宽")
    private String columnWidth;

    @Schema(description = "绑定数据格式ID")
    private String dataFormatId;

    @Schema(description = "绑定数据格式对象")
    private GroupInstanceDto dataFormat;

    @Schema(description = "绑定字典项")
    private List<DicItemDto> dicItems;

    @Schema(description = "计算规则")
    private String computationRule;

    @Schema(description = "父表的入口列")
    private Boolean parentTableEntry = Boolean.FALSE;

    @Schema(description = "关联子表id")
    private Integer relSubTableId;

    @Schema(description = "对齐方式")
    private String contentAlign;

    /**
     * 列编码映射
     */
    @Schema(description = "列编码映射")
    private String codeMapping;

}
