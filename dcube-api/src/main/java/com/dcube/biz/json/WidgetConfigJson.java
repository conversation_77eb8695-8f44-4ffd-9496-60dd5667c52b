package com.dcube.biz.json;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.dto.ColumnDto;
import com.dcube.biz.dto.VariableDto;
import com.dcube.biz.dto.WidgetPageDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 可视化组件元数据
 */
@Data
public class WidgetConfigJson extends BaseDto {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "x坐标")
    private int x;

    @Schema(description = "y坐标")
    private int y;

    @Schema(description = "宽")
    private int w;

    @Schema(description = "高")
    private int h;

    @Schema(description = "视图Id（数据源Id或二维表Id）")
    private String viewId;

    @Schema(description = "视图名称")
    private String viewName;

    @Schema(description = "数据源类型")
    private String sourceType;

    @Schema(description = "SQL脚本")
    private String viewScript;

    @Schema(description = "图表类型")
    private String chartType;

    @Schema(description = "横轴纬度列")
    private List<ColumnDto> dimColumns;

    @Schema(description = "图例纬度列")
    private List<ColumnDto> legendColumns;

    @Schema(description = "指标列")
    private List<ColumnDto> dataColumns;

    @Schema(description = "筛选维")
    private List<ColumnDto> filterColumns;

    @Schema(description = "分页参数")
    private WidgetPageDto page;

    @Schema(description = "文本内容")
    private String text;

}
