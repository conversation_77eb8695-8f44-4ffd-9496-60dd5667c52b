package com.dcube.biz.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维度类型
 */
@Getter
@AllArgsConstructor
@Schema(description = "维度类型（DIM、IND）")
public enum DimTypeEnum {
    /**
     * 表
     */
    DIM(0, "维度"),
    /**
     * 列
     */
    IND(1, "指标"),

    ;

    @EnumValue
    private final int type;

    private final String desc;

}
