package com.dcube.biz.constant.enums;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/25 15:30
 * @描述
 */
public enum StringQueryTypeEnum {

    EQAUL("精确", "0"),

    LIKE("模糊", "-1");

    private String code;
    private String name;

    public static StringQueryTypeEnum getByCode(String code) {
        for (StringQueryTypeEnum stringQueryTypeEnum : StringQueryTypeEnum.values()) {
            if (stringQueryTypeEnum.name.equals(code)) {
                return stringQueryTypeEnum;
            }
        }
        return EQAUL;
    }

    StringQueryTypeEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
