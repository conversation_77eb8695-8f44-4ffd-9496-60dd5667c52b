package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class DimTableListQuery extends BaseQuery {

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    private Integer id;

    /**
     * 多维表名称
     */
    @Schema(description = "多维表名称")
    private String tableName;

    /**
     * 父Id
     */
    @Schema(description = "父Id")
    private Integer parentId;
}
