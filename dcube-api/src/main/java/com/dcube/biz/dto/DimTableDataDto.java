package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DimTableDataDto extends BaseDto {

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    private Integer tableId;

    /**
     * 多维表单元格集合
     */
    @Schema(description = "多维表单元格集合")
    private List<DimTableDataCellDto> dimTableDataCellList;
}
