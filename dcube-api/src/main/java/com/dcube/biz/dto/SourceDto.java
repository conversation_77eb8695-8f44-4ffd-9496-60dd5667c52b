package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.SourceConfigJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class SourceDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 数据源名称
     */
    @Schema(description = "数据源名称")
    private String sourceName;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型")
    private String sourceType;

    /**
     * 数据源配置
     */
    @Schema(description = "数据源配置")
    private SourceConfigJson sourceConfig;

    @Schema(description = "数据文件类型")
    private String dataFileType;

    @Schema(description = "数据文件目录")
    private String dataFileDir;

    @Schema(description = "目录日期格式")
    private String dirDateFormat;

    @Schema(description = "字段分割符")
    private String splitter;

    @Schema(description = "数据文件后缀")
    private String dataFileSuffix;

    @Schema(description = "ok文件后缀")
    private String okFileSuffix;

    @Schema(description = "Excel文件路径，数据文件格式为Excel时必传，其他格式传空")
    private String filePath;

    @Schema(description = "是否为数据底座(Y/N)，默认N")
    private String dataFoundationFlag;

    @Schema(description = "数据源明细，解析文件时，可不传")
    private List<SourceDetailDto> details;
}
