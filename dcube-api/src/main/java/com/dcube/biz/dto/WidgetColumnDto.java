package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/15 15:13
 * @描述
 */
@Data
public class WidgetColumnDto extends BaseDto{

    @Schema(description = "列名称", example = "列名称，必传")
    private String name;

    @Schema(description = "列描述", example = "列描述")
    private String desc;

    @Schema(description = "列类型", example = "列类型，必传")
    private String type;

}
