package com.dcube.biz.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 二维表
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Data
public class SubTableDto extends TableDto {

    /**
     * 父表ID
     */
    @Schema(description = "父表ID")
    private Integer parentTableId;

    /**
     * 父表列序号
     */
    @Schema(description = "父表列序号")
    private Integer parentTableMetaNo;

    /**
     * 父表列编码
     */
    @Schema(description = "父表列编码")
    private String parentTableMetaCode;

    /**
     * 父表列名称
     */
    @Schema(description = "父表列名称")
    private String parentTableMetaName;
}
