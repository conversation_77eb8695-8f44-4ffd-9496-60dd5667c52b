package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.WidgetConfigJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WidgetDto extends BaseDto {

    @Schema(description = "主键ID", example = "主键ID，更新时必传")
    private String id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "视图名称")
    private String viewName;

    @Schema(description = "视图id", example = "视图Id，必传")
    private String viewId;

    @Schema(description = "仪表板ID", example = "仪表板ID，必传")
    private String dashboardId;

    @Schema(description = "配置", example = "配置，必传")
    private WidgetConfigJson configJson;

    @Schema(description = "排序", example = "排序，必传")
    private Integer seq;

    private String config;
}
