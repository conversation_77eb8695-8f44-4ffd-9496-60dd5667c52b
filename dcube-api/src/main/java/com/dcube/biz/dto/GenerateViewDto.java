package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.TableMetaJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 数据底表生成视图
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Data
public class GenerateViewDto extends BaseDto {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "视图名称")
    private String viewName;

    @Schema(description = "数据源Id")
    private String sourceId;

    @Schema(description = "SQL脚本")
    private String sqlScript;

    @Schema(description = "表格元数据")
    private List<TableMetaJson> tableMetaJson;
}
