package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class LayoutDto extends BaseDto {

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    private Integer tableId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 筛选维度列表
     */
    @Schema(description = "筛选维度对象(包含维度ID和选中维值)集合")
    private List<LayoutFilterDimDTO> filterDimList;

    /**
     * 行维度列表
     */
    @Schema(description = "行维度列表")
    private List<LayoutDimDTO> rowDimList;

    /**
     * 列维度列表
     */
    @Schema(description = "列维度列表")
    private List<LayoutDimDTO> columnDimList;


}
