package com.dcube.biz.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.TableMetaJson;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class SourceDetailDto extends BaseDto {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String sourceId;

    private String tableName;

    private Integer fieldCount;

    private String fileName;

    private String tableComments;

    private List<TableMetaJson> tableMeta;

    private Map<String, Object> previewData;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
