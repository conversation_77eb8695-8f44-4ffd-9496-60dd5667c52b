package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class DimTableDataCellDto extends BaseDto {

    /**
     * 关联维度ID集合(确保顺序)
     */
    @Schema(description = "关联维度ID集合(确保顺序)")
    private List<Integer> relDimIdList;

    /**
     * 关联维度实例ID集合(确保顺序)
     */
    @Schema(description = "关联维度实例ID集合(确保顺序)")
    private List<String> dimInstanceIdList;

    /**
     * 关联指标ID
     */
    @Schema(description = "关联指标ID")
    private String indId;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型")
    private String opType;

    /**
     * 单元格中原始的值
     */
    @Schema(description = "单元格中原始的值")
    private Double originalValue;

    /**
     * parentDimId
     */
    @Schema(description = "parentDimId")
    private String parentDimId;

    /**
     * parentDimInstanceId
     */
    @Schema(description = "parentDimInstanceId")
    private String parentDimInstanceId;
}
