import request from '@/utils/request'

// 新增分类or流程
export function addWorkflow(data) {
  return request({
    url: `/dcube/workflow/define`,
    method: 'post',
    data
  })
}

// 修改分类or流程
export function updateWorkflow(data) {
  return request({
    url: `/dcube/workflow/define`,
    method: 'put',
    data
  })
}

// 删除分类or流程
export function deleteWorkflow(id) {
  return request({
    url: `/dcube/workflow/define/${id}`,
    method: 'DELETE'
  })
}

// 获取流程列表
export function getList(params) {
  return request({
    url: `/dcube/workflow/define/list`,
    method: 'get',
    params
  })
}

// 获取二维表列表
export function get2dTableList() {
  return request({
    url: `/dcube/workflow/define/get2dTable?filterChildTable=true`,
    method: 'get'
  })
}

// 获取二维表格列
export function get2dcol(tableId) {
  return request({
    url: `/dcube/workflow/define/get2dTableMeta`,
    method: 'get',
    params:{tableId}
  })
}

// 获取流程详情
export function getFlowChartById(id) {
  return request({
    url: `/dcube/workflow/define/${id}`,
    method: 'get',
  })
}

// 获取提交流程的参数信息
export function getCommitParamsById(data) {
  return request({
    url: `/dcube/workflow/runtime/task/nextEdgeInfo`,
    method: 'post',
    data
  })
}

// 获取流程列表-已办
export function getDoneFlowList(params){
  return request({
    url: `/dcube/workflow/runtime/task/done`,
    method: 'get',
    params
  })
}

// 获取流程列表-待办
export function getToDoFlowList(params){
  return request({
    url: `/dcube/workflow/runtime/task/todo`,
    method: 'get',
    params
  })
}

// 查看审批记录-已办
export function getRecordMsgById(instanceId){
  return request({
    url: `/dcube/workflow/runtime/history/getByInstanceId?instanceId=${instanceId}`,
    method: 'get',
  })
}

// 查询待办和已办数量-首页
export function getFlowTaskCount(){
  return request({
    url: `/dcube/workflow/runtime/task/count`,
    method: 'post',
  })
}

// 撤回审批
export function getFlowRecordBack(data){
  return request({
    url: `/dcube/workflow/runtime/task/execute`,
    method: 'post',
    data
  })
}

// 同意审批
export function getRecordAgree(data){
  return request({
    url: `/dcube/workflow/runtime/task/execute`,
    method: 'post',
    data
  })
}

// 发起审批
export function getRecordStart(data){
  return request({
    url: `/dcube/workflow/runtime/task/execute`,
    method: 'post',
    data
  })
}

// 获取组织机构数据源
export function getOrgInstitutionData(id){
  return request({
    url: `/dcube/workflow/define/getUnitTableMeta?id=${id}`,
    method: 'get',
  })
}

export function getCubeTableList(){
  return request({
    url: `/dcube/workflow/define/getCubeTableList`,
    method: 'get',
  })
}

export function getDimsByCubeId(id){
  return request({
    url: `/dcube/workflow/define/getDimListByDimTableId?dimTableId=${id}`,
    method: 'get',
  })
}

// 多维详情页-左侧维度树
export function getDimTreeById(params){
  return request({
    url: `/dcube/workflow/define/getByDefineId`,
    method: 'get',
    params
  })
}

// 保存左侧维度的审批人和归口
export function saveApproversCentralized(data){
  return request({
    url: `/dcube/workflow/dimDefineDetail/save`,
    method: 'post',
    data
  })
}

// 归口节点维护 列表
export function getCentralizedNodes(data){
  return request({
    url: `/dcube/workflow/dimDefineDetail/getCentralize`,
    method: 'post',
    data
  })
}

// 
export function saveCentralizedNode(data){
  return request({
    url: `/dcube/workflow/dimDefineDetail/saveCentralize`,
    method: 'post',
    data
  })
}

// 发起流程
export function startProcess(data){
  return request({
    url: `/dcube/workflow/runtime/instance/startProcess`,
    method: 'post',
    data
  })
}

// 终止流程
export function stopProcess(data){
  return request({
    url: `/dcube/workflow/runtime/instance/stopProcess`,
    method: 'post',
    data
  })
}

// 获取多维流程子任务列表
export function getFlowCubeChild(taskId) {
    return request({
        url: `/dcube/workflow/runtime/task/childTaskList?id=${taskId}`,
        method: 'get',
    })
}

// 查看审批记录列表
export function getRecordMsgList(instanceId, nodeId, isAsc, centralizeDimDefineId) {
  return request({
    url: `/dcube/workflow/runtime/history/list?instanceId=${instanceId}&nodeId=${nodeId}&isAsc=${isAsc}&centralizeDimDefineId=${centralizeDimDefineId}`,
      method: 'get',
  })
}

// 查看节点状态
export function viewNodeState(id, nodeId) {
    return request({
        url: `/dcube/workflow/runtime/task/viewNodeState?id=${id}&nodeId=${nodeId || ''}`,
    method: 'get',
  })
}
