import request from '@/utils/request'

export function uploadSliceFile(data) {
  return request({
    url: `/dcube/common/uploadSliceFile`,
    method: 'post',
    data
  })
}

export function mergeFile(data) {
  return request({
    url: `/dcube/common/mergeFile`,
    method: 'post',
    data
  })
}

export function spiltDownload(params, headers) {
  return request({
    url: `/dcube/common/spiltDownload`,
    method: 'get',
    params,
    headers: headers,
    responseType: 'blob',
    async: true
  })
}
