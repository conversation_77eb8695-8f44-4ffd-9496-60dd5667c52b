import request from '@/utils/request'

// 查询规则
export function getRuleList(data) {
  return request({
    url: `/dcube/cube/dimRule/get`,
    method: 'get',
    params: data
  })
}

// 新增规则
export function addRule(data) {
  return request({
    url: `/dcube/cube/dimRule/add`,
    method: 'post',
    data
  })
}

// 删除规则
export function deleteRule(id) {
  return request({
    url: `/dcube/cube/dimRule/delete/${id}`,
    method: 'delete'
  })
}

// 移动规则
export function moveRule(data) {
  return request({
    url: `/dcube/cube/dimRule/move`,
    method: 'put',
    data
  })
}

// 查询维度
export function getDimAndIndicator(params) {
  return request({
    url: `/dcube/cube/dimRule/getIndicatorOperation`,
    method: 'post',
    params
  })
}

// 查询维度成员
export function getDimMemberById(data) {
  return request({
    url: `/dcube/cube/dimRule/queryIndicatorOperationDimInstance`,
    method: 'post',
    data
  })
}

// 保存维度成员和指标
export function saveDimMemberAndIndicator(data) {
  return request({
    url: `/dcube/cube/dimRule/saveIndicatorOperation`,
    method: 'post',
    data
  })
}

// 批量保存
export function batchSaveDimMembers(data) {
  return request({
    url: `/dcube/cube/dimRule/saveIndicatorOperationEffectScopes`,
    method: 'post',
    data
  })
}
