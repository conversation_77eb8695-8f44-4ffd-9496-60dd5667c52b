// 数据源列表
import request from '@/utils/request'

export function getList(params) {
  return request({
    url: `/dcube/dashboard`,
    method: 'get',
    params
  })
}

export function addDashboard(data) {
  return request({
    url: '/dcube/dashboard',
    method: 'post',
    data
  })
}
// 修改图表
export function editDashboard(data) {
  return request({
    url: '/dcube/dashboard',
    method: 'put',
    data
  })
}
// 删除图表
export function delDashboard(id) {
  return request({
    url: '/dcube/dashboard/' + id,
    method: 'delete'
  })
}
// 修改状态
export function changeStatus(id, status) {
  return request({
    url: '/dcube/dashboard/' + id + '/' + status,
    method: 'put',
    data: null
  })
}

export function getWidget(params) {
  return request({
    url: `/dcube/widget`,
    method: 'get',
    params
  })
}

export function saveOrUpdateWidget(data) {
  return request({
    url: `/dcube/widget`,
    method: 'post',
    data
  })
}

// 获取有哪些主题色
export function getTheme() {
  return request({
    url: `/dcube/dashboard/themes`,
    method: 'get'
  })
}

// 获取系统变量
export function getSystemVariable() {
  return request({
    url: `/dcube/variable/system/list`,
    method: 'get'
  })
}

// 获取系统变量值
export function getSystemVariableValue(variableName) {
  return request({
    url: `/dcube/variable/system/${variableName}`,
    method: 'get'
  })
}

// 批量新增或者更新视图卡片组件接口
export function batchSaveOrUpdate(data) {
  return request({
    url: `/dcube/widget/batchSaveOrUpdate`,
    method: 'post',
    data
  })
}

// 批量新增或者更新视图报告组件接口
export function batchSaveOrUpdateReport(data) {
  return request({
    url: `/dcube/widget/batchSaveOrUpdateReport`,
    method: 'post',
    data
  })
}

// 删除组件
export function getAggregationType(id) {
  return request({
    url: '/dcube/widget/aggregationType',
    method: 'get'
  })
}

// 删除组件
export function delWidget(id) {
  return request({
    url: '/dcube/widget/' + id,
    method: 'delete'
  })
}

// 二维表
export function get2dTableList() {
  return request({
    url: `/dcube/widget/queryViewList`,
    method: 'get'
  })
}

// 预览图表
export function getPreviewData(data) {
  return request({
    url: '/dcube/widget/preview',
    method: 'post',
    data
  })
}
