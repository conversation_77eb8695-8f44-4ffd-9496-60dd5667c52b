import request from '@/utils/request'

// 获取维度树
export function getDimensionTree(params) {
  return request({
    url: `/dcube/dim_directory/list`,
    method: 'get',
    params
  })
}
// 维度成员
export function getMember(params) {
  return request({
    url: `/dcube/dim_instance/list`,
    method: 'get',
    params
  })
}
// 新增维度
export function addDim(data) {
  return request({
    url: `/dcube/dim_directory/add`,
    method: 'post',
    data
  })
}
// 删除维度
export function delDim(id) {
  return request({
    url: `/dcube/dim_directory/remove/${id}`,
    method: 'post'
  })
}
// 修改维度
export function updateDim(data) {
  return request({
    url: `/dcube/dim_directory/put`,
    method: 'post',
    data
  })
}
// 添加维度成员
export function addMember(data) {
  return request({
    url: `/dcube/dim_instance/add`,
    method: 'post',
    data
  })
}
// 删除维度成员
export function delMember(id) {
  return request({
    url: `/dcube/dim_instance/remove/${id}`,
    method: 'post'
  })
}
// 修改维度成员
export function updateMember(data) {
  return request({
    url: `/dcube/dim_instance/put`,
    method: 'post',
    data
  })
}
// 移动维度成员
export function moveDim(data) {
  return request({
    url: `/dcube/dim_instance/move`,
    method: 'put',
    data
  })
}

