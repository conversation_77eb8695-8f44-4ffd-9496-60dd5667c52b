import request from '@/utils/request'

// 获取部门列表
export function getDeptList(params) {
  return request({
    url: `/dcube/system/dept/list`,
    method: 'get',
    params
  })
}
// 添加部门
export function addDepartment(data) {
  return request({
    url: `/dcube/system/dept`,
    method: 'post',
    data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: `/dcube/system/dept/${deptId}`,
    method: 'delete'
  })
}
// 编辑部门
export function editDept(data) {
  return request({
    url: `/dcube/system/dept`,
    method: 'put',
    data
  })
}
// 机构树
export function getDeptTree() {
  return request({
    url: `/dcube/system/dept/tree`,
    method: 'get'
  })
}
// 下载用户模板信息
export function downloadTemplate() {
  return request({
    url: '/dcube/system/dept/downloadTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}
// 导入
export function importFromExcel(data) {
  return request({
    url: '/dcube/system/dept/importFromExcel',
    method: 'post',
    data
  })
}

// 主数据维度获取部门列表
export function masterDimGetDeptList(params) {
  return request({
    url: `/dcube/system/dept/masterDimGetDeptList`,
    method: 'get',
    params
  })
}