import request from '@/utils/request'

// 事件列表
export function getEventList(params) {
  return request({
    url: `/dcube/monitor/job/list`,
    method: 'get',
    params
  })
}
// 删除事件
export function delEvent(id) {
  return request({
    url: `/dcube/monitor/job/${id}`,
    method: 'delete'
  })
}
// 新增分组
export function addGroup(data) {
  return request({
    url: `/dcube/group/add`,
    method: 'post',
    data
  })
}
// 新增事件
export function addEvent(data) {
  return request({
    url: `/dcube/monitor/job/saveEvent`,
    method: 'post',
    data
  })
}
// 修改事件
export function updateEvent(data) {
  return request({
    url: `/dcube/monitor/job/updateEvent`,
    method: 'put',
    data
  })
}
// 新增进程
export function addProcess(data) {
  return request({
    url: `/dcube/jobDetail`,
    method: 'post',
    data
  })
}
// 修改进程
export function updateProcess(data) {
  return request({
    url: `/dcube/jobDetail`,
    method: 'put',
    data
  })
}
// 删除进程
export function delProcess(id) {
  return request({
    url: `/dcube/jobDetail/${id}`,
    method: 'DELETE'
  })
}
// 获取进程详情
export function getProcessDetail(params) {
  return request({
    url: `/dcube/jobDetail/getByJobId`,
    method: 'get',
    params
  })
}
// 获取规则进程列表
export function getRuleList(params) {
  return request({
    url: `/dcube/rule/getRulesByJobId`,
    method: 'get',
    params
  })
}
// 手动执行
export function execFun(data) {
  return request({
    url: `/dcube/monitor/job/run`,
    method: 'put',
    data
  })
}
// 查看日志
export function checkLog(params) {
  return request({
    url: `/dcube/monitor/jobLog/pageJobLog`,
    method: 'get',
    params
  })
}
// 手动恢复和暂停
export function changeStatus(data) {
  return request({
    url: `/dcube/monitor/job/changeStatus`,
    method: 'put',
    data
  })
}
