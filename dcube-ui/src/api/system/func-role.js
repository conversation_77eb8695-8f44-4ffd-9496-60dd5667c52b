import request from '@/utils/request'

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/dcube/system/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/dcube/system/role/' + roleId,
    method: 'get'
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/dcube/system/role',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/dcube/system/role',
    method: 'put',
    data: data
  })
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: '/dcube/system/role/dataScope',
    method: 'put',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/dcube/system/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/dcube/system/role/' + roleId,
    method: 'delete'
  })
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: '/dcube/system/role/authUser/allocatedList',
    method: 'get',
    params: query
  })
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
  return request({
    url: '/dcube/system/role/authUser/unallocatedList',
    method: 'get',
    params: query
  })
}

// 取消用户授权角色
export function authUserCancel(data) {
  return request({
    url: '/dcube/system/role/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
  return request({
    url: '/dcube/system/role/authUser/cancelAll',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll(data) {
  return request({
    url: '/dcube/system/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}

// 根据角色ID查询部门树结构
export function deptTreeSelect(roleId) {
  return request({
    url: '/dcube/system/role/deptTree/' + roleId,
    method: 'get'
  })
}
// 获取数据编辑权限下拉选项
export function getDataEditOpts() {
  return request({
    url: '/dcube/permission/data_edition',
    method: 'get'
  })
}
// 获取数据范围权限下拉选项
export function getScopeOpts() {
  return request({
    url: '/dcube/permission/data_scope',
    method: 'get'
  })
}
// 定义表格权限下拉选项
export function getDefineOpts() {
  return request({
    url: '/dcube/permission/table_definition',
    method: 'get'
  })
}
// 获取列表
export function getPermissionList(params) {
  return request({
    url: `/dcube/permission/role_table_list`,
    method: 'get',
    params
  })
}
// 保存表格权限
export function saveTablePermission(data) {
  return request({
    url: '/dcube/permission/save',
    method: 'post',
    data
  })
}
// 主数据权限下拉选项
export function getMasterOption() {
  return request({
    url: '/dcube/permission_dim/master_option',
    method: 'get'
  })
}
// 有无选项
export function predicateOption() {
  return request({
    url: '/dcube/permission_dim/predicate_option',
    method: 'get'
  })
}
// 根据角色获取数方
export function getDcubeListByRole(params) {
  return request({
    url: '/dcube/permission_dim/role_dim_list',
    method: 'get',
    params
  })
}
// 数方权限关联的维度列表
export function getDimListByRole(params) {
  return request({
    url: '/dcube/permission_dim/role_dim_instance_list',
    method: 'get',
    params
  })
}
// 获取已选的维度成员列表
export function getSelectDimMember(params) {
  return request({
    url: '/dcube/permission_dim/role_dim_has_instance_list',
    method: 'get',
    params
  })
}
// 保存已选的维度成员列表
export function saveSelectDimMember(data) {
  return request({
    url: '/dcube/permission_dim/save_dim_instance',
    method: 'post',
    data: data
  })
}
// 保存数方权限

export function saveDcubePermission(data) {
  return request({
    url: '/dcube/permission_dim/save',
    method: 'post',
    data: data
  })
}
// 保存主数据维度数据范围
export function saveMasterDataScope(data) {
  return request({
    url: '/dcube/permission_dim/save_master_data_scope',
    method: 'post',
    data: data
  })
}

