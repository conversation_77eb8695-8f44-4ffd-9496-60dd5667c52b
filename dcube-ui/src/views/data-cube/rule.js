import { checkRule } from '@/api/base-table'

const validateRule = (form, tableId, columnCode) => async(rule, value, callback) => {
  if (!form.rule) {
    callback()
    return
  } else {
    const params = new FormData()
    params.append('express', JSON.stringify(form.rule))
    params.append('tableId', tableId)
    params.append('columnCode', columnCode)
    const result = await checkRule(params)
    if (result.code !== 200) {
      callback(new Error(result.msg))
      return
    }
  }
}

const rules = {
  createCalcRule: (form, tableId, columnCode) => ({
    rule: [{
      validator: validateRule(form, tableId, columnCode),
      trigger: 'blur'
    }]
  })
}

export default rules
