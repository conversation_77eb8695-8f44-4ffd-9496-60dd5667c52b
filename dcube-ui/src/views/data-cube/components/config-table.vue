<template>
  <div class="config-table">
    <vxe-grid ref="vxeGrid" v-bind="gridOptions" />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
const staticEmptyArr = ['empty0', 'empty1', 'empty2', 'empty3']

// 添加路径信息
function addPath(row, parentId, id, arr) {
  const path = [parentId, id]
  function findParent(parentId) {
    const parent = arr.find(item => item.id === parentId)
    if (parent) {
      path.unshift(parent.parentId)
      findParent(parent.parentId)
    }
  }
  findParent(parentId)
  row.path = path
  row.isExpand = typeof row.isExpand === 'boolean' ? row.isExpand : true
  row.isChild = row.isLeaf === 'Y'
  return { path, isChild: row.isChild, isExpand: row.isExpand }
}

// 计算树的最大层级
function calculationLevel(arr) {
  const cloneArr = cloneDeep(arr)
  let maxLevel = 0
  if (cloneArr && cloneArr.length) {
    !(function multiArr(arr, level) {
      ++level
      maxLevel = Math.max(level, maxLevel)
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        item.level = level
        if (item.headers && item.headers.length > 0) {
          multiArr(item.headers, level)
        } else {
          delete item.headers
        }
      }
    })(cloneArr, 0)
  }
  return maxLevel
}

// 计算行头范围
function findRange(arr) {
  let left = 0
  if (arr && arr.length) {
    arr.forEach(item => {
      const { rowSpan } = item
      item.range = [left, left + rowSpan]
      left = left + rowSpan
    })
  }
}

// 查找当前行
function findCurRow(arr, index) {
  let curRow = {}
  for (let idx = 0; idx < arr.length; idx++) {
    const item = arr[idx]
    const [left, right] = item.range
    if (index >= left && index < right) {
      curRow = cloneDeep(item)
      break
    }
  }
  return curRow
}

export default {
  props: {
    configData: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      gridOptions: {
        loading: true,
        border: true,
        stripe: false,
        resizable: true,
        showOverflow: true,
        autoResize: true,
        height: 'auto',
        size: 'mini',
        editConfig: { trigger: 'click', mode: 'cell' },
        columns: [],
        data: []
      },
      tableMetaJson: [],
      tableTimer: null,
      tableData: [],
      originColumnHeaders: [],
      originRowHeaders: [],
      originRows: [],
      curColumnHeaders: [],
      curRowHeaders: [],
      curRows: [],
      delRowHeaderObj: {},
      delRowObj: {},
      // 存储每个节点的后代节点ID映射
      descendantMap: {},
      // 列折叠相关状态
      colDesMap: {},
      delColumnHeaderObj: {},
      delColumnObj: {}
    }
  },
  watch: {
    loading(newVal) {
      this.gridOptions.loading = newVal
    },
    configData: {
      handler(newVal) {
        if (newVal) {
          const { columnHeaders, rowHeaders, rows } = newVal || {}
          this.originColumnHeaders = cloneDeep(columnHeaders)
          this.originRowHeaders = cloneDeep(rowHeaders)
          this.originRows = cloneDeep(rows)
          this.curColumnHeaders = cloneDeep(columnHeaders)
          this.curRowHeaders = cloneDeep(rowHeaders)
          this.curRows = cloneDeep(rows)

          // 预计算所有节点的后代ID
          this.precomputeDescendants(rowHeaders)
          this.precomputeCol(columnHeaders)
          this.renderTable(columnHeaders, rowHeaders, rows)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 预计算所有行节点的后代ID
    precomputeDescendants(rowHeaders) {
      this.descendantMap = {}
      const allNodes = this.flattenRowHeaders(rowHeaders)

      allNodes.forEach(node => {
        this.descendantMap[node.id] = this.collectDescendantIds(node.id, allNodes)
      })
    },
    // 预计算所有列节点的后代ID
    precomputeCol(columnHeaders) {
      this.colDesMap = {}
      const allNodes = this.flattenRowHeaders(columnHeaders)

      allNodes.forEach(node => {
        this.colDesMap[node.id] = this.collectDescendantIds(node.id, allNodes)
      })
    },
    // 扁平化行头数据
    flattenRowHeaders(rowHeaders) {
      const result = []

      function flatten(items) {
        items && items.forEach(item => {
          result.push(item)
          if (item.headers && item.headers.length) {
            flatten(item.headers)
          }
        })
      }

      flatten(rowHeaders)
      return result
    },

    // 收集后代节点ID（包括所有层级）
    collectDescendantIds(parentId, allNodes) {
      const descendants = []
      const stack = [parentId]

      while (stack.length) {
        const currentId = stack.pop()
        const children = allNodes.filter(node => node.parentId === currentId)

        children.forEach(child => {
          descendants.push(child.id)
          stack.push(child.id)
        })
      }

      return descendants
    },

    // 查找最长的标签
    findLongesLabel(arr) {
      if (!arr.length) return 0
      return Math.max(...arr.map(item => (item.empty0 || '').length))
    },
    findInHeaders(headers, targetId) {
      if (!headers) return null
      for (let i = 0; i < headers.length; i++) {
        if (headers[i].id === targetId) return headers[i]
        const found = this.findInHeaders(headers[i].headers, targetId)
        if (found) return found
      }
      return null
    },
    // 修改后的点击展开/折叠图标方法
    async toggleRow(row, rowIndex, field) {
      return
      const idField = `${field}-id`
      const expandField = `${field}-isExpand`
      const clickId = row[idField]
      const isExpand = row[expandField]
      // 1. 确定当前点击节点的层级
      const level = parseInt(field.replace('empty', ''))
      console.log('点击节点', rowIndex, field, row)
      // 2. 查找当前节点在行头列表中的位置
      let nodeIndex = -1
      let currentNode = null
      let topLevelNode = null
      let secondLevelNode = null
      let validHeaders = null
      // 如果是第一层节点，直接查找
      if (level === 0) {
        nodeIndex = this.curRowHeaders.findIndex(item => item.id === clickId)
        if (nodeIndex !== -1) {
          currentNode = this.curRowHeaders[nodeIndex]
        }
        validHeaders = this.curRowHeaders
      } else if (level === 1) {
        // 查找所属的顶层节点

        let accumulatedSpan = 0

        for (let i = 0; i < this.curRowHeaders.length; i++) {
          const node = this.curRowHeaders[i]
          accumulatedSpan += node.rowSpan

          if (rowIndex < accumulatedSpan) {
            topLevelNode = node
            break
          }
        }
        if (topLevelNode) {
          // 在顶层节点的headers中查找当前节点
          currentNode = this.findInHeaders(topLevelNode.headers, clickId)
          if (currentNode) {
            // 在curRowHeaders中找到对应的节点
            nodeIndex = topLevelNode.headers.findIndex(item => item.id === currentNode.id)
          }
        }
        validHeaders = topLevelNode.headers
      } else if (level === 2) {
        const topNodeId = row['empty0-id']
        const secondNodeId = row['empty1-id']
        topLevelNode = this.findInHeaders(this.curRowHeaders, topNodeId)
        secondLevelNode = this.findInHeaders(topLevelNode.headers, secondNodeId)
        currentNode = this.findInHeaders(secondLevelNode.headers, clickId)
        validHeaders = secondLevelNode.headers
        if (currentNode) {
          nodeIndex = secondLevelNode.headers.findIndex(item => item.id === currentNode.id)
        }
      }

      if (nodeIndex === -1 || !currentNode) return
      if (isExpand) {
        // 折叠操作
        const descendantIds = this.descendantMap[clickId] || []

        // 查找需要折叠的连续节点
        let removeCount = 0
        let removeRowCount = 0
        for (let i = nodeIndex + 1; i < validHeaders.length; i++) {
          if (descendantIds.includes(validHeaders[i].id)) {
            removeCount++
            removeRowCount += validHeaders[i].rowSpan
          } else {
            break
          }
        }
        console.log('removeRowCount', removeCount, removeRowCount, descendantIds)
        if (removeCount > 0) {
          // 保存被移除的节点和行数据
          const removedNodes = validHeaders.splice(nodeIndex + 1, removeCount)
          const rowRemoveStart = rowIndex + currentNode.rowSpan
          const removedRows = this.curRows.splice(rowRemoveStart, removeRowCount)
          if (level === 1) {
            this.curRowHeaders.forEach(item => {
              if (item.id === topLevelNode.id) {
                item.rowSpan = item.rowSpan - removeRowCount
              }
            })
          }
          if (level === 2) {
            this.curRowHeaders.forEach(item => {
              if (item.id === topLevelNode.id) {
                item.rowSpan = item.rowSpan - removeRowCount
                item && item.headers.forEach(child => {
                  if (child.id === secondLevelNode.id) {
                    child.rowSpan = child.rowSpan - removeRowCount
                  }
                })
              }
            })
          }
          if (level === 0) {
            this.delRowHeaderObj[clickId] = removedNodes
            this.delRowObj[clickId] = removedRows
          } else if (level === 1) {
            this.delRowHeaderObj[`${topLevelNode.id}-${clickId}`] = removedNodes
            this.delRowObj[`${topLevelNode.id}-${clickId}`] = removedRows
          } else if (level === 2) {
            this.delRowHeaderObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`] = removedNodes
            this.delRowObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`] = removedRows
          }
        }

        // 更新当前节点状态
        currentNode.isExpand = false
        row[expandField] = false
      } else {
        // 展开操作
        if (level === 0) {
          if (this.delRowHeaderObj[clickId]) {
            const insertIndex = nodeIndex + 1
            const rowInsertIndex = rowIndex + currentNode.rowSpan
            // 插入节点和行数据
            this.curRowHeaders.splice(insertIndex, 0, ...this.delRowHeaderObj[clickId])
            this.curRows.splice(rowInsertIndex, 0, ...this.delRowObj[clickId])
            // 清除缓存
            delete this.delRowHeaderObj[clickId]
            delete this.delRowObj[clickId]
          }
        } else if (level === 1) {
          const delObj = this.delRowHeaderObj[`${topLevelNode.id}-${clickId}`]
          if (delObj) {
            const insertIndex = nodeIndex + 1
            const rowInsertIndex = rowIndex + currentNode.rowSpan
            // 插入节点和行数据
            validHeaders.splice(insertIndex, 0, ...delObj)
            this.curRows.splice(rowInsertIndex, 0, ...this.delRowObj[`${topLevelNode.id}-${clickId}`])
            this.curRowHeaders.forEach(item => {
              if (item.id === topLevelNode.id) {
                delObj.forEach((del) => {
                  item.rowSpan += del.rowSpan
                })
              }
            })
            // 清除缓存
            delete this.delRowHeaderObj[`${topLevelNode.id}-${clickId}`]
            delete this.delRowObj[`${topLevelNode.id}-${clickId}`]
          }
        } else if (level === 2) {
          const delObj = this.delRowHeaderObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`]
          if (delObj) {
            const insertIndex = nodeIndex + 1
            const rowInsertIndex = rowIndex + currentNode.rowSpan
            // 插入节点和行数据
            validHeaders.splice(insertIndex, 0, ...delObj)
            this.curRows.splice(rowInsertIndex, 0, ...this.delRowObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`])
            this.curRowHeaders.forEach(item => {
              if (item.id === topLevelNode.id) {
                delObj.forEach((del) => {
                  item.rowSpan += del.rowSpan
                })
                item.headers && item.headers.forEach(child => {
                  if (child.id === secondLevelNode.id) {
                    delObj.forEach((del) => {
                      child.rowSpan += del.rowSpan
                    })
                  }
                })
              }
            })
            // 清除缓存
            delete this.delRowHeaderObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`]
            delete this.delRowObj[`${topLevelNode.id}-${secondLevelNode.id}-${clickId}`]
          }
        }

        // 更新当前节点状态
        currentNode.isExpand = true
        row[expandField] = true
      }

      // 使用nextTick确保在DOM更新后重新渲染表格
      const scrollInfo = this.$refs.vxeGrid.getScroll()
      await this.$nextTick()
      console.log('更新后的数据', this.curRowHeaders, this.curRows)
      await this.renderTable(this.curColumnHeaders, this.curRowHeaders, this.curRows)
      // 表格折叠隐藏后高度变化需要重新加载数据
      this.clearTimer(this.tableTimer)
      this.tableTimer = setTimeout(() => {
        this.$refs.vxeGrid.scrollTo(scrollInfo.scrollLeft, isExpand ? scrollInfo.scrollTop - 1 : scrollInfo.scrollTop + 1)
      }, 10)
    },
    // 列展开折叠方法
    async toggleColumn(column) {
      return
      const { id, groupId, isExpand } = column.params
      let currentNode = null
      let topLevelNode = null
      let validHeaders = null
      let nodeIndex = -1
      let rowRemoveStart = -1
      if (groupId) {
        topLevelNode = this.findInHeaders(this.curColumnHeaders, groupId)
        currentNode = this.findInHeaders(topLevelNode.headers, id)
      } else {
        currentNode = this.findInHeaders(this.curColumnHeaders, id)
      }
      validHeaders = groupId ? topLevelNode.headers : this.curColumnHeaders
      nodeIndex = validHeaders.findIndex(item => item.id === id)
      if (!groupId) {
        rowRemoveStart = nodeIndex
      } else {
        const groupIndex = this.curColumnHeaders.findIndex(item => item.id === groupId)
        for (let i = 0; i < groupIndex; i++) {
          rowRemoveStart = rowRemoveStart + this.curColumnHeaders[i].colSpan
        }
        rowRemoveStart = rowRemoveStart + nodeIndex
      }
      if (nodeIndex < 0) return
      if (isExpand) {
        const descendantIds = this.colDesMap[id]
        // 查找需要折叠的连续节点
        let removeCount = 0
        let removeColCount = 0
        for (let i = nodeIndex + 1; i < validHeaders.length; i++) {
          if (descendantIds.includes(validHeaders[i].id)) {
            removeCount++
            removeColCount += validHeaders[i].colSpan
          } else {
            break
          }
        }
        console.log('removeColCount', nodeIndex, descendantIds, removeCount, removeColCount)
        if (removeCount > 0) {
          console.log('rowRemoveStart', rowRemoveStart)
          const removedNodes = validHeaders.splice(nodeIndex + 1, removeCount)
          this.curColumnHeaders.forEach(col => {
            col.colSpan = col.headers ? col.headers.length : 1
          })
          const removeCells = []
          this.curRows.forEach(item => {
            removeCells.push(item.cells.splice(rowRemoveStart + 1, removeCount))
          })
          if (groupId) {
            this.delColumnHeaderObj[`${groupId}-${id}`] = removedNodes
            this.delColumnObj[`${groupId}-${id}`] = removeCells
          } else {
            this.delColumnHeaderObj[id] = removedNodes
            this.delColumnObj[id] = removeCells
          }
        }
        currentNode.isExpand = false
      } else {
        const delColumns = groupId ? this.delColumnHeaderObj[`${groupId}-${id}`] : this.delColumnHeaderObj[id]
        const delCells = groupId ? this.delColumnObj[`${groupId}-${id}`] : this.delColumnObj[id]
        validHeaders.splice(nodeIndex + 1, 0, ...delColumns)
        this.curColumnHeaders.forEach(col => {
          col.colSpan = col.headers ? col.headers.length : 1
        })
        this.curRows.forEach((row, rowIdx) => {
          const rowCells = delCells[rowIdx]
          row.cells.splice(rowRemoveStart + 1, 0, ...rowCells)
        })
        if (groupId) {
          delete this.delColumnHeaderObj[`${groupId}-${id}`]
          delete this.delColumnObj[`${groupId}-${id}`]
        } else {
          delete this.delColumnHeaderObj[id]
          delete this.delColumnObj[id]
        }
        currentNode.isExpand = true
      }
      const scrollInfo = this.$refs.vxeGrid.getScroll()
      console.log('更新后的数据', this.curColumnHeaders, this.curRows)
      await this.$nextTick()
      await this.renderTable(this.curColumnHeaders, this.curRowHeaders, this.curRows)
      // 表格折叠隐藏后高度变化需要重新加载数据
      this.clearTimer(this.tableTimer)
      this.tableTimer = setTimeout(() => {
        this.$refs.vxeGrid.scrollTo(isExpand ? scrollInfo.scrollLeft - 1 : scrollInfo.scrollLeft + 1, scrollInfo.scrollTop)
      }, 10)
    },
    renderTable(columnHeaders, rowHeaders, rows) {
      // 获取列
      const columns = []

      const maxLevel = calculationLevel(rowHeaders)
      for (let i = 0; i < maxLevel; i++) {
        columns.push({ title: '', field: `empty${i}` })
      }
      // 再获取实列
      if (columnHeaders && columnHeaders.length) {
        let colCount = 0
        const cloneColumns = cloneDeep(columnHeaders)
        columnHeaders.forEach((col, idx) => {
          const { label, headers, id, parentId, type } = col
          const { path, isChild } = addPath(col, parentId, id, cloneColumns)
          if (!headers) {
            columns.push({
              title: label,
              field: `col${colCount}`,
              params: { path, isChild, id, type, isExpand: col.isExpand }
            })
            colCount++
          } else {
            const colGroup = {
              title: label,
              params: { id, type, path, isChild, isExpand: col.isExpand },
              children: []
            }
            const childHeaders = cloneDeep(headers)
            const groupId = id
            headers.forEach((child, idx) => {
              const { label, id, type, parentId } = child
              const { path, isChild } = addPath(child, parentId, id, childHeaders)
              const grandHeaders = cloneDeep(child.headers)
              if (grandHeaders && grandHeaders.length) {
                colGroup.children.push({
                  title: child.label,
                  children: [],
                  params: {
                    path,
                    isChild,
                    id,
                    type,
                    parentId,
                    groupId,
                    isExpand: child.isExpand
                  }
                })
                child.headers.forEach(grandChild => {
                  const { parentId, id, type } = grandChild
                  const { path, isChild } = addPath(grandChild, parentId, id, grandHeaders)
                  colGroup.children[idx].children.push({
                    title: grandChild.label,
                    field: `col${colCount}`,
                    params: {
                      path,
                      isChild,
                      id,
                      type,
                      parentId,
                      groupId,
                      isExpand: grandChild.isExpand
                    }
                  })
                  colCount++
                })
              } else {
                colGroup.children.push({
                  title: label,
                  field: `col${colCount}`,
                  children: [],
                  params: {
                    path,
                    isChild,
                    id,
                    type,
                    parentId,
                    groupId,
                    isExpand: child.isExpand
                  }
                })
                colCount++
              }
            })
            columns.push(colGroup)
          }
        })
      }
      // 获取数据
      const data = []
      if (rows && rows.length) {
        rows.forEach((row, idx) => {
          const cells = row.cells
          const obj = {}
          cells.forEach((cell, cellIdx) => {
            obj[`col${cellIdx}`] = cell.displayValue
            obj[`col${cellIdx}-originValue`] = cell.originalValue
            obj[`col${cellIdx}-cellValue`] = cell.cellValue
            obj[`col${cellIdx}-isEdit`] = cell.isEdite
            obj[`col${cellIdx}-align`] = cell.align
          })
          data.push(obj)
        })
      } else {
        if (rowHeaders && rowHeaders.length) {
          const allRowSpan = rowHeaders.reduce((prev, cur) => {
            return prev + cur.rowSpan
          }, 0)
          const allColSpan = columnHeaders && columnHeaders.length && columnHeaders.reduce((prev, cur) => {
            return prev + cur.colSpan
          }, 0)
          for (let i = 0; i < allRowSpan; i++) {
            const obj = {}
            for (let j = 0; j < allColSpan; j++) {
              obj[`col${j}`] = ''
              obj[`col${j}-isEdit`] = false
            }
            data.push(obj)
          }
        }
      }
      // 获取空列对应的行标签及合并的单元格
      const mergeCells = []
      // 合并单元格
      let initRowIdx1 = 0; let initRowIdx2 = 0; let initRowIdx3 = 0

      const cloneHeaders = cloneDeep(rowHeaders)
      let initSpanIdx1 = 0
      let initSpanIdx2 = 0
      let initSpanIdx3 = 0
      let initSpanIdx4 = 0
      if (rowHeaders && rowHeaders.length) {
        rowHeaders.forEach((row, idx) => {
          const { label: parentLabel, headers: parentHeaders, id, parentId, relId, colSpan, rowSpan, isExpand } = row
          for (let i = initSpanIdx1; i < (initSpanIdx1 + rowSpan); i++) {
            if (colSpan === 1) {
              data[i]['empty0'] = parentLabel
              data[i]['empty0-id'] = id
              data[i]['empty0-relId'] = relId
              data[i]['empty0-parentId'] = parentId
              data[i]['empty0-isExpand'] = isExpand === undefined ? true : isExpand
            } else if (colSpan === 2) {
              data[i]['empty0'] = parentLabel
              data[i]['empty1'] = parentLabel
              data[i]['empty0-id'] = id
              data[i]['empty0-relId'] = relId
              data[i]['empty0-parentId'] = parentId
              data[i]['empty0-isExpand'] = isExpand === undefined ? true : isExpand
            } else if (colSpan === 3) {
              data[i]['empty0'] = parentLabel
              data[i]['empty1'] = parentLabel
              data[i]['empty2'] = parentLabel
              data[i]['empty0-id'] = id
              data[i]['empty0-relId'] = relId
              data[i]['empty0-parentId'] = parentId
              data[i]['empty0-isExpand'] = isExpand === undefined ? true : isExpand
            } else if (colSpan === 4) {
              data[i]['empty0'] = parentLabel
              data[i]['empty1'] = parentLabel
              data[i]['empty2'] = parentLabel
              data[i]['empty3'] = parentLabel
              data[i]['empty0-id'] = id
              data[i]['empty0-relId'] = relId
              data[i]['empty0-parentId'] = parentId
              data[i]['empty0-isExpand'] = isExpand === undefined ? true : isExpand
            }
          }
          initSpanIdx1 = initSpanIdx1 + rowSpan
          addPath(row, parentId, id, cloneHeaders)
          if (parentHeaders && parentHeaders.length) {
            const cloneHeaders = cloneDeep(parentHeaders)
            parentHeaders.forEach((child, childIdx) => {
              const { label, headers: childHeaders, id, parentId, colSpan: childColSpan, rowSpan: childRowSpan } = child
              addPath(child, parentId, id, cloneHeaders)
              const validHeaders1 = childHeaders && childHeaders.length ? childHeaders : parentHeaders
              if (validHeaders1 && validHeaders1.length) {
                const cloneHeaders = cloneDeep(validHeaders1)
                validHeaders1.forEach((grandChild, idx) => {
                  const { label: grandLabel, headers: grandHeaders, id, parentId, colSpan: grandColSpan, rowSpan: grandRowSpan } = grandChild
                  addPath(grandChild, parentId, id, cloneHeaders)
                  const validHeaders2 = grandHeaders && grandHeaders.length ? grandHeaders : childHeaders
                  const cloneHeaders2 = cloneDeep(validHeaders2)
                  if (validHeaders2 && validHeaders2.length) {
                    validHeaders2.forEach(base => {
                      const { id, parentId } = base
                      addPath(base, parentId, id, cloneHeaders2)
                    })
                  }
                })
              }
            })
          }
          // let rowSpan1 = 0; let rowSpan2 = 0; let rowSpan3 = 0
          let initColIdx1 = 0
          if (rowSpan > 1 || colSpan > 1) {
            mergeCells.push({ row: initRowIdx1, col: initColIdx1, rowspan: rowSpan, colspan: colSpan })
          }
          initRowIdx1 = initRowIdx1 + rowSpan
          initColIdx1 = initColIdx1 + colSpan
          if (parentHeaders && parentHeaders.length) {
            parentHeaders.forEach(child => {
              const { colSpan: childColSpan, rowSpan: childRowSpan, headers: childHeaders } = child || {}
              if (childRowSpan > 1 || childColSpan > 1) {
                mergeCells.push({ row: initRowIdx2, col: initColIdx1, rowspan: childRowSpan, colspan: childColSpan })
              }
              initRowIdx2 = initRowIdx2 + childRowSpan
              const initColIdx2 = initColIdx1 + childColSpan
              if (childHeaders && childHeaders.length) {
                childHeaders.forEach(grand => {
                  const { colSpan: grandColSpan, rowSpan: grandRowSpan, headers: grandHeaders } = grand || {}
                  if (grandRowSpan > 1 || grandColSpan > 1) {
                    mergeCells.push({ row: initRowIdx3, col: initColIdx2, rowspan: grandRowSpan, colspan: grandColSpan })
                  }
                  initRowIdx3 = initRowIdx3 + grandRowSpan
                })
              }
            })
          }
        })
        // 获取empty1
        if (maxLevel >= 2) {
          rowHeaders.forEach((row, idx) => {
            const { headers: parentHeaders, colSpan, rowSpan } = row
            if (colSpan === 1) {
              if (parentHeaders && parentHeaders.length) {
                parentHeaders.forEach(child => {
                  const { label: childLabel, colSpan: childColSpan, rowSpan: childRowSpan, id, parentId, relId, isExpand } = child
                  for (let i = initSpanIdx2; i < (initSpanIdx2 + childRowSpan); i++) {
                    if (childColSpan === 1) {
                      data[i]['empty1'] = childLabel
                      data[i]['empty1-id'] = id
                      data[i]['empty1-relId'] = relId
                      data[i]['empty1-parentId'] = parentId
                      data[i]['empty1-isExpand'] = isExpand === undefined ? true : isExpand
                    } else if (childColSpan === 2) {
                      data[i]['empty1'] = childLabel
                      data[i]['empty2'] = childLabel
                      data[i]['empty1-id'] = id
                      data[i]['empty1-relId'] = relId
                      data[i]['empty1-parentId'] = parentId
                      data[i]['empty1-isExpand'] = isExpand === undefined ? true : isExpand
                    } else if (childColSpan === 3) {
                      data[i]['empty1'] = childLabel
                      data[i]['empty2'] = childLabel
                      data[i]['empty3'] = childLabel
                      data[i]['empty1-id'] = id
                      data[i]['empty1-relId'] = relId
                      data[i]['empty1-parentId'] = parentId
                      data[i]['empty1-isExpand'] = isExpand === undefined ? true : isExpand
                    }
                  }
                  initSpanIdx2 = initSpanIdx2 + childRowSpan
                })
              }
            } else {
              initSpanIdx2 = initSpanIdx2 + rowSpan
            }
          })
        }

        if (maxLevel >= 3) {
          // 获取empty2
          rowHeaders.forEach((row, idx) => {
            const { headers: parentHeaders, colSpan, rowSpan } = row
            if (colSpan === 1) {
              if (parentHeaders && parentHeaders.length) {
                parentHeaders.forEach(child => {
                  const { headers: childHeaders, colSpan: childColSpan, rowSpan: childRowSpan } = child
                  if (childColSpan === 1) {
                    if (childHeaders && childHeaders.length) {
                      childHeaders.forEach(grand => {
                        const { label: grandLabel, colSpan: grandColSpan, rowSpan: grandRowSpan, id, parentId, relId, isExpand } = grand
                        for (let i = initSpanIdx3; i < (initSpanIdx3 + grandRowSpan); i++) {
                          if (grandColSpan === 1) {
                            data[i]['empty2'] = grandLabel
                            data[i]['empty2-id'] = id
                            data[i]['empty2-relId'] = relId
                            data[i]['empty2-parentId'] = parentId
                            data[i]['empty2-isExpand'] = isExpand === undefined ? true : isExpand
                          } else {
                            data[i]['empty2'] = grandLabel
                            data[i]['empty3'] = grandLabel
                            data[i]['empty2-id'] = id
                            data[i]['empty2-relId'] = relId
                            data[i]['empty2-parentId'] = parentId
                            data[i]['empty2-isExpand'] = isExpand === undefined ? true : isExpand
                          }
                        }
                        initSpanIdx3 = initSpanIdx3 + grandRowSpan
                      })
                    }
                  } else {
                    initSpanIdx3 = initSpanIdx3 + childRowSpan
                  }
                })
              }
            } else {
              if (parentHeaders && parentHeaders.length) {
                parentHeaders.forEach(child => {
                  const { label: childLabel, colSpan: childColSpan, rowSpan: childRowSpan, id, parentId, relId, isExpand } = child
                  for (let i = initSpanIdx3; i < (initSpanIdx3 + childRowSpan); i++) {
                    if (childColSpan === 1) {
                      data[i]['empty2'] = childLabel
                      data[i]['empty2-id'] = id
                      data[i]['empty2-relId'] = relId
                      data[i]['empty2-parentId'] = parentId
                      data[i]['empty2-isExpand'] = isExpand === undefined ? true : isExpand
                    } else {
                      data[i]['empty2'] = childLabel
                      data[i]['empty3'] = childLabel
                      data[i]['empty2-id'] = id
                      data[i]['empty2-relId'] = relId
                      data[i]['empty2-parentId'] = parentId
                      data[i]['empty2-isExpand'] = isExpand === undefined ? true : isExpand
                    }
                  }
                  initSpanIdx3 = initSpanIdx3 + childRowSpan
                })
              }
            }
          })
        }
        if (maxLevel >= 4) {
          // 获取empty3
          rowHeaders.forEach((row, idx) => {
            const { label: parentLabel, headers: parentHeaders, colSpan, rowSpan } = row
            if (colSpan === 1) {
              if (parentHeaders && parentHeaders.length) {
                parentHeaders.forEach(child => {
                  const { label: childLabel, headers: childHeaders, colSpan: childColSpan, rowSpan: childRowSpan } = child
                  if (childColSpan === 1) {
                    if (childHeaders && childHeaders.length) {
                      childHeaders.forEach(grand => {
                        const { label: grandLabel, headers: grandHeaders, colSpan: grandColSpan, rowSpan: grandRowSpan } = grand
                        if (grandColSpan === 1) {
                          grandHeaders.forEach(base => {
                            const { label: baseLabel, headers: baseHeaders, colSpan: baseColSpan, rowSpan: baseRowSpan } = base
                            for (let i = initSpanIdx4; i < (initSpanIdx4 + baseRowSpan); i++) {
                              if (baseColSpan === 1) {
                                data[i]['empty3'] = baseLabel
                              } else {
                                data[i]['empty3'] = baseLabel
                                data[i]['empty4'] = baseLabel
                              }
                            }
                            initSpanIdx4 = initSpanIdx4 + baseRowSpan
                          })
                        } else {
                          for (let i = initSpanIdx4; i < (initSpanIdx4 + grandRowSpan); i++) {
                            data[i]['empty3'] = grandLabel
                          }
                          initSpanIdx4 = initSpanIdx4 + grandRowSpan
                        }
                      })
                    }
                  } else if (childColSpan === 2) {
                    if (childHeaders && childHeaders.length) {
                      childHeaders.forEach(grand => {
                        const { label: grandLabel, colSpan: grandColSpan, rowSpan: grandRowSpan } = grand
                        for (let i = initSpanIdx4; i < (initSpanIdx4 + grandRowSpan); i++) {
                          if (grandColSpan === 1) {
                            data[i]['empty3'] = grandLabel
                          } else {
                            data[i]['empty3'] = grandLabel
                            data[i]['empty4'] = grandLabel
                          }
                        }
                        initSpanIdx4 = initSpanIdx4 + grandRowSpan
                      })
                    }
                  } else {
                    for (let i = initSpanIdx4; i < (initSpanIdx4 + childRowSpan); i++) {
                      data[i]['empty3'] = childLabel
                    }
                    initSpanIdx4 = initSpanIdx4 + childRowSpan
                  }
                })
              }
            } else {
              if (colSpan === 2) {
                if (parentHeaders && parentHeaders.length) {
                  parentHeaders.forEach(child => {
                    const { label: childLabel, headers: childHeaders, colSpan: childColSpan, rowSpan: childRowSpan } = child
                    if (childColSpan === 1) {
                      childHeaders.forEach(grand => {
                        const { label: grandLabel, headers: grandHeaders, colSpan: grandColSpan, rowSpan: grandRowSpan } = grand
                        for (let i = initSpanIdx4; i < (initSpanIdx4 + grandRowSpan); i++) {
                          if (grandColSpan === 1) {
                            data[i]['empty3'] = grandLabel
                          } else {
                            data[i]['empty3'] = grandLabel
                            data[i]['empty4'] = grandLabel
                          }
                        }
                        initSpanIdx4 = initSpanIdx4 + grandRowSpan
                      })
                    } else {
                      for (let i = initSpanIdx4; i < (initSpanIdx4 + childRowSpan); i++) {
                        if (childColSpan === 1) {
                          data[i]['empty3'] = childLabel
                        } else {
                          data[i]['empty3'] = childLabel
                          data[i]['empty4'] = childLabel
                        }
                      }
                      initSpanIdx4 = initSpanIdx4 + childRowSpan
                    }
                  })
                }
              } else if (colSpan === 3) {
                if (parentHeaders && parentHeaders.length) {
                  parentHeaders.forEach(child => {
                    const { label: childLabel, headers: childHeaders, colSpan: childColSpan, rowSpan: childRowSpan } = child
                    for (let i = initSpanIdx4; i < (initSpanIdx4 + childRowSpan); i++) {
                      if (childColSpan === 1) {
                        data[i]['empty3'] = childLabel
                      } else {
                        data[i]['empty3'] = childLabel
                        data[i]['empty4'] = childLabel
                      }
                    }
                    initSpanIdx4 = initSpanIdx4 + childRowSpan
                  })
                }
              } else {
                // colSpan=4
                for (let i = initSpanIdx4; i < (initSpanIdx4 + rowSpan); i++) {
                  data[i]['empty3'] = parentLabel
                }
                initSpanIdx4 = initSpanIdx4 + rowSpan
              }
            }
          })
        }
      }
      const that = this

      function loopColumn(columns) {
        columns.forEach((col) => {
          if (col.children && col.children.length) {
            // 修复列分组时父集合不显示小三角的bug
            col.slots = {
              header: ({ column }) => {
                const { params } = column || {}
                if (params) {
                  const { isChild, path, isExpand } = params
                  const paddingTop = path ? 15 * (path.length - 2) + 'px' : '30px'
                  return [
                    <div style={{ paddingTop: paddingTop }}><span onClick={() => that.toggleColumn(column)} class={isChild ? '' : (isExpand ? 'vxe-icon-caret-down' : 'vxe-icon-caret-right')}>{column.title}</span></div>
                  ]
                } else {
                  return [
                    <div>{column.title}</div>
                  ]
                }
              }
            }
            loopColumn(col.children)
          } else {
            col.headerAlign = 'center'
            col.showHeaderOverflow = 'tooltip'
            col.minWidth = '180px'
            if (staticEmptyArr.includes(col.field)) {
              col.width = '200px'
            }
            if (col.field.includes('col')) {
              col.editRender = { autofocus: '.edit-input', autoselect: true }
            }
            col.slots = {
              // 使用 JSX 渲染
              edit: ({ row, column }) => {
                const { field } = column
                const disabledEdit = !row[`${field}-isEdit`]
                // const disabledEdit = false
                if (field.includes('col')) {
                  return [
                    <vxe-input disabled={disabledEdit} value={row[`${field}-originValue`]} onBlur={($event) => { row[field] = $event.value; row[`${field}-originValue`] = $event.value }} />
                  ]
                } else {
                  return [
                    <div><span class='vxe-icon-caret-down'>{row[field]}</span></div>
                  ]
                }
              },
              default: ({ row, column, rowIndex }) => {
                const { field } = column
                const disabledEdit = !row[`${field}-isEdit`]
                const align = row[`${field}-align`]
                // const disabledEdit = false
                if (field.includes('col')) {
                  return [
                    <div class={disabledEdit ? 'disabled-cell' : ''} style={{ textAlign: align }}>{row[field]}</div>
                  ]
                } else {
                  if (field === 'empty0' || field === 'empty1' || field === 'empty2' || field === 'empty3') {
                    const label = row[field]
                    let target = null
                    findRange(rowHeaders)
                    const parentRow = findCurRow(rowHeaders, rowIndex)
                    if (field === 'empty0') {
                      target = parentRow
                    } else if (field === 'empty1') {
                      const { colSpan, headers } = parentRow
                      if (colSpan === 1) {
                        target = headers
                      } else {
                        target = parentRow
                      }
                    } else if (field === 'empty2') {
                      const { colSpan, headers, range } = parentRow
                      if (colSpan === 1) {
                        const a = rowIndex - range[0]
                        findRange(headers)
                        const childRow = findCurRow(headers, a)
                        const { headers: childHeaders, colSpan: childSpan } = childRow
                        if (childSpan === 1) {
                          target = childHeaders
                        } else {
                          target = childRow
                        }
                      } else if (colSpan === 2) {
                        target = headers
                      } else {
                        target = parentRow
                      }
                    } else if (field === 'empty3') {
                      const { colSpan, headers, range } = parentRow
                      if (colSpan === 1) {
                        const a = rowIndex - range[0]
                        findRange(headers)
                        const childRow = findCurRow(headers, a)
                        const { headers: childHeaders, colSpan: childSpan, range: childRange } = childRow
                        if (childSpan === 1) {
                          const b = rowIndex - range[0] - childRange[0]
                          findRange(childHeaders)
                          const grandRow = findCurRow(childHeaders, b)
                          const { headers: grandHeaders, colSpan: grandColSpan, range: grandRange } = grandRow
                          if (grandColSpan === 1) {
                            target = grandHeaders
                          } else {
                            target = grandRow
                          }
                        } else if (childSpan === 2) {
                          target = childHeaders
                        } else {
                          target = childRow
                        }
                      } else if (colSpan === 2) {
                        const a = rowIndex - range[0]
                        findRange(headers)
                        const childRow = findCurRow(headers, a)
                        const { headers: childHeaders, colSpan: childSpan } = childRow
                        if (childSpan === 1) {
                          target = childHeaders
                        } else {
                          target = childRow
                        }
                      } else if (colSpan === 3) {
                        target = headers
                      } else {
                        target = parentRow
                      }
                    }
                    const validTarget = Array.isArray(target) ? target.find(item => item.label === label) : target
                    const { path, isChild, isExpand } = validTarget || {}
                    const paddingLeft = path ? 30 * (path.length - 2) + 'px' : '60px'
                    return [
                      <div style={{ paddingLeft: paddingLeft }}><span onClick={($event) => that.toggleRow(row, rowIndex, field)} class={isChild ? '' : isExpand ? 'vxe-icon-caret-down' : 'vxe-icon-caret-right'}>{row[field]}</span></div>
                    ]
                  } else {
                    return [
                      <div style={{ textAlign: align }}>{row[field]}</div>
                    ]
                  }
                }
              },
              header: ({ column }) => {
                const { params } = column || {}
                if (params) {
                  const { isChild, path, isExpand } = params
                  const paddingTop = path ? 15 * (path.length - 2) + 'px' : '30px'
                  return [
                    <div style={{ paddingTop: paddingTop }}><span onClick={() => that.toggleColumn(column)} class={isChild ? '' : (isExpand ? 'vxe-icon-caret-down' : 'vxe-icon-caret-right')}>{column.title}</span></div>
                  ]
                } else {
                  return [
                    <div>{column.title}</div>
                  ]
                }
              }
            }
          }
        })
      }
      loopColumn(columns)
      // 列渲染slots
      const chartMaxWidth = this.findLongesLabel(data) * 12 + 150
      columns.forEach(item => {
        if (item.field === 'empty0') {
          item.width = `${chartMaxWidth}px`
        }
      })
      this.gridOptions.columns = columns
      this.gridOptions.data = data
      this.gridOptions.mergeCells = mergeCells
      this.tableMetaJson = columns
      this.tableData = data
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
.config-table {
  width: 100%;
  height: 100%;
  ::v-deep .vxe-icon-caret-right, .vxe-icon-caret-down {
    font-size: 12px;
    &::before {
      cursor: pointer;
      color: #939599;
    }
  }
  ::v-deep .vxe-table--header-wrapper {
    .vxe-icon-edit {
      display: none;
    }
    .vxe-header--row {
      .col--group {
        .vxe-cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .vxe-cell {
        max-height: 100px !important;
      }
    }
  }
  ::v-deep .vxe-table--body-wrapper {
    .vxe-cell {
      min-width: 170px !important;
      padding: 0px !important;
      .disabled-cell {
        height: 22px;
        background: rgba(198,223,250,0.5);
      }
      .vxe-input {
        width: 100%;
        border: none !important;
        height: 20px !important;
        position: relative;
        &.is--disabled {
          .vxe-input--inner {
            background: rgba(198,223,250,0.5);
          }
        }
        .vxe-input--inner {
          position: absolute;
          border: none !important;
          border-radius: 0px !important;
        }
      }
    }
  }
}
</style>
