<template>
  <div class="table-list">
    <vxe-table
      ref="xTree"
      size="mini"
      :loading="loading"
      show-overflow
      :tree-config="{transform: true}"
      :data="tableData"
      :row-config="{isCurrent: true, isHover: true}"
    >
      <vxe-column field="tableName" title="名称" tree-node show-overflow>
        <template #default="{ row }">
          <span class="flex-vertical-center custom-node" :class="row.type==='INSTANCE'?'table-node':''" @click="clickTreeNode(row)">
            <template v-if="row.type==='GROUP'">
              <i class="tree-node-icon" :class="$refs.xTree.isTreeExpandByRow(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'" />
            </template>
            <template v-else>
              <i class="tree-node-icon vxe-icon-custom-column" />
            </template>
            <div class="custom-ellipsis">{{ row.tableName }}</div>
          </span>
        </template>
      </vxe-column>
      <vxe-column field="cellSize" title="单元格数" />
      <vxe-column field="memorySize" title="耗用内存">
        <template #default="{ row }">
          <div>{{ row.memorySize?row.memorySize+'G':'' }}</div>
        </template>
      </vxe-column>
      <vxe-column field="createTime" title="创建日期" />
      <vxe-column field="updateTime" title="修改日期" />
      <vxe-column title="操作" width="450">
        <template #default="{ row }">
          <el-button v-if="row.type==='GROUP'" size="mini" icon="el-icon-circle-plus-outline" type="text" status="primary" @click="addChildGroup(row)">新增子组</el-button>
          <el-button v-if="row.type==='GROUP'" size="mini" type="text" status="primary" icon="el-icon-circle-plus-outline" @click="openAddCube(row,true)">{{ '新增数方' }}</el-button>
          <el-button size="mini" type="text" status="primary" icon="el-icon-delete" @click="delNode(row)">删除</el-button>
          <el-button size="mini" type="text" status="primary" icon="el-icon-edit-outline" @click="openReName(row)">重新命名</el-button>
          <el-button v-if="row.type==='INSTANCE'" size="mini" type="text" status="primary" icon="el-icon-edit-outline" @click="openAddCube(row,false)">修改数方</el-button>
          <el-button
            v-if="row.type==='INSTANCE'"
            size="mini"
            type="text"
            status="primary"
            icon="el-icon-connection"
            @click="hanldeRule(row)"
          >计算规则
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 新增数方 -->
    <add-table v-if="addCubeVisible" :current-node-data="currentNodeData" :is-add-cube="isAddCube" :cube-info="cubeInfo" @addCube="addCube" @close="closeCubeDia" />
    <!-- 重命名弹框 -->
    <el-dialog title="重新命名" :visible.sync="reNameVisible" width="400px" :close-on-click-modal="false" @close="closeReName">
      <el-form ref="reNameForm" label-position="right" label-width="60px" size="small" :model="reNameForm" :rules="reNameRules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="reNameForm.name" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeReName">取 消</el-button>
        <el-button size="mini" type="primary" :loading="renameBtnLoad" @click="handleReName">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddTable from './add-table.vue'
import { addDimTable, delNode, editDimTable, getDim, renameCube } from '@/api/data-cube'
// import { getList } from '@/api/base-table'

export default {
  components: { AddTable },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addGroupVisible: false,
      addGroupForm: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ]
      },
      reNameVisible: false,
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      addCubeVisible: false,
      currentNodeData: {},
      currentNodeId: '',
      renameBtnLoad: false,
      isAddCube: true,
      cubeInfo: {}
    }
  },
  computed: {

  },
  created() {
    // this.fetchData()
  },
  methods: {
    // 新增数方
    addCube(params) {
      const parentId = this.isAddCube ? this.currentNodeData.id : this.currentNodeData.parentId
      const validParams = { ...params, type: 'INSTANCE', parentId }
      const validApi = this.isAddCube ? addDimTable : editDimTable
      validApi(validParams).then(res => {
        if (res.code === 200) {
          this.$message.success(this.isAddCube ? '新增成功' : '修改成功')
          this.$emit('refreshTable', res.data)
          this.closeCubeDia()
        }
      })
    },
    // 关闭分组弹框
    closeAddGroup() {
      this.addGroupVisible = false
    },
    handleAddGroup() {
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          alert('submit!')
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 展开树
    expandTree(row) {
      this.expandTimer = setTimeout(() => {
        this.$refs.xTree.setTreeExpand(row, true)
      }, 0)
    },
    // 高亮行
    setCurrentRow(row) {
      this.activeTimer = setTimeout(() => {
        this.$refs.xTree.setCurrentRow(row)
      }, 100)
    },
    // 清除高亮
    clearCurrentRow() {
      this.$refs.xTree.clearCurrentRow()
    },
    // 新增子组
    addChildGroup(row) {
      this.$emit('addChildGroup', row)
    },
    // 新增或编辑数方
    openAddCube(row, flag) {
      this.isAddCube = flag
      this.currentNodeData = row
      this.cubeInfo = {}
      if (!flag && row && row.type === 'INSTANCE') {
        getDim(row.id).then(res => {
          if (res.code === 200) {
            this.cubeInfo = res.data
            this.addCubeVisible = true
          }
        })
      } else {
        this.addCubeVisible = true
      }
    },
    closeCubeDia() {
      this.addCubeVisible = false
    },
    // 删除节点
    delNode(row) {
      const delTxt = row.type === 'GROUP' ? '删除分组时，该分组下级的所有子组和数方将一并删除，是否继续?' : '确定删除该表格吗?'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delNode(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const target = this.tableData.find(item => item.id === row.id)
            this.$emit('refreshTable', target.parentId)
          }
        })
      })
    },
    resetRenameForm() {
      if (this.$refs.reNameForm) {
        this.$refs.reNameForm.resetFields()
      }
    },
    // 打开重命名弹框
    openReName(row) {
      const { tableName } = row
      this.reNameVisible = true
      this.resetRenameForm()
      this.reNameForm.name = tableName
      this.currentNodeData = row
    },
    closeReName() {
      this.reNameVisible = false
    },
    handleReName() {
      this.$refs['reNameForm'].validate((valid) => {
        if (valid) {
          const { type, id, parentId } = this.currentNodeData
          const params = {
            id: id,
            tableName: this.reNameForm.name,
            type: type,
            parentId: parentId
          }
          this.renameBtnLoad = true
          renameCube(params).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.closeReName()
              this.$emit('refreshTable', id)
            }
          }).finally(() => {
            this.renameBtnLoad = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    clickTreeNode(row) {
      if (row.type !== 'INSTANCE') return
      this.$emit('openTableDetail', row)
    },
    hanldeRule(row) {
      console.log('打开规则行', row)
      this.$emit('openRule', row)
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.table-node {
  color: $primary;
  cursor: pointer;
  i {
    margin-right: 2px;
  }
}
.calc-btn {
  text-align: right;
  margin-bottom: 15px;
}
.calc-table {
  height: 500px;
  overflow-y: auto;
}
</style>
