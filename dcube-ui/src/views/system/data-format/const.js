// 数值类型
export const numberArr = ['INTEGER', 'DOUBLE']
// 日期显示
export const dateTypeArr = Object.freeze([
  {
    label: 'yyyy年mm月dd日',
    value: '10'
  },
  {
    label: 'yyyy-mm-dd',
    value: '11'
  },
  {
    label: 'yyyy/mm/dd',
    value: '12'
  },
  {
    label: 'yyyy/mm',
    value: '13'
  },
  {
    label: 'yyyy-MM-dd HH:mm:ss',
    value: '14'
  }
])
// 数字显示
export const numberTypeArr = Object.freeze([
  {
    label: '无千分分隔',
    value: '21'
  },
  {
    label: '有千分分隔',
    value: '20'
  }
])
// 对齐方式
export const alignArr = Object.freeze([{
  label: '左对齐',
  value: 'left'
}, {
  label: '居中对齐',
  value: 'middle'
}, {
  label: '右对齐',
  value: 'right'
}])
// 原数据示例
export const originEgMap = {
  VARCHAR: '超算表格',
  DOUBLE: 100000000.0123,
  INTEGER: 100000000,
  DATE: '2025/01/01'
}

// 显示数据示例
export const showEgMap = {
  VARCHAR: '超算表格',
  NUMBER: {
    20: '100,000,000',
    21: '100000000'
  },
  DATE: {
    10: '2025年1月1日',
    11: '2025-01-01',
    12: '2025/01/01',
    13: '2025/01',
    14: '2025-01-01 10:00:00'
  }
}

