.source-config-wrap{
  width: 100%;
  height: 100%;
  padding: 0 12px;
  display: flex;
  flex-direction: column;
  .config-area{
    display: inline-flex;
    align-items: flex-end;
    .form-wrap{
      flex: 1;
      .el-form-item{
        width: 95%;
        display: inline-flex;
        .el-form-item__content{
          flex: 1;
          .el-select{
            width: 100%;
          }
        }
      }
    }
    .btns-wrap{
      flex-shrink: 0;
      margin-bottom: 15px;
    }
  }
  .table-area{
    flex: 1;
    display: inline-flex;
    gap: 24px;
    >*{
      flex: 1;
      overflow: hidden;
    }
    .table-title{
      font-size: 16px;
      color: #004DE7;
      line-height: 24px;
      height: 24px;
    }
    .vxe-table{
      min-height: 180px;
      height: calc(100% - 24px - 36px);
    }
    .table-totoal{
      margin: 8px 6px 0 0;
      line-height: 28px;
      font-size: 13px;
      color:#606266;
      text-align: right;
    }
  }
  .operate-wrap{
    text-align: center;
    margin: 12px 0;
  }
}
.pop-form-wrap.el-form .el-form-item{
  margin-right: 0;
}
.source-import-progress {
  text-align: center;
  margin-bottom: 12px;
  .label {
    // color: $primary;
  }
  .el-progress{
    width: 600px;
    margin: 0 auto;
  }
}

