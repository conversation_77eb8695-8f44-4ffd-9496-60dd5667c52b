<template>
  <div class="app-container pt-5">
    <!-- tag区域 -->
    <div class="tag-box">
      <el-tag
        v-for="tag in tags"
        :key="tag.name"
        effect="plain"
        :closable="tag.close"
        :type="tag.id===currentTagId?'':'info'"
        size="small"
        @click="clickTag(tag)"
        @close="closeTag(tag)"
      >
        {{ tag.name }}
      </el-tag>
    </div>
    <!-- 源数据列表 -->
    <div v-show="currentTagId === 0">
      <div class="top-search-area">
        <el-form ref="queryForm" :model="queryParams" size="small" :inline="true">
          <el-form-item label="名称" prop="sourceName">
            <el-input
              v-model="queryParams.sourceName"
              placeholder="请输入数据源名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="main-btn">
          <el-button
            plain
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="openOriginDia"
          >新增数据源</el-button>
        </div>
      </div>
      <vxe-table
        ref="sourceTable"
        :loading="loading"
        size="mini"
        show-overflow
        :data="sourceList"
        :row-config="{isCurrent: true, isHover: true}"
      >
        <vxe-column field="sourceName" title="数据源名称">
          <template #default="{ row }">
            <div class="source-name custom-ellipsis" @click="goConfig(row)">{{ row.sourceName }}</div>
          </template>
        </vxe-column>
        <vxe-column field="sourceType" title="数据源类型" />
        <vxe-column field="dataFoundationFlag" title="是否底座">
          <template #default="{ row }">
            <div>{{ row.dataFoundationFlag==='Y'?'是':'否' }}</div>
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建日期">
          <template #default="{ row }">
            <span>{{ parseTime(row.createTime) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="updateTime" title="修改日期">
          <template #default="{ row }">
            <span>{{ parseTime(row.updateTime) }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" width="300">
          <template #default="{ row }">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="editOrigin(row)"
            >修改</el-button>
            <el-button
              v-if="row.sourceType!=='Excel'"
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="copyOrigin(row)"
            >复制</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="delOrigin(row)"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="iconfont icon-new-setting"
              @click="setBase(row)"
            >设为底座</el-button>
          </template>
        </vxe-column>
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>
      <el-pagination
        :current-page="pageObj.pageNum"
        :page-sizes="[20,50,100]"
        :page-size="pageObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!-- 新增数据源对话框 -->
      <el-drawer
        :title="dialogTitle"
        :visible.sync="originDiaShow"
        size="50%"
        @close="closeOriginDia"
      >
        <el-collapse v-model="activeCollapse" class="custom-collapse custom-scroll">
          <el-collapse-item title="数据源表单" name="1">
            <el-form ref="addOriginForm" class="view-form" size="small" :model="addOriginForm" :rules="rules" label-position="right" label-width="110px">
              <el-form-item label="数据源名称" prop="sourceName">
                <el-input v-model="addOriginForm.sourceName" />
              </el-form-item>
              <el-form-item label="数据源类型" prop="sourceType">
                <el-select v-model="addOriginForm.sourceType" :disabled="curType==='edit'" size="small" placeholder="请选择" @change="selecOriginType">
                  <el-option
                    v-for="item in sourceTypeArr"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="addOriginForm.sourceType==='Excel'" label="文件路径" prop="filePath">
                <div class="upload-file">
                  <div class="path">{{ addOriginForm.filePath }}</div>
                  <el-progress v-if="showUploadProgress" class="upload-progress" :percentage="uploadProgress" />
                  <el-button type="primary" size="mini" :loading="loadUploadBtn" @click="importData">{{ uploatText }}</el-button>
                </div>
              </el-form-item>
              <el-form-item v-if="showSaveProgress" label="保存进度">
                <el-progress :percentage="saveProgress" />
              </el-form-item>
              <template v-if="addOriginForm.sourceType!=='Excel'">
                <el-form-item label="数据库驱动" prop="sourceConfig.driverClass">
                  <el-input v-model="addOriginForm.sourceConfig.driverClass" />
                </el-form-item>
                <el-form-item label="数据库地址" prop="sourceConfig.url">
                  <el-input v-model="addOriginForm.sourceConfig.url" maxlength="100" />
                </el-form-item>
                <el-form-item label="数据库用户" prop="sourceConfig.username">
                  <el-input v-model="addOriginForm.sourceConfig.username" />
                </el-form-item>
                <el-form-item label="数据库密码" prop="sourceConfig.password">
                  <el-input v-model="addOriginForm.sourceConfig.password" type="password" />
                </el-form-item>
              </template>
              <el-form-item style="text-align:right;">
                <el-button v-if="addOriginForm.sourceType!=='Excel'" type="primary" plain size="mini" :loading="loadUrlBtn" @click="testUrl">测试连接</el-button>
                <!-- <el-button type="primary" plain size="mini" @click="handlerPreview">数据预览</el-button> -->
                <el-button type="primary" plain size="mini" :loading="loadSave" :disabled="disableSave" @click="handleOrigin">保 存</el-button>
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-drawer>
    </div>
    <source-config v-if="currentTagId !== 0" :cur-source-detail="curSourceDetail" @updateTagCache="updateTagCache" />
  </div>
</template>

<script>
import VXETable from 'vxe-table'
import {
  deleteSource,
  addSource,
  editSource,
  getSourceDetail,
  getSourceTypeList,
  testConnection,
  preview,
  getProcess,
  addExcelView,
  editExcelView,
  newGetSourceList,
  setFoundation
} from '@/api/system/data-origin'
import { mergeFile, uploadSliceFile } from '@/api/common'
import JSEncrypt from 'jsencrypt'
import { cloneDeep } from 'lodash'
import sourceConfig from './components/source-config.vue'
export default {
  components: { sourceConfig },
  dicts: ['sys_normal_disable'],
  data() {
    const validUrl = (rule, value, callback) => {
      // const reg = /^([a-z]+:)([a-z]+:)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/
      if (!value) {
        callback(new Error('请输入url地址'))
      } else if (value && value.length > 100) {
        callback(new Error('url地址字符长度不能超过100'))
      } else {
        callback()
      }
    }
    return {
      tags: [
        { name: '数据源列表', id: 0, close: false }
      ],
      currentTagId: 0,
      curSourceDetail: {},
      queryParams: {
        sourceName: undefined
      },
      pageObj: {
        pageNum: 1,
        pageSize: 20
      },
      total: 0,
      sourceList: [],
      loading: false,
      originDiaShow: false,
      addOriginForm: {
        sourceName: '',
        sourceType: '',
        filePath: '',
        sourceConfig: {
          url: '',
          username: '',
          password: '',
          driverClass: '',
          properties: {}
        }
      },
      rules: {
        sourceName: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' }
        ],
        sourceType: [
          { required: true, message: '请选择数据源类型', trigger: ['change', 'blur'] }
        ],
        filePath: [
          { required: true, message: '请选择Excel文件', trigger: 'blur' }
        ],
        'sourceConfig.driverClass': [
          { required: true, message: '请输入数据库驱动', trigger: 'blur' }
        ],
        'sourceConfig.url': [
          { required: true, message: '请输入数据库地址', trigger: 'blur' },
          { required: true, validator: validUrl, trigger: 'blur' }
        ],
        'sourceConfig.username': [
          { required: true, message: '请输入数据库用户', trigger: 'blur' }
        ],
        'sourceConfig.password': [
          { required: true, message: '请输入数据库密码', trigger: 'blur' }
        ]
      },
      // sqlPreviewShow: false,
      loadingPreview: false,
      // 数据源行数据
      previewList: [],
      // 数据源列数据
      tableMetaJson: [],
      previewParams: {
        timestamp: undefined,
        fileName: undefined
      },
      sourceTypeArr: [],
      curType: 'add',
      activeCollapse: ['1'],
      loadUrlBtn: false,
      totalPreview: 0,
      loadUploadBtn: false,
      loadSave: false,
      uploatText: '文件上传',
      disableSave: false,
      showSaveProgress: false,
      saveProgress: 0,
      showUploadProgress: true,
      uploadProgress: 0,
      timer: null,
      saveExcelViewCol: []
    }
  },
  computed: {
    dialogTitle() {
      return this.curType === 'add' ? '新增数据源' : '编辑数据源'
    }
  },
  created() {
    this.getList()
    getSourceTypeList().then(res => {
      this.sourceTypeArr = res.data
    })
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    setBase(row) {
      this.$confirm('已经设置了其他数据源作为数据底座，是否将此数据源覆盖为新的数据底座?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setFoundation(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '设置成功!'
            })
            this.getList()
          }
        })
      })
    },
    // 点击tag
    clickTag(tag) {
      this.currentTagId = tag.id
      if (tag.id !== 0) {
        this.curSourceDetail = tag.detail
      } else {
        this.getList()
      }
    },
    closeTag(tag) {
      if (tag.id === this.currentTagId) {
        this.$refs.sourceTable.clearCurrentRow()
      }
      this.tags = this.tags.filter((item) => item.id !== tag.id)
      if (this.currentTagId !== 0) {
        this.currentTagId = this.tags.length === 1 ? 0 : this.tags[1].id
      }
    },
    updateTagCache(params) {
      const tagItem = this.tags.find(item => item.id === this.currentTagId)
      if (tagItem) {
        tagItem.detail = params
      }
    },
    // 数据源配置页面
    goConfig(row) {
      const { id, sourceName } = row
      console.log('数据源配置页面', row.sourceName, row)
      const target = this.tags.find(item => item.id === id)
      if (!target) {
        this.currentTagId = id
        this.tags.push({ name: sourceName, id: id, close: true, detail: row })
        this.curSourceDetail = row
      } else {
        this.currentTagId = target.id
      }
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = null
    },
    selecOriginType(value) {
      this.initData()
      const typeItem = this.sourceTypeArr.find(item => item.code === value)
      this.addOriginForm.sourceConfig.driverClass = typeItem && typeItem.driverClassName
      this.addOriginForm.sourceConfig.url = typeItem && typeItem.url
    },
    async importData() {
      try {
        const { file } = await VXETable.readFile({
          types: ['xlsx', 'xls']
        })
        console.log('上传文件', file)
        this.loadUploadBtn = true
        this.uploatText = '文件上传中'
        this.disableSave = true
        this.addOriginForm.filePath = ''
        this.uploadFile(file)
      } catch (e) {
        console.log(e)
      }
    },
    async uploadFile(file) {
      this.showUploadProgress = true
      this.uploadProgress = 0
      console.log('文件名', file, file.name)
      const size = file.size
      console.log('文件大小', size)
      const chunkSize = 5 * 1024 * 1024 // 1个分片 10M
      let start = 0 // 上传的开始位置
      let index = 0 // 分片索引, 从0开始(0,1,2...)
      const totalFragmentCount = Math.ceil(size / chunkSize) // 总的分片数量
      const timestamp = '' + new Date().getTime()
      console.log('分片数量', totalFragmentCount)
      while (true) {
        let end // 当前分片的结束位置（不包括，开区间）
        if (start + chunkSize > size) { // 如果加上了一个分片大小，超出了文件的大小, 那么结束位置就是文件大小
          end = size
        } else {
          end = start + chunkSize // 如果加上了一个分片大小，没超出了文件的大小, 那么结束位置就是start加上分片大小
        }
        // 对file分片,分片完后, 给分片一个名字, 这个名字可以在后台获取为分片文件的真实名字
        const sfile = new File([file.slice(start, end)], `${file.name}-${index}`)
        // 上传完这个分片后, 再走下面的代码
        await this.uploadFragmentFile(sfile, index, file.name, totalFragmentCount, timestamp)
        index++
        if (end === size) { // 检查是否传完了, 传完了的话, 就跳出循环
          break
        }
        // 开始位置
        start = end
      }
      console.log('发送合并文件请求')
      this.mergeFile(file.name, timestamp)
    },
    // 上传分片文件（将切分的分片文件上传）
    async uploadFragmentFile(sfile, index, realFilename, totalFragmentCount, timestamp) {
      console.log('分片', sfile, index, realFilename, totalFragmentCount)
      const formData = new FormData()
      formData.append('sFile', sfile)
      formData.append('index', index)
      formData.append('realFilename', realFilename)
      formData.append('timestamp', timestamp)
      console.log('sfile', sfile, index)
      await uploadSliceFile(formData).then(res => {
        if (res.code === 200) {
          console.log(`上传第${index}个分片成功`)
          this.uploadProgress = parseFloat(((index + 1) / totalFragmentCount * 100).toFixed(1))
        }
      }).catch(() => {
      })
    },

    // 合并分片文件（当所有分片上传成功之后, 发送合并分片的请求）
    mergeFile(realFilename, timestamp) {
      const formData = new FormData()
      formData.append('realFilename', realFilename)
      formData.append('timestamp', timestamp)
      mergeFile(formData).then(res => {
        if (res.code === 200) {
          this.$message.success('上传成功')
          this.addOriginForm.filePath = res.data
          // this.preview(res.data)
        }
      }).finally(() => {
        this.uploatText = '文件上传'
        this.loadUploadBtn = false
        this.disableSave = false
        this.showUploadProgress = false
      })
    },
    preview(fileName) {
      const timestamp = new Date().getTime()
      this.previewParams.fileName = fileName
      this.previewParams.timestamp = timestamp
      this.loadingPreview = true
      this.uploatText = '数据预览中'
      this.activeCollapse = ['1', '2']
      preview(this.previewParams).then(res => {
        if (res.code === 200) {
          const { result, totalCount, columns } = res.data
          this.tableMetaJson = this.formatCol(result)
          this.previewList = result
          this.totalPreview = totalCount
          this.saveExcelViewCol = columns
        }
      }).finally(() => {
        this.uploatText = '文件上传'
        this.loadUploadBtn = false
        this.disableSave = false
        this.loadingPreview = false
      })
    },
    formatCol(result) {
      if (!result || !result.length) return []
      const arr = Object.keys(result[0])
      const res = arr.map(item => {
        return {
          code: item,
          name: item
        }
      })
      return res
    },
    /** 查询数据源列表 */
    getList() {
      const decryptor = new JSEncrypt()
      const keypair = decryptor.getKey()
      // 获取公钥和私钥
      const publicKey = keypair.getPublicKey()
      const privateKey = keypair.getPrivateKey()
      const validPublicKey = this.formatKey(publicKey, 1)
      const validPrivateKey = this.formatKey(privateKey, 2)
      this.loading = true
      newGetSourceList({ ...this.queryParams, publicKey: validPublicKey }, this.pageObj).then(response => {
        this.sourceList = response.rows
        decryptor.setPrivateKey(validPrivateKey)
        this.sourceList.forEach(item => {
          item.sourceType = decryptor.decrypt(item.sourceType)
        })
        this.total = response.total
        this.loading = false
      })
    },
    handleSizeChange(val) {
      this.pageObj.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageObj.pageNum = val
      this.getList()
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.pageObj.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 添加数据源 */
    openOriginDia() {
      this.curType = 'add'
      this.batchOperate()
      this.rules.filePath = [
        { required: true, message: '请选择Excel文件', trigger: 'blur' }
      ]
    },
    initData() {
      this.previewList = []
      this.tableMetaJson = []
      this.totalPreview = 0
    },
    // 关闭数据源弹框
    closeOriginDia() {
      this.resetForm('addOriginForm')
      this.clearTimer()
      this.originDiaShow = false
    },
    formatKey(key, type) {
      return type === 1 ? key.replace(new RegExp('-----BEGIN PUBLIC KEY-----', 'g'), '').replace(new RegExp('-----END PUBLIC KEY-----', 'g'), '').replace(/[\r\n]/g, '') : key.replace(new RegExp('-----BEGIN RSA PRIVATE KEY-----', 'g'), '').replace(new RegExp('-----END RSA PRIVATE KEY-----', 'g'), '').replace(/[\r\n]/g, '')
    },
    // 修改数据源
    async  editOrigin(row) {
      this.curType = 'edit'
      const decryptor = new JSEncrypt()
      const keypair = decryptor.getKey()
      // 获取公钥和私钥
      const publicKey = keypair.getPublicKey()
      const privateKey = keypair.getPrivateKey()
      const validPublicKey = this.formatKey(publicKey, 1)
      const validPrivateKey = this.formatKey(privateKey, 2)
      getSourceDetail({ publicKey: validPublicKey }, row.id).then(res => {
        if (res.code === 200) {
          this.batchOperate()
          this.addOriginForm = { ...res.data }
          const { sourceType } = res.data
          const { username, password, url, driverClass } = this.addOriginForm.sourceConfig
          decryptor.setPrivateKey(validPrivateKey)
          console.log('解密后的结果', decryptor.decrypt(username), decryptor.decrypt(password), decryptor.decrypt(url))
          this.addOriginForm.sourceConfig.username = decryptor.decrypt(username)
          this.addOriginForm.sourceConfig.password = decryptor.decrypt(password)
          this.addOriginForm.sourceConfig.url = decryptor.decrypt(url)
          this.addOriginForm.sourceConfig.driverClass = decryptor.decrypt(driverClass)
          this.addOriginForm.sourceType = decryptor.decrypt(sourceType)
          if (sourceType === 'Excel') {
            delete this.rules.filePath
          }
        }
      })
    },
    batchOperate() {
      this.initData()
      this.resetForm('addOriginForm')
      this.originDiaShow = true
      this.disableSave = false
      this.showSaveProgress = false
      this.saveProgress = 0
      this.showUploadProgress = false
      this.uploadProgress = 0
      this.saveExcelViewCol = []
      this.activeCollapse = ['1']
    },
    async  copyOrigin(row) {
      this.curType = 'add'
      const decryptor = new JSEncrypt()
      const keypair = decryptor.getKey()
      // 获取公钥和私钥
      const publicKey = keypair.getPublicKey()
      const privateKey = keypair.getPrivateKey()
      const validPublicKey = this.formatKey(publicKey, 1)
      const validPrivateKey = this.formatKey(privateKey, 2)
      getSourceDetail({ publicKey: validPublicKey }, row.id).then(res => {
        if (res.code === 200) {
          this.originDiaShow = true
          this.resetForm('addOriginForm')
          delete res.data.id
          this.addOriginForm = { ...res.data }
          this.addOriginForm.sourceName = res.data.sourceName + '_COPY'
          const { sourceType } = res.data
          const { username, password, url, driverClass } = this.addOriginForm.sourceConfig
          decryptor.setPrivateKey(validPrivateKey)
          console.log(999999, decryptor.decrypt(username), decryptor.decrypt(password), decryptor.decrypt(url))
          this.addOriginForm.sourceConfig.username = decryptor.decrypt(username)
          this.addOriginForm.sourceConfig.password = decryptor.decrypt(password)
          this.addOriginForm.sourceConfig.url = decryptor.decrypt(url)
          this.addOriginForm.sourceConfig.driverClass = decryptor.decrypt(driverClass)
          this.addOriginForm.sourceType = decryptor.decrypt(sourceType)
        }
      })
    },
    // 删除数据源
    delOrigin(row) {
      this.$confirm(`确定删除该数据源吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSource(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      })
    },
    // 重置弹框
    resetForm(form) {
      if (form === 'addOriginForm') {
        this.addOriginForm = {
          sourceName: '',
          sourceType: '',
          filePath: '',
          sourceConfig: {
            url: '',
            username: '',
            password: '',
            driverClass: '',
            properties: {}
          }
        }
        if (this.$refs.addOriginForm) {
          this.$refs.addOriginForm.resetFields()
        }
      } else {
        this.queryParams.sourceName = undefined
        this.$refs.queryForm.resetFields()
      }
    },
    // 保存数据源
    handleOrigin() {
      this.$refs['addOriginForm'].validate(async(valid) => {
        if (valid) {
          this.loadSave = true
          const { sourceType, sourceName, filePath, id } = this.addOriginForm
          const handleFunc = this.curType === 'add' ? (sourceType === 'Excel' ? addExcelView : addSource) : (sourceType === 'Excel' ? editExcelView : editSource)
          const columns = this.saveExcelViewCol
          const excelParams = this.curType === 'add' ? {
            sourceType, sourceName, filePath, columns
          } : {
            sourceType, sourceName, filePath, id, columns
          }
          const params = sourceType === 'Excel' ? cloneDeep(excelParams) : cloneDeep({ ...this.addOriginForm })
          params.timestamp = new Date().getTime()
          const copySourceConfig = cloneDeep(params.sourceConfig)
          const { username, password, url, driverClass } = copySourceConfig || null
          let publicKey = this.$store.getters.publicKey
          if (!publicKey) {
            publicKey = await this.$store.dispatch('user/getPublicKey')
          }
          const encryptor = new JSEncrypt()
          encryptor.setPublicKey(publicKey)
          params.sourceConfig.driverClass = encryptor.encrypt(driverClass)
          params.sourceConfig.username = encryptor.encrypt(username)
          params.sourceConfig.password = encryptor.encrypt(password)
          params.sourceConfig.url = encryptor.encrypt(url)
          params.sourceType = encryptor.encrypt(sourceType)
          handleFunc(params).then(res => {
            if (res.code === 200) {
              if (sourceType !== 'Excel') {
                this.saveCallBack()
              } else {
                // 轮询进度接口
                if (filePath) {
                  this.loopProgress(params.timestamp)
                } else {
                  this.saveCallBack()
                }
              }
            } else {
              this.$message.error(res.msg)
            }
          }).finally(() => {
            this.loadSave = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveCallBack() {
      const msg = this.curType === 'add' ? '新增成功' : '修改成功'
      this.$message.success(msg)
      this.closeOriginDia()
      this.getList()
    },
    loopProgress(timestamp) {
      this.showSaveProgress = true
      this.saveProgress = 0
      this.timer = setInterval(() => {
        this.getProcessFunc(timestamp)
      }, 1000)
    },
    getProcessFunc(timestamp) {
      getProcess({ timestamp }).then(res => {
        const { code, data } = res
        if (code === 200 && data.process === 1) {
          this.saveCallBack()
          this.clearTimer()
        }
        this.saveProgress = Number((100 * (data.process)).toFixed(0))
      }).catch(() => {
        this.clearTimer()
      })
    },
    // 测试连接
    testUrl() {
      this.$refs['addOriginForm'].validate(async(valid) => {
        if (valid) {
          this.loadUrlBtn = true
          const { sourceName, sourceType, sourceConfig } = this.addOriginForm
          const copySourceConfig = cloneDeep(this.addOriginForm.sourceConfig)
          const { username, password, url, driverClass } = copySourceConfig || null
          let publicKey = this.$store.getters.publicKey
          if (!publicKey) {
            publicKey = await this.$store.dispatch('user/getPublicKey')
          }
          const encryptor = new JSEncrypt()
          encryptor.setPublicKey(publicKey)
          testConnection({ sourceName, sourceType: encryptor.encrypt(sourceType), ...sourceConfig, driverClass: encryptor.encrypt(driverClass), username: encryptor.encrypt(username), password: encryptor.encrypt(password), url: encryptor.encrypt(url) }).then(res => {
            this.$message.success(res.msg)
          }).finally(() => {
            this.loadUrlBtn = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import  '@/styles/variables';
.tag-box {
  margin-bottom:10px;
}
.source-name{
  color: #004DE7;
  cursor: pointer;
}
.table-wrapper {
  min-height: 300px;
  height: 100%;
}
.view-form {
  .el-select {
    width: 100%;
  }
}
.custom-collapse {
  height: calc(100vh - 70px);
  padding: 0 15px;
  overflow: auto;
  ::v-deep .el-collapse-item__header {
    color: $primary;
  }
}
.upload-file {
  display: flex;
  align-items: center;
  .path {
    margin-right: 10px;
  }
}
.upload-progress {
  width: 400px;
  ::v-deep .el-progress {
    line-height: 2;
  }
}
</style>
