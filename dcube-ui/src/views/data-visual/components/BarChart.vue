<template>
  <div id="barChart" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
// require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import dealData from './mixins/dealData'
import switchTheme from './mixins/switchTheme'

const animationDuration = 1000

// eslint-disable-next-line prefer-const
let legendRight = 80
export default {
  mixins: [resize, dealData, switchTheme],
  props: {
    titlePosition: { // 标题位置
      type: String,
      default: 'top'
    },
    isMax: { // 是否是全屏展示所有数据模式
      type: Boolean,
      default: false
    },
    showSeriesLabel: { // 是否展示图上数据
      type: Boolean,
      default: false
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    chartData: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    legendRight: {
      type: Number,
      default: legendRight
    }
  },
  data() {
    return {
      chart: null,
      dataZoom: [
        {
          show: true,
          start: 0,
          end: 10
        },
        {
          type: 'inside',
          start: 96,
          end: 100
        }
      ]
    }
  },
  computed: {
    defaultOptions() {
      const legendRight = this.legendRight
      return {
        title: {
          text: '柱状图示例',
          left: 'center',
          top: 10,
          padding: [0, 40, 0, 0],
          textStyle: {
            fontSize: 16,
            overflow: 'truncate',
            ellipsis: '...',
            width: 200
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['Forest'],
          type: 'scroll',
          right: 5,
          top: 'middle',
          orient: 'vertical',
          formatter: function(name) { // 字符过长的处理
            return echarts.format.truncateText(name, legendRight, '14px Microsoft Yahei', '…')
          },
          tooltip: {
            show: true
          }
        },
        grid: {
          left: '3%',
          right: legendRight + 35,
          top: 40,
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: ['2012', '2013', '2014', '2015', '2016']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Forest',
            type: 'bar',
            barGap: 0,
            emphasis: {
              focus: 'series'
            },
            data: [320, 332, 301, 334, 390]
          }
        ]
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(this.title, val)
      }
    },
    title(value) {
      this.setOptions(value, this.chartData)
    },
    showSeriesLabel(value) {
      this.setOptions(this.title, this.chartData)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.changeTheme())
      this.$_resizeHandler()
    })
    // if (this.isMax) {
    //   setTimeout(() => {
    //     this.$_resizeHandler()
    //   }, 3000)
    // }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getTitleBottomOption({ text }) {
      const legendRight = this.legendRight
      return { title: {
        text: text,
        left: 'center',
        bottom: 0,
        padding: [0, 40, 10, 0],
        textStyle: {
          fontSize: 12,
          overflow: 'truncate',
          ellipsis: '...',
          width: 200,
          fontFamily: '"SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif'
        }
      },
      grid: {
        left: '3%',
        right: legendRight + 35,
        top: 10,
        bottom: 40,
        containLabel: true
      }}
    },
    initChart(theme) {
      this.chart = echarts.init(this.$el, theme)
      this.setOptions(this.title, this.chartData)
    },
    /**
     *
     * @param title
     * @param chartData 需要展示的数据值数组222
     */
    setOptions(title, chartData = []) {
      chartData = chartData ?? []
      if (!chartData.length) {
        return this.chart.setOption({ ...this.defaultOptions, ...(this.titlePosition === 'top' ? {} : this.getTitleBottomOption('柱状图示例')) })
      }
      let xData = new Set()
      let legendData = new Set()
      const seriesData = []
      const _this = this
      const showSeriesLabel = this.showSeriesLabel ? {
        label: {
          show: true,
          position: 'top',
          formatter(params) { // 字符过长的处理
            const value = params.value
            return _this.isEmpty(value, 'money')
          }
        }
      } : {
        label: {
          show: false
        }
      }
      for (const item of chartData) {
        // item 是一个数组有三个值 [横轴值，图例值，指标值]
        if (item[0]) {
          xData.add(item[0])
        }
        if (item[1]) {
          legendData.add(item[1])
          // legendData.add(item[1] + '这使得得得得的点点滴滴大大大')
        }
      }// 横坐标
      xData = [...xData]
      if (!xData.length) return
      legendData = [...legendData]
      legendData.forEach(item => {
        // 找到对应图例的横坐标对应的指标的值
        const data = {}
        const result = []
        chartData.forEach(item2 => {
          if (item2[1] === item) { // 找到图例相同的
            data[item2[0]] = item2[2]
          }
          // console.log(data, item2, item2[2], 'datadatadatadatadatadatadatadatadatadatadata')
        })

        xData.forEach(item3 => {
          result.push(data[item3] || 0)
        })
        seriesData.push({
          name: item,
          type: 'bar',
          stack: false, // 不要堆叠处理
          data: result,
          ...showSeriesLabel
        })
      })
      // 处理放大的时候保留4位小数，展示前10条数据的比例
      this.dataZoom[0].end = Math.min(parseInt((10 / xData.length) * 10000) / 100, 100) // 默认放大的都是展示前10条数据
      const isMaxObj = this.isMax ? { dataZoom: this.dataZoom } : {}
      // console.log(xData, legendData, seriesData, 'seriesData')
      const selectedLegendData = {}// 避免图例过多页面卡死默认只展示10个图例
      legendData.slice(10).forEach(item => {
        selectedLegendData[item] = false
      })

      const legendOption = {
        ...this.defaultOptions.legend,
        data: legendData,
        selected: selectedLegendData // 默认只选中10个图例
      }
      this.chart.setOption({
        ...isMaxObj,
        title: {
          ...this.defaultOptions.title,
          text: title
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: legendOption,
        grid: this.defaultOptions.grid,
        xAxis: {
          type: 'category',
          // boundaryGap: false,
          data: xData
        },
        yAxis: this.defaultOptions.yAxis,
        series: seriesData,
        ...(this.titlePosition === 'top' ? {} : this.getTitleBottomOption({ text: title }))
      })
    }
  }
}
</script>
