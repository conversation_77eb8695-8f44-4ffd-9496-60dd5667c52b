<script>
import LineChart from './LineChart'
import PieChart from './Pie<PERSON>hart'
import Bar<PERSON>hart from './BarChart'
import barChartPng from '@/assets/bar.png'
import lineChartPng from '@/assets/line.png'
import pieChartPng from '@/assets/pie.png'
import { batchSaveOrUpdate, delWidget, getWidget, saveOrUpdateWidget, get2dTableList, getPreviewData, getAggregationType } from '@/api/data-visual'
import { deepClone } from '@/utils'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

const orderByList = [
  { code: '', value: '默认排序' },
  { code: 'ASC', value: '升序排序' },
  { code: 'DESC', value: '降序排序' }
]
export default {
  components: {
    LineChart,
    PieChart,
    BarChart,
    Treeselect
  },
  props: {
    curTableDetail: {
      type: Object,
      default: () => {
      }
    },
    isRt: { // 是否是运行态运行在首页等地方的
      type: Boolean,
      default: false
    },
    rtWidgets: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    const item = {}
    return {
      autoSize: true, // 用来处理值改变就重置echarts图的大小
      curMaxChart: {},
      maxChartShowDialog: false,
      dashboardName: '',
      aggregationType: [], // 有哪些聚合函数类型
      dimShowFilterDialog: false,
      modelType: 'edit', // edit:编辑视图模式；view:查看视图模式
      loadingDOM: null,
      widgetList: [],
      barChartPng, lineChartPng, pieChartPng,
      tableData: Array(20).fill(item),
      queryParams: {
        dashboardId: undefined
        // name: undefined
      },
      activeIndexArr: [0, 0],
      curChart: {},
      xDimKeyLabel: '',
      legendDimKeyLabel: '',
      indicatorKeyLabel: '',
      filterDimKeyLabel: '',
      currentModelDimShowFilterDialog: [], // 当前弹框需要展示的筛选维数据
      currentModelDimShowChart: {}, // 当前弹框打开的chart数据
      currentModelDimObj: {},
      form: {
        dataColumns: {},
        // legendColumns: {},
        title: '',
        sourceType: 'MEMORY',
        tableId: '',
        xDimKey: '',
        filterDimKey: '',
        legendDimKey: '',
        indicatorKey: '',
        height: 1,
        width: 1
      },
      listLoading: false,
      chartList: [],
      widthKey: 0, // 解决宽高输入框传入和内部不一致问题
      heightKey: 0, // 随机数刷新组件
      dialogVisible: false,
      radioKey: undefined,
      sourceTypeArr: [], // 数据源类型
      table2dData: [], // 二维表
      xDimArr: [], // 横轴维度
      filterDimArr: [], // 筛选维度
      legendDimArr: [], // 图例维度、
      indicatorDimArr: [], // 指标维度
      initialChartParams: {
        'dataColumns': [],
        'dimColumns': [
          {
            'aggregation': '', // 聚合类型
            'desc': '',
            'name': '',
            'orderBy': '', // 排序
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'legendColumns': [], // 图例维度相关的
        'filterColumns': [// 筛选维度相关的
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'page': {
          'currentPage': 1,
          'pageSize': 10
        },
        'sourceType': 'MEMORY',
        'viewId': '',
        'viewScript': ''
      },
      queryChartParams: {},
      dimShowCountDialog: false,
      hiddenPic: false,
      isFoldConfigForm: false, // 是否收起属性配置界面
      hasConfigChange: false// 记录是否有做了配置的更改，在修改右侧属性配置后会设置该值为true，切换卡片后会重置为false
    }
  },
  computed: {
    curTableId() {
      return this.curTableDetail && this.curTableDetail.id || ''
    },
    chartDisabled() {
      return !this.curChart || !(this.curChart && this.xDimArr.length)
    }
  },
  watch: {
    // form: {
    //   deep: true,
    //   handler(nn) {
    //     // console.log(nn, 'nnnnnnnnnnnnnnnnnnnn')
    //   }
    // },
    'form.xDimKey': {
      handler(newval) {
        this.xDimKeyLabel = (this.xDimArr.find(item => item.name === newval))?.desc
      }
    },
    'form.legendDimKey': {
      handler(newval) {
        this.legendDimKeyLabel = (this.legendDimArr.find(item => item.name === newval))?.desc
      }
    },
    'form.indicatorKey': {
      handler(newval) {
        this.indicatorKeyLabel = (this.indicatorDimArr.find(item => item.name === newval))?.desc
      }
    },
    'form.filterDimKey': {
      handler(newval) {
        this.filterDimKeyLabel = (this.filterDimArr.find(item => item.name === newval))?.desc
      }
    },
    listLoading: {
      immediate: true,
      handler(newval) {
        if (newval) {
          this.loadingDOM = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.8)'
          })
        } else {
          this.loadingDOM && this.loadingDOM.close()
        }
      }
    },
    /**
     * 监听接口获取的 当前画布上需要渲染的组件列表值的更改
     * @param newVal
     * @param oldVal
     */
    // rtWidgets: {
    //   immediate: true,
    //   handler(newVal) {

    //   }
    // },
    widgetList(newVal, oldVal) {
      newVal && newVal.forEach(item => {
        const { configJson } = item
        if (configJson && configJson.chartType) {
          if (!this.chartList.length) {
            this.chartList = this.initChartList()// 首页是初始进入页面就带数据
          }
          const chatIndex = this.chartList.findIndex(c => {
            return c.x === configJson.x && c.y === configJson.y
          })
          if (chatIndex > -1) {
            const form = {}
            const configJson = item.configJson
            if (configJson.x && configJson.y) {
              form.x = configJson.x
              form.title = configJson.title
              form.y = configJson.y
              form.w = configJson.w
              form.h = configJson.h
              form.height = configJson.h
              form.width = configJson.w
              form.tableId = configJson.viewId
              form.viewName = configJson.viewName
              form.viewId = configJson.viewId
              form.viewScript = configJson.viewScript
              form.dataColumns = configJson.dataColumns[0] || {} // 指标的相关配置，排序，聚合等
              // form.legendColumns = configJson.legendColumns[0] || {} // 图例的相关配置，排序，聚合等
              form.xDimKey = configJson.dimColumns[0] ? configJson.dimColumns[0].name : ''
              form.filterDimKey = configJson.filterColumns && configJson.filterColumns.length ? configJson.filterColumns.filter(f => f.name).map(i => i.name) : ''
              form.legendDimKey = configJson.legendColumns[0] ? configJson.legendColumns[0].name : ''
              form.indicatorKey = configJson.dataColumns[0] ? configJson.dataColumns[0].name : ''
            }
            this.chartList.splice(chatIndex, 1, { ...this.chartList[chatIndex], ...item, form, ...configJson })
            if (this.activeIndexArr[0] === 0) {
              // 当前没有选中的默认选中第一个
              this.handleUpdateView({ tableId: form.tableId, x: form.x, y: form.y, chart: null, w: form.width, h: form.height })
            } else {
              this.handleUpdateChart({ tableId: form.tableId, form, x: form.x, y: form.y, chart: null, w: form.width, h: form.height })
            }
          }
        }
      })

      // 处理哪些是变更了宽高的需要隐藏或者释放其相邻元素
      this.chartList.forEach(item => {
        if (item.form && item.form.width && item.form.height) {
          if (item.form.width > 1) {
            this.changeChartShow(item.form.width, 'width', item)
          }
          if (item.form.height > 1) {
            this.changeChartShow(item.form.height, 'height', item)
          }
        }
      })
    },
    curTableDetail: {
      handler(newVal, oldVal) {
        this.queryParams.dashboardId = this.curTableDetail.id
        this.dashboardName = this.curTableDetail.name
        // this.queryParams.name = this.curTableDetail.name
      },
      immediate: true,
      deep: true
    },
    chartList(newVal) {
      this.curChart = this.commonGetCurChart(this.activeIndexArr) || {}
      // console.log(newVal, 'newValnewValnewVal', this.form, this.activeIndexArr)
    },
    // 设计还是预览模式更改后需要重置echarts图大小
    modelType() {
      this.autoSize = !this.autoSize
    },
    // echarts图大小重置后需要处理流式布局自适应
    autoSize() {
      this.handleAutoSize()
    }
  },
  created() {
    if (this.isRt) {
      this.modelType = 'view'
    }
    this.chartList = this.initChartList()
    // 获取数据源类型
    this.sourceTypeArr = [{ code: 'MEMORY', name: '二维表' }]
    getAggregationType().then(res => {
      if (res.code === 200) {
        this.aggregationType = res.data
      }
    })
    get2dTableList().then(res => {
      if (res.code === 200) {
        this.table2dData = this.removeLeafChild(res.data.memView)
        if (this.isRt) {
          this.widgetList = this.rtWidgets
          return
        }
        this.getWidget()
      }
    })
    this.queryChartParams = this.initialChartParams
  },
  methods: {
    handleAutoSize() {
      this.hiddenPic = true
      // 自适应完成后放开超出可见
      setTimeout(() => {
        this.hiddenPic = false
      }, 1000)
    },
    handleChangeHasConfigChange(flag, from) {
      // console.log(from, '那里修改了hasConfigChange')
      this.hasConfigChange = flag
    },
    /**
     * 切换到视图模式
     */
    handleToViewModel() {
      this.modelType = this.modelType === 'edit' ? 'view' : 'edit'
    },
    /**
     * 更新各个卡片的属性数据视图等信息
     * @param param0
     */
    handleUpdateChart({ x, y, form, chart = null, tableId, w, h }) {
      let node = {}
      this.table2dData.find(item => {
        if (item.id == tableId) {
          node = item
          return true
        }
        return item.children?.find(child => {
          if (child.id == tableId) {
            node = child
            return true
          }
          return false
        })
      })
      if (node.id) {
        const targetSelectNode = this.select2dTable(node, '', 'setChart')
        targetSelectNode.form = form
        this.cardClick({ x, y, chart, w, h, targetSelectNode, customType: 'setChart' }, 'force')
      }
    },
    /**
     * 通过form信息更新组件展示
     * @param param0
     */
    handleUpdateView({ x, y, chart = null, tableId, w, h }) {
      let node = {}
      this.table2dData.find(item => {
        if (item.id == tableId) {
          node = item
          return true
        }
        return item.children?.find(child => {
          if (child.id == tableId) {
            node = child
            return true
          }
          return false
        })
      })
      if (node.id) {
        this.select2dTable(node, '', 'init')
        this.activeIndexArr = [x, y]
        this.cardClick({ x, y, chart, w, h }, 'force')
      }
    },
    // 去掉叶子结点的children属性
    removeLeafChild(tree) {
      for (const item of tree) {
        if (item.children) {
          item.children = this.removeLeafChild(item.children)
        } else {
          delete item.children
        }
      }
      return tree
    },
    // vuetreeselect 格式化
    normalizer(node) {
      return {
        id: node.id,
        label: node.tableName,
        children: node.children
      }
    },
    /**
     * 属性配置--标题修改
     * @param value
     */
    titleInput(value) {
      this.$set(this.form, 'title', value)
      const flag = this.chartList.find(item => item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1])
      if (flag) {
        flag.title = value
        this.handleChangeHasConfigChange(true, 'titleInput')
      }
    },
    /**
     * 属性配置--选择数据源方法,只修改当前的，不修改存库的数据，只有保存后才落库，某则切换视图后还原成库里的
     * @param node
     * @param instanceId
     */
    select2dTable(node, instanceId, type) {
      const { id, metadata, viewScript, tableName } = node
      const target = type === 'setChart' ? { form: {}, queryChartParams: {}} : this
      // 清除以往维度数据
      if (type === 'setChart') {
        target.form.xDimKey = ''
        target.form.legendDimKey = ''
        target.form.indicatorKey = ''
        target.form.filterDimKey = ''
      } else {
        this.$set(this.form, 'xDimKey', '')
        this.$set(this.form, 'legendDimKey', '')
        this.$set(this.form, 'indicatorKey', '')
        this.$set(this.form, 'filterDimKey', '')
        if (!type) {
          this.handleChangeHasConfigChange(true, 'select2dTable')
        }
      }

      // 横轴维度type="VARCHAR" 图例维度type！="VARCHAR"
      const [xDimArr, legendDimArr, indicatorDimArr, filterDimArr] = [[], [], [], []]
      metadata && metadata.forEach(item => {
        if (item.type === 'VARCHAR') {
          xDimArr.push(item)
          legendDimArr.push(item)
        } else {
          indicatorDimArr.push(item)
        }
        filterDimArr.push(item)
      })
      target.xDimArr = xDimArr
      target.filterDimArr = filterDimArr
      target.legendDimArr = legendDimArr
      target.indicatorDimArr = indicatorDimArr
      target.form.tableId = id
      target.form.viewName = tableName
      target.form.viewId = id
      target.form.viewScript = viewScript
      // target.queryChartParams.viewId = id
      // target.queryChartParams.viewScript = viewScript
      return target
    },
    /**
     * 属性配置--横轴维度的下拉选择更改事件
     * @param key
     */
    changeXDim(key) {
      this.handleChangeHasConfigChange(true, 'changeXDim')
      this.$set(this.form, 'xDimKey', key)
      this.previewChartData({})
    },
    /**
     * 属性配置--图例修改
     * @param key
     */
    changeLegendDim(key) {
      this.handleChangeHasConfigChange(true, 'changeLegendDim')
      this.$set(this.form, 'legendDimKey', key)
      this.previewChartData({})
    },
    /**
     * 属性配置--指标维修改
     * @param key
     */
    changeIndicatorDim(key) {
      this.handleChangeHasConfigChange(true, 'changeIndicatorDim')
      this.$set(this.form, 'indicatorKey', key)
      this.previewChartData({})
    },
    /**
     * 所有更改queryChartParams的操作都调用这个方法，这个queryChartParams是用来请求预览接口的参数
     */
    changeQueryChartParams() {

    },
    commonGetCurChart(activeIndexArr) {
      return this.chartList.find(item => item.x === activeIndexArr[0] && item.y === activeIndexArr[1])
    },
    /**
     * 维度值更改后触发的预览事件
     */
    previewChartData({ customType, targetSelectNode }) {
      const target = customType === 'setChart' ? targetSelectNode : this
      const { tableId, xDimKey, filterDimKey, legendDimKey, viewId, viewScript, indicatorKey } = target.form
      const targetChart = deepClone(this.commonGetCurChart(target.activeIndexArr))
      const { filterColumns, dimColumns = [], dataColumns = [], legendColumns = [] } = targetChart
      if (!tableId || !xDimKey || (!legendDimKey && (!target.curChart || target.curChart.chartType !== 'pie')) || !indicatorKey) return
      const xDimInfo = target.xDimArr.find(item => item.name === xDimKey)
      const legendInfo = target.legendDimArr.find(item => item.name === legendDimKey)
      const indicatorInfo = target.indicatorDimArr.find(item => item.name === indicatorKey)

      const dimColumns2 = xDimInfo ? [{ ...dimColumns[0], ...xDimInfo }] : []
      const tQueryChartParams = deepClone(target.queryChartParams)
      tQueryChartParams.dimColumns = dimColumns2

      const filterColumns2 = filterColumns ? [...filterColumns] : []
      tQueryChartParams.filterColumns = filterColumns2

      tQueryChartParams.legendColumns = legendInfo ? [{ ...legendColumns[0], ...legendInfo }] : []
      tQueryChartParams.dataColumns = indicatorInfo ? [{ ...dataColumns[0], ...indicatorInfo }] : []

      // 处理饼图展示的图例维度其实是横轴维度传给后端的
      // if (target.curChart && target.curChart.chartType === 'pie') {
      //   tQueryChartParams.dimColumns = legendInfo ? [legendInfo] : tQueryChartParams.dimColumns
      //   tQueryChartParams.legendColumns = []
      // }

      tQueryChartParams.viewScript = viewScript
      tQueryChartParams.viewId = viewId

      target.queryChartParams = tQueryChartParams

      getPreviewData({ ...target.queryChartParams }).then(res => {
        if (!res.data) return this.$message.error('暂无数据')
        this.chartList.find((item, ind) => {
          if ((item.id && item.id == target.curChart.id) || (item.x == target.form.x && item.y == target.form.y)) {
            // const newItem = Object.assign({}, { ...item, chart: res.data.rows ?? [] })
            const chart = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
            const newItem = Object.assign({}, { ...item, chart: chart, ...target.queryChartParams,
              dimColumns: dimColumns2, filterColumns: filterColumns2,
              queryChartParams: { ...target.queryChartParams, totalPage: res.data.totalPage, totalRows: res.data.totalRows } // 记录当前这个图数据请求完了没
            })
            this.chartList.splice(ind, 1, newItem)
            return true
          }
        })
      })
    },
    // 获取当前视图的组件数据
    getWidget() {
      this.listLoading = true
      getWidget(this.queryParams).then(res => {
        if (res.code === 200) {
          this.widgetList = res.data || []
        }
      }).finally(() => {
        setTimeout(() => {
          this.listLoading = false
        }, 700)
      })
    },
    /**
     * 删除格子里组件
     * @param id
     */
    delWidget(id, chart) {
      const delTxt = '确定删除该组件吗?'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!id) {
          // 没有保存过数据落库的直接本地清除相关组件
          const hasIndex = this.widgetList.findIndex(item => item.x === chart.x && item.y === chart.y)
          const hasIndex2 = this.chartList.findIndex(item => item.x === chart.x && item.y === chart.y)
          const initCart = { x: chart.x, y: chart.y, chart: [], w: 1, h: 1, show: true, chartType: null }
          if (hasIndex2 > -1) {
            this.chartList.splice(hasIndex2, 1, initCart)
          }
          if (hasIndex > -1) {
            this.widgetList.splice(hasIndex, 1)
          }
          if (this.activeIndexArr[0] == chart.x && this.activeIndexArr[1] == chart.y) {
            // 初始化当前选中卡片的一些状态值
            this.cardClick(initCart, 'force')
          }
          return
        }
        delWidget(id).then(res => {
          if (res.code === 200) {
            const hasIndex2 = this.chartList.findIndex(item => item.x === chart.x && item.y === chart.y)
            const initCart = { x: this.chartList[hasIndex2].x, y: this.chartList[hasIndex2].y, chart: [], w: 1, h: 1, show: true, chartType: null }
            if (hasIndex2 > -1) {
              this.chartList.splice(hasIndex2, 1, initCart)
            }
            if (this.activeIndexArr[0] == this.chartList[hasIndex2].x && this.activeIndexArr[1] == this.chartList[hasIndex2].y) {
            // 初始化当前选中卡片的一些状态值
              this.cardClick(initCart, 'force')
            }
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getWidget()
          }
        })
      }).catch(error => {
        // 取消
        return error
      })
    },
    /**
     * 保存或者新增组件配置
     */
    handleSaveOrUpdateChartList() {
      const curChart = this.chartList.find(item => item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1])
      if (curChart) {
        curChart.form = { ...this.form }
        const { dashboardId } = this.queryParams
        // const { dashboardId, name } = this.queryParams
        saveOrUpdateWidget({
          configJson: { ...curChart, title: this.form.title || '' },
          // configJson: { ...curChart, ...this.queryChartParams, title: this.form.title || '', filterColumns: curChart.filterColumns },
          dashboardId,
          name: this.dashboardName,
          id: curChart.id, // 当前编辑的是哪个组件的id
          seq: 1,
          viewId: this.form.tableId, // 当前选中的数据源id
          viewName: this.form.viewName // 当前选中的数据源的表名
        }).then(res => {
          if (res) {
            Object.assign(curChart, { ...this.form })
            this.$message({
              type: 'success',
              message: '保存成功!'
            })
          }
        }).finally(() => {
          const curChartIndex = this.chartList.findIndex(item => item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1])
          if (curChartIndex > -1) {
            this.chartList.splice(curChartIndex, 1, { ...this.chartList[curChartIndex], form: { ...this.form }})
          }
        }).catch(() => {
          this.$message({
            type: 'error',
            message: '保存失败!'
          })
        })
      }
    },
    /**
     * 初始化画布格子
     */
    initChartList() {
      const list = []
      for (let i = 1; i < 4; i++) {
        for (let j = 1; j < 4; j++) {
          // list.push({ x: i, y: j, chart: [], w: 1, h: 1, show: true, chartType: null, title: '' })
          list.push({
            chart: [],
            'w': 1,
            'h': 1,
            'x': i,
            'y': j,
            show: true,
            'chartType': null,
            'code': '',
            'dataColumns': [
              {
                'aggregation': '',
                'desc': '',
                'name': '',
                'orderBy': '',
                'queryType': '',
                'queryValue': '',
                'type': ''
              }
            ],
            'dimColumns': [
              {
                'aggregation': '',
                'desc': '',
                'name': '',
                'orderBy': '',
                'queryType': '',
                'queryValue': '',
                'type': ''
              }
            ],
            'filterColumns': [
              {
                'aggregation': '',
                'desc': '',
                'name': '',
                'orderBy': '',
                'queryType': '',
                'queryValue': '',
                'type': ''
              }
            ],
            'legendColumns': [
              {
                'aggregation': '',
                'desc': '',
                'name': '',
                'orderBy': '',
                'queryType': '',
                'queryValue': '',
                'type': ''
              }
            ],
            'page': {
              'currentPage': 0,
              'pageSize': 0
            },
            'sourceType': '',
            'title': '',
            'viewId': '',
            'viewName': '',
            'viewScript': ''
          })
        }
      }
      return list
    },
    /**
     * 卡片组件点击后事件，处理清空当前选中的数据源相关内容，初始化为当前选中的卡片的数据源等
     * @param param0
     */
    async cardClick({ x, y, chart, w, h, customType, targetSelectNode }, type) {
      if (type !== 'force' && !customType && this.activeIndexArr[0] == x && this.activeIndexArr[1] == y) {
        return
      }
      // 切换卡片需要给用户提示是否要保存当前卡片修改的内容，不保存的话会丢失前面配置的内容
      // if (!customType && type !== 'force') {
      //   if (this.hasConfigChange) {
      //     const confirmResult = await this.$confirm('是否需要保存当前卡片修改内容，不保存会取消前面配置内容', '提示', {
      //       confirmButtonText: '确定',
      //       cancelButtonText: '取消',
      //       type: 'warning'
      //     }).catch(() => {
      //       return false
      //     })
      //     if (confirmResult) {
      //       // 需要保存，去调用保存接口
      //     } else {
      //       // 不需要保存直接重置有更改的状态为无更改
      //       this.handleChangeHasConfigChange(false, 'cardClick')
      //     }
      //   }
      // }
      const target = customType === 'setChart' ? targetSelectNode : this
      target.queryChartParams = this.initialChartParams

      target.activeIndexArr = [x, y]
      target.curChart = this.chartList.find(item => item.x === x && item.y === y)
      target.form = {
        x,
        y,
        height: h,
        width: w,
        ...target.curChart.form
      }
      target.widthKey = Math.random()
      target.heightKey = Math.random()

      this.previewChartData({ targetSelectNode, customType })
    },
    changeChartShow(newVal, sign, customTarget) {
      const target = customTarget || this
      const { x, y, width, height } = target.form
      const startIndex = sign === 'width' ? y : x
      const oldVal = customTarget ? 1 : (sign === 'width' ? width : height)
      const positionSign = sign === 'width' ? 'y' : 'x'
      const fixedSign = sign === 'width' ? 'x' : 'y'
      const fixedVal = sign === 'width' ? height : width // 当前操作项的宽或者高
      const fixedStartIndex = sign === 'width' ? x : y // 当前操作项的x轴或者Y轴
      if (newVal > oldVal) { // 值变大了需要把其相邻的隐藏
        const finalCharts = JSON.parse(JSON.stringify(this.chartList))
        for (let i = fixedStartIndex; i < fixedVal + fixedStartIndex; i++) {
          const flag = finalCharts.find(item => item[positionSign] === newVal + startIndex - 1 && item[fixedSign] === i)
          if (flag && flag.show && flag.w === 1 && flag.h === 1) {
            flag.show = false
          } else {
            return false
          }
        }
        this.chartList = finalCharts
        return true
      } else { // 值变大了需要把其相邻的释放出来
        for (let i = fixedStartIndex; i < fixedVal + fixedStartIndex; i++) {
          const flag = this.chartList.find(item => item[positionSign] === oldVal + startIndex - 1 && item[fixedSign] === i)
          flag.show = true
        }
        return true
      }
    },
    /**
     * 属性配置--宽度属性变化
     * @param value
     */
    widthChange(value) {
      const { x, y } = this.form
      const check = this.changeChartShow(value, 'width')
      if (!check) {
        this.widthKey = Math.random()
        return this.$message.warning('周围已没有空间扩大')
      }
      this.form.width = value
      const flag = this.chartList.find(item => item.x === x && item.y === y)
      if (flag) {
        this.handleChangeHasConfigChange(true, 'widthChange')
        Object.assign(flag, { w: value })
      }
    },
    /**
     * 属性配置--高度修改
     * @param value
     */
    heightChange(value) {
      const { x, y } = this.form
      const check = this.changeChartShow(value, 'height')
      if (!check) {
        this.heightKey = Math.random()
        return this.$message.warning('周围已没有空间扩大')
      }
      this.form.height = value
      const flag = this.chartList.find(item => item.x === x && item.y === y)
      if (flag) {
        this.handleChangeHasConfigChange(true, 'heightChange')
        Object.assign(flag, { h: value })
      }
    },
    /**
     * 属性配置--筛选维更改
     * @param value
     */
    changeFilterDim(value, ...params) {
      this.$set(this.form, 'filterDimKey', value)
      // 找到需要修改的卡片组件数据
      let flagIndex = -1
      const flag = this.chartList.find((item, index) => {
        if (item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1]) {
          flagIndex = index
          return true
        }
        return false
      })
      let filterColumns = flag.filterColumns || []
      const result = []
      if (value && value.length) {
        // 处理选了维度后修奥有一个更详细的包含被选择维度的名称和值等信息
        value.forEach(item => {
          const dim = filterColumns.find(f => {
            f.name === item
          })
          if (dim) {
            result.push(dim)
          } else {
            const d = this.filterDimArr.find(d => d.name === item)
            result.push(d)
          }
        })
        filterColumns = result
      } else {
        filterColumns = []
      }
      this.handleChangeHasConfigChange(true, 'changeFilterDim')
      this.chartList.splice(flagIndex, 1, { ...flag, form: { ...flag.form, filterDimKey: value }, filterColumns })
      // this.previewChartData({}) //筛选的不需要调接口，需要设置好了筛选的值才调接口
    },
    /**
     * 打开筛选的值设置弹框
     * @param id
     * @param chart
     */
    filterWidget(id, chart) {
      // console.log(chart, this.chartList, this.queryChartParams, 'ccccccccc')
      this.dimShowFilterDialog = true
      const flag = this.commonGetCurChart([chart.x, chart.y])
      this.currentModelDimShowChart = chart
      // 赋值弹框展示当前选中项的过滤数组，且必需有name属性展示的
      this.currentModelDimShowFilterDialog = deepClone(flag.filterColumns || []).filter(item => item.name)
    },
    /**
     * 渲染栅栏
     */
    renderChartTable() {
      const chartPic = this.chartList.map(item => {
        if (item.show) {
          const className = {
            'chart-pic-wrap': true,
            'hidden-over': this.hiddenPic,
            'chart-view-model-wrap': this.modelType === 'view',
            'chart-pic-wrap-active': item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1]
          }
          return (
            !item.chart || !item.chart.length || (item.chart.length && item.chart[0] && !item.chart[0].dataErrorTip)
              ? <div class={className} style={`grid-column-end:span ${item.w};grid-row-end:span ${item.h}`} onClick={e => this.cardClick(item)}>
                <div class='operateBtn' onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}>
                  {/* 删除按钮 */}
                  {item.chartType && this.modelType === 'edit' ? <el-button type='danger' onClick={() => this.delWidget(item.id, item)} class='deleteBtn' icon='el-icon-delete' circle></el-button> : null}
                  {/* 过滤图标 */}
                  {item.chartType && this.modelType === 'edit' && item.form && item.form.filterDimKey && item.form.filterDimKey.length
                    ? <el-button type='primary' onClick={() => this.filterWidget(item.id, item)} class='filterBtn' icon='el-icon-s-tools' circle></el-button>
                    : null}
                  {/* 全屏展示图标 */}
                  {item.chartType && item.chart && item.chart.length ? <el-button type='primary' onClick={() => this.handleMaxChart(item)} class='fullScreenBtn' icon='el-icon-full-screen' circle></el-button> : null}
                </div>
                {/* 图表展示 */}
                {
                  (!item.chartType || this.listLoading) && this.modelType === 'edit'
                    ? <div class='add-icon-wrap' onClick={() => { this.dialogVisible = true; return true }}>{item.x + ',' + item.y}<i class='el-icon-plus add-icon'></i></div>
                    : item.chartType === 'bar' ? <BarChart autoSize={this.autoSize} w={item.w} h={item.h} chartData={item.chart} title={item.title}/>
                      : item.chartType === 'line' ? <LineChart autoSize={this.autoSize} w={item.w} h={item.h} chartData={item.chart} title={item.title} />
                        : item.chartType === 'pie' ? <PieChart autoSize={this.autoSize} w={item.w} h={item.h} chartData={item.chart} title={item.title}/> : null
                }
              </div>

              : <div class={className} style={`grid-column-end:span ${item.w};grid-row-end:span ${item.h}`} onClick={e => this.cardClick(item)}>
                <div class='operateBtn' onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}>
                  {/* 删除按钮 */}
                  {item.chartType && this.modelType === 'edit' ? <el-button type='danger' onClick={() => this.delWidget(item.id, item)} class='deleteBtn' icon='el-icon-delete' circle></el-button> : null}
                  {/* 过滤图标 */}
                  {item.chartType && this.modelType === 'edit' && item.form && item.form.filterDimKey
                    ? <el-button type='primary' onClick={() => this.filterWidget(item.id, item)} class='filterBtn' icon='el-icon-s-tools' circle></el-button>
                    : null}
                  {/* 全屏展示图标-异常图表不需要展示全屏 */}
                  {/* {item.chartType && item.chart && item.chart.length ? <el-button type='primary' onClick={() => this.handleMaxChart(item)} class='fullScreenBtn' icon='el-icon-full-screen' circle></el-button> : null} */}
                </div>
                {item.chart[0]
                  ? <span class='block_middle'>{item.chart[0].dataErrorTip}</span>
                  : null}

              </div>
          )
        }
      })
      return (
        <div class={{ 'grid-wrap': true, 'hidden-over': this.hiddenPic }}>
          {chartPic}
        </div>
      )
    },
    /**
     * 设置不同维度的额外属性 排序，聚合等
     * @param index
     * @param indexPath
     * @param v
     * @param type 不同的维度类型 dimColumns dataColumns
     */
    handleClickDimAttr(index, indexPath, v, type) {
      // console.log(index, indexPath, v, type, this.form, 'index, indexPath, type')
      const indexPath2 = indexPath[2]
      const curChart = this.commonGetCurChart(this.activeIndexArr)
      curChart[type] = curChart[type] || [{}]
      const targetColumns = curChart[type][0]
      if (index === 'count') {
        this.dimShowCountDialog = true
      } else if (indexPath[1] === 'aggregation') {
        // 聚合属性
        if (indexPath2 && targetColumns.aggregation !== indexPath2) {
          targetColumns.aggregation = indexPath2
          this.previewChartData({})
          this.handleChangeHasConfigChange(true, 'handleClickDimAttr')
        }
      } else if (indexPath[1] === 'orderBy') {
        // 排序属性
        if (indexPath2 && targetColumns.orderBy !== indexPath2) {
          targetColumns.orderBy = indexPath2
          this.previewChartData({})
          this.handleChangeHasConfigChange(true, 'handleClickDimAttr')
        }
      }
    },
    /**
     * 渲染属性面板
     */
    renderChartProps() {
      const widthMax = 3 - this.form.x + 1
      const heightMax = 3 - this.form.y + 1
      const warpClassName = {
        'chart-props-wrap': true,
        'is-fold': this.isFoldConfigForm
      }
      return (
        this.isRt ? null
          : this.modelType === 'edit'
            ? <div class={warpClassName}>
              <div class='prop-header'>
                {/* 折叠属性配置操作按钮 */}
                <el-tooltip effect='dark' content={this.isFoldConfigForm ? '展开属性面板' : '折叠属性面板'} placement='top'>
                  <el-button onClick={() => { this.isFoldConfigForm = !this.isFoldConfigForm; this.autoSize = !this.autoSize }} class='fold-btn' type='primary' icon={this.isFoldConfigForm ? 'el-icon-s-unfold' : 'el-icon-s-fold'}></el-button>
                </el-tooltip>
                <el-button class='save-btn' disabled={this.chartDisabled} type='primary' onClick={this.handleSaveOrUpdateChartList}>保存</el-button>
                <div class='switch-mode-btn' onClick={this.handleToViewModel}>
                  {this.modelType === 'edit' ? '切至视图模式' : '切至编辑模式'}
                </div>
              </div>
              <div class='prop-content'>
                <div class='prop-label'>图表标题</div>
                <div class='prop-value'>
                  <el-input size='small' value={this.form.title} onInput={this.titleInput}></el-input>
                </div>
                <div class='prop-label'>数据源</div>
                <div class='prop-value'>
                  <el-select style='width:100%' size='small' placeholder='选数据源类型' clearable={false} value={this.form.sourceType} disabled>
                    {this.sourceTypeArr.map(item => {
                      return (
                        <el-option
                          key={item.code}
                          label={item.name}
                          value={item.code}
                        />)
                    })}
                  </el-select>
                  <treeselect value={this.form.tableId} size='small' clearable={false} normalizer={this.normalizer} options={this.table2dData} placeholder='选二维表格' onSelect={this.select2dTable} />
                </div>
                <div class='prop-label'>筛选维度</div>
                <div class='prop-value prop-filter-wrap'>
                  <el-select multiple placeholder='请选择' value={this.form.filterDimKey} onChange={this.changeFilterDim}>
                    {this.filterDimArr.map((item) => {
                      return (
                        <el-option key={item.name} value={item.name} label={item.desc}></el-option>
                      )
                    })}
                  </el-select>

                </div>
                {/* 饼图界面展示的是图例维度这个名字，但是实际传到后端的是横轴维度，这里只处理名字不一样，别的逻辑不变 */}
                <div>
                  <div class='prop-label'>{this.curChart.chartType !== 'pie' ? '横轴维度' : '图例维度'}</div>
                  <div class='prop-value'>
                    <el-select placeholder='请选择' value={this.form.xDimKey} onChange={this.changeXDim}>
                      {/* 处理维度选择后去继续选择排序等相关设置 */}
                      {
                        this.xDimKeyLabel
                          ? <div class='select-prefix' slot='prefix'>
                            <el-menu onSelect={(...params) => { this.handleClickDimAttr(...params, 'dimColumns') }} defaultActive='' menu-trigger='hover' class='el-menu-demo' mode='horizontal'>
                              <el-submenu popper-append-to-body={true} index='2'>
                                <template slot='title'>
                                  <div onClick={(e) => {
                                    e.preventDefault()
                                    e.stopPropagation()
                                  }} >
                                    {this.xDimKeyLabel}
                                  </div>

                                </template>
                                {/* <el-menu-item index='count'>最大展示数据条数</el-menu-item> */}
                                <el-submenu index='orderBy'>
                                  <template slot='title'>排序</template>
                                  {
                                    orderByList.map(o => {
                                      return (
                                        <el-menu-item index={o.code}>
                                          {/* 当前是否是选中状态 */}
                                          {
                                            this.curChart.dimColumns && this.curChart.dimColumns[0] && this.curChart.dimColumns[0].orderBy === o.code
                                              ? <span class='el-icon-check'></span>
                                              : null
                                          }
                                          {o.value}
                                        </el-menu-item>
                                      )
                                    })
                                  }

                                  {/* <el-menu-item index='ASC'>升序排序</el-menu-item>
                                <el-menu-item index='DESC'>降序排序</el-menu-item> */}
                                </el-submenu>
                              </el-submenu>
                            </el-menu>
                          </div>
                          : null
                      }
                      {this.xDimArr.map((item) => {
                        return (
                          <el-option key={item.name} value={item.name} label={item.desc}></el-option>
                        )
                      })}
                    </el-select>
                  </div>
                </div>

                {/* 图例维度在饼图不需要 */}
                {
                  !this.curChart || this.curChart.chartType !== 'pie'
                    ? <div>
                      <div class='prop-label'>图例维度</div>
                      <div class='prop-value'>
                        <el-select placeholder='请选择' value={this.form.legendDimKey} onChange={this.changeLegendDim}>
                          {/* 处理维度选择后去继续选择排序等相关设置 */}
                          {
                            this.legendDimKeyLabel
                              ? <div class='select-prefix' slot='prefix'>
                                <el-menu onSelect={(...params) => { this.handleClickDimAttr(...params, 'legendColumns') }} defaultActive='' menu-trigger='hover' class='el-menu-demo' mode='horizontal'>
                                  <el-submenu popper-append-to-body={true} index='2'>
                                    <template slot='title'>
                                      <div onClick={(e) => {
                                        e.preventDefault()
                                        e.stopPropagation()
                                      }} >
                                        {this.legendDimKeyLabel}
                                      </div>

                                    </template>
                                    {/* <el-menu-item index='count'>最大展示数据条数</el-menu-item> */}
                                    <el-submenu index='orderBy'>
                                      <template slot='title'>排序</template>
                                      {
                                        orderByList.map(o => {
                                          return (
                                            <el-menu-item index={o.code}>
                                              {/* 当前是否是选中状态 */}
                                              {/* {console.log(this.curChart, this.curChart.legendColumns, o.code, 'kkkkkkkkkkkk')} */}
                                              {
                                                this.curChart.legendColumns && this.curChart.legendColumns[0] && this.curChart.legendColumns[0].orderBy === o.code
                                                  ? <span class='el-icon-check'></span>
                                                  : null
                                              }
                                              {o.value}
                                            </el-menu-item>
                                          )
                                        })
                                      }
                                      {/* <el-menu-item index='default'>默认排序</el-menu-item>
                                    <el-menu-item index='ASC'>升序排序</el-menu-item>
                                    <el-menu-item index='DESC'>降序排序</el-menu-item> */}
                                    </el-submenu>
                                  </el-submenu>
                                </el-menu>
                              </div>
                              : null
                          }
                          {this.legendDimArr.map((item) => {
                            return (
                              <el-option key={item.name} value={item.name} label={item.desc}></el-option>
                            )
                          })}
                        </el-select>
                      </div>
                    </div>
                    : null
                }
                <div>
                  <div class='prop-label'>指标</div>
                  <div class='prop-value'>
                    <el-select placeholder='请选择' value={this.form.indicatorKey} onChange={this.changeIndicatorDim}>
                      {/* 处理维度选择后去继续选择排序等相关设置 */}
                      {
                        this.indicatorKeyLabel
                          ? <div class='select-prefix' slot='prefix'>
                            <el-menu onSelect={(...params) => { this.handleClickDimAttr(...params, 'dataColumns') }} defaultActive='' menu-trigger='hover' class='el-menu-demo' mode='horizontal'>
                              <el-submenu popper-append-to-body index='2'>
                                <template slot='title'>
                                  <div onClick={(e) => {
                                    e.preventDefault()
                                    e.stopPropagation()
                                  }} >
                                    {this.indicatorKeyLabel}
                                  </div>

                                </template>
                                {/* <el-menu-item index='count'>最大展示数据条数</el-menu-item> */}
                                <el-submenu index='aggregation'>
                                  <template slot='title'>聚合</template>
                                  {
                                    this.aggregationType.map(item => {
                                      return (
                                        <el-menu-item key={item.code} index={item.code}>
                                          {/* 当前是否是选中状态 */}
                                          {
                                            this.curChart.dataColumns && this.curChart.dataColumns[0] && this.curChart.dataColumns[0].aggregation === item.code
                                              ? <span class='el-icon-check'></span>
                                              : null
                                          }

                                          {item.name}
                                        </el-menu-item>
                                      )
                                    })
                                  }
                                </el-submenu>
                                <el-submenu index='orderBy'>
                                  <template slot='title'>排序</template>
                                  {
                                    orderByList.map(o => {
                                      return (
                                        <el-menu-item index={o.code}>
                                          {/* 当前是否是选中状态 */}
                                          {
                                            this.curChart.dataColumns && this.curChart.dataColumns[0] && this.curChart.dataColumns[0].orderBy === o.code
                                              ? <span class='el-icon-check'></span>
                                              : null
                                          }
                                          {o.value}
                                        </el-menu-item>
                                      )
                                    })
                                  }
                                  {/* <el-menu-item index='default'>默认排序</el-menu-item>
                                <el-menu-item index='ASC'>升序排序</el-menu-item>
                                <el-menu-item index='DESC'>降序排序</el-menu-item> */}
                                </el-submenu>
                              </el-submenu>
                            </el-menu>
                          </div>
                          : null
                      }
                      {this.indicatorDimArr.map((item) => {
                        return (
                          <el-option key={item.name} value={item.name} label={item.desc}></el-option>
                        )
                      })}
                    </el-select>
                  </div>

                </div>
                <div class='prop-label'>权限维度</div>
                <div class='prop-value'>
                  <el-input size='small'></el-input>
                </div>
                <div class='prop-label'>尺寸</div>
                <div class='prop-value'>
            宽：
                <el-input-number style='marginRight:4px' size='small' key={this.widthKey ? this.widthKey : 'widthKey'} class='size-change-input' value={this.form.width} onChange={this.widthChange} min={1} max={heightMax}></el-input-number>
            高：
                <el-input-number size='small' key={this.heightKey ? this.heightKey : 'heightKey'} class='size-change-input' value={this.form.height} onChange={this.heightChange} min={1} max={widthMax}></el-input-number>
                </div>
              </div>
            </div>
            : <div class='chart-props-wrap chart-props-view'>
              <div class='prop-header'>
                <div class='switch-mode-btn' onClick={this.handleToViewModel}>
                      切至编辑模式
                </div>
              </div>
            </div>
      )
    },
    handleClose() {
      this.dialogVisible = false
      this.radioKey = undefined
    },
    handleOk() {
      const flag = this.chartList.find(item => item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1])
      flag.chartType = this.radioKey
      this.handleClose()
    },
    handleDimShowCount() {

    },
    handleDimShowCountChange() {

    },
    /**
     * 筛选值确定后调用的预览接口
     * @param target
     */
    filterPreviewChartData(target) {
      const { sourceType, page, viewScript, viewId, dimColumns, filterColumns, legendColumns, dataColumns } = target
      getPreviewData({ sourceType, page, viewScript, viewId, dimColumns, filterColumns, legendColumns, dataColumns }).then(res => {
        if (!res.data) return this.$message.error(res.data.msg)
        this.chartList.find((item, ind) => {
          if ((item.id && item.id === target.id) || (item.x == target.x && item.y == target.y)) {
            const chart = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
            const newItem = Object.assign({}, { ...item, chart: chart,
              queryChartParams: { ...target.queryChartParams, filterColumns, totalPage: res.data.totalPage, totalRows: res.data.totalRows } // 记录当前这个图数据请求完了没
            })
            this.chartList.splice(ind, 1, newItem)
            return true
          }
        })
      })
    },
    /**
     * 筛选框点击确认调用预览的接口
     */
    handleDimShowOk() {
      this.dimShowFilterDialog = false
      this.currentModelDimShowChart.filterColumns = this.currentModelDimShowFilterDialog
      this.currentModelDimShowFilterDialog = []
      this.filterPreviewChartData(this.currentModelDimShowChart)
      this.handleChangeHasConfigChange(true, 'handleDimShowOk')
      // 更改完除了要调用预览的接口，还需要调用保存的接口，这里需要修改直接在右侧配置不用弹框
    },
    filterChange(value, index, type) {
      this.$set(this.currentModelDimShowFilterDialog[index], 'queryValue', value || '')
      this.currentModelDimShowFilterDialog[index].queryType = type === 'DECIMAL' ? 0 : -1 // 查询模糊度是精确还是模糊 0：精确 1：模糊
    },
    /**
     * 全屏展示图表
     * @param chart
     */
    async handleMaxChart(chart) {
      const queryChartParams = chart.queryChartParams || {}
      const { page, totalRows } = queryChartParams
      const { pageSize } = page
      const curMaxChart = deepClone(chart)
      if (pageSize < totalRows) {
        // 还有数据没有请求完需要一次请求所有数据
        const newPage = { currentPage: 1, pageSize: totalRows }
        await getPreviewData({ ...queryChartParams, page: newPage }).then(res => {
          if (!res.data) return this.$message.error(res.data.msg)
          const chartData = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
          curMaxChart.chart = chartData
        })
      }
      this.curMaxChart = curMaxChart
      this.maxChartShowDialog = true
    }
  },
  render() {
    const className = {
      'chart-board-wrap': true,
      'chart-rt-model-wrap': this.isRt
    }
    return (
      <div class={className}>
        {this.renderChartTable()}
        {this.renderChartProps()}
        <el-dialog
          title='图表类型选择'
          visible={this.dialogVisible}
          width='30%'
          center
          close-on-click-modal={false}
          before-close={this.handleClose}>
          <el-radio-group value={this.radioKey} onInput={key => this.radioKey = key}>
            <el-radio label='bar'><img class='img-radio' src={barChartPng}></img></el-radio>
            <el-radio label='line'><img class='img-radio' src={lineChartPng}></img></el-radio>
            <el-radio label='pie'><img class='img-radio' src={pieChartPng}></img></el-radio>
          </el-radio-group>
          <span slot='footer' class='dialog-footer'>
            <el-button onClick={this.handleClose}>取 消</el-button>
            <el-button type='primary' onClick={this.handleOk}>确 定</el-button>
          </span>
        </el-dialog>
        {/* 输入需要展示的条数 */}
        <el-dialog
          title='编辑最大展示条数'
          visible={this.dimShowCountDialog}
          onClose={() => { this.dimShowCountDialog = false }}
          width='30%'>
          <el-input placeholder='请输入条数' onChange={this.handleDimShowCountChange}></el-input>
          <span slot='footer' class='dialog-footer'>
            <el-button onClick={() => { this.dimShowCountDialog = false }}>取 消</el-button>
            <el-button type='primary'>确 定</el-button>
          </span>
        </el-dialog>
        {/* 选择筛选维度的值 */}
        <el-dialog
          custom-class='filterCustomDialog'
          title='选择筛选维度的值'
          visible={this.dimShowFilterDialog}
          onClose={() => { this.dimShowFilterDialog = false }}
          width='60%'>
          <el-form ref='form' label-width='80px'>
            {
              this.currentModelDimShowFilterDialog.map((item, index) => {
                return (
                  <el-form-item label={item.desc}>
                    {
                      item.type === 'VARCHAR'
                        ? <el-input value={item.queryValue} onInput={(value) => { this.filterChange(value, index, item.type) }} placeholder='请输入'></el-input>
                        : (item.type === 'DECIMAL'
                          ? <el-input-number value={item.queryValue} onChange={(value) => { this.filterChange(value, index, item.type) }} placeholder='请输入'></el-input-number>
                          : <el-input value={item.queryValue} onInput={(value) => { this.filterChange(value, index, item.type) }} placeholder='请输入'></el-input>)
                    }
                  </el-form-item>
                )
              })
            }
          </el-form>
          <span slot='footer' class='dialog-footer'>
            <el-button onClick={() => { this.dimShowFilterDialog = false }}>取 消</el-button>
            <el-button onClick={this.handleDimShowOk} type='primary'>确 定</el-button>
          </span>
        </el-dialog>
        {/* 全屏展示完整echarts数据 */}
        <el-dialog
          title=''
          custom-class='max-chart-show-dialog'
          visible={this.maxChartShowDialog}
          onClose={() => { this.maxChartShowDialog = false }}
          fullscreen={true}
          height='100%'
          width='100%'>
          {
            this.curMaxChart.chartType === 'bar' ? <BarChart isMax={true} autoSize={this.autoSize} w={this.curMaxChart.w} h={this.curMaxChart.h} chartData={this.curMaxChart.chart} title={this.curMaxChart.title}/>
              : this.curMaxChart.chartType === 'line' ? <LineChart isMax={true} autoSize={this.autoSize} w={this.curMaxChart.w} h={this.curMaxChart.h} chartData={this.curMaxChart.chart} title={this.curMaxChart.title} />
                : this.curMaxChart.chartType === 'pie' ? <PieChart isMax={true} autoSize={this.autoSize} w={this.curMaxChart.w} h={this.curMaxChart.h} chartData={this.curMaxChart.chart} title={this.curMaxChart.title}/> : null
          }
        </el-dialog>
      </div>
    )
  }
}

</script>
<style lang="scss">
@import '../styles/detailCharts.scss';
</style>

