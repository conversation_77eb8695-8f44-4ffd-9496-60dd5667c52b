<template>
  <div class="dashboard-list">
    <vxe-table
      ref="xTree"
      size="mini"
      height="auto"
      :loading="loading"
      show-overflow
      :tree-config="treeConfig"
      :data="tableData"
      :row-config="{isCurrent: true, isHover: true}"
    >
      <vxe-column field="name" title="名称" tree-node show-overflow>
        <template #default="{ row }">
          <span
            class="flex-vertical-center custom-node"
            :class="row.type!=='GROUP'?'table-node':''"
            @click="clickTreeNode(row)"
          >
            <template v-if="row.type==='GROUP'">
              <i
                class="tree-node-icon"
                :class="$refs.xTree.isTreeExpandByRow(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'"
              />
            </template>
            <template v-else>
              <i class="tree-node-icon vxe-icon-chart-bar-y" />
            </template>
            <div class="custom-ellipsis">{{ row.name }}</div>
          </span>
        </template>
      </vxe-column>
      <vxe-column title="状态">
        <template #default="{ row }">
          <div>{{ row.type === 'GROUP' ? '' : row.status }}</div>
        </template>
      </vxe-column>
      <vxe-column title="类型">
        <template #default="{ row }">
          <div>{{ row.type === 'GROUP' ? '' : row.typeName }}</div>
        </template>
      </vxe-column>
      <vxe-column field="createTime" title="创建日期" />
      <vxe-column field="updateTime" title="修改日期" />
      <vxe-column title="操作" width="450">
        <template #default="{ row }">
          <el-button
            v-if="row.type==='GROUP'"
            size="mini"
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="addChildGroup(row)"
          >新增子组
          </el-button>
          <el-button
            v-if="row.type !== 'REPORT'"
            type="text"
            size="mini"
            :icon="row.type==='GROUP'?'el-icon-circle-plus':'el-icon-s-promotion'"
            @click="openAddDashboard(row)"
          >{{ row.type === 'GROUP' ? '新增图表' : '发布' }}
          </el-button>
          <el-button
            type="text"
            status="primary"
            size="mini"
            icon="el-icon-edit"
            @click="openReName(row)"
          >重命名
          </el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="delDashboard(row)"
          >删除
          </el-button>
        </template>
      </vxe-column>
      <template #empty>
        <table-empty />
      </template>
    </vxe-table>

    <!-- 新增表格 -->
    <add-table
      v-if="addTableVisivle"
      :dict-type="dictType"
      :current-node-data="currentNodeData"
      :origin-data-arr="originDataArr"
      @close="closeTable"
      @addSuccess="addTableSuccess"
    />
    <!-- 重命名弹框 -->
    <el-dialog
      title="重新命名"
      :visible.sync="reNameVisible"
      width="400px"
      :close-on-click-modal="false"
      @close="closeReName"
    >
      <el-form
        ref="reNameForm"
        label-position="right"
        label-width="60px"
        size="small"
        :model="reNameForm"
        :rules="reNameRules"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="reNameForm.name" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeReName">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleReName">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddTable from './add-dashboard.vue'
import { loadData, releaseData } from '@/api/base-table'
import { getViewList } from '@/api/system/data-view'
import { changeStatus, delDashboard, editDashboard } from '@/api/data-visual'

export default {
  components: { AddTable },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    dictType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为汉字、英文、数字、下划线的组合'))
      }
      callback()
    }
    return {
      treeConfig: { transform: true, rowField: 'id' },
      currentNodeData: {},
      addGroupForm: {
        name: ''
      },
      reNameVisible: false,
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      addTableVisivle: false,
      expandTimer: null,
      activeTimer: null,
      originDataArr: []
    }
  },
  computed: {},
  created() {
  },
  beforeDestroy() {
    if (this.expandTimer) clearTimeout(this.expandTimer)
    if (this.activeTimer) clearTimeout(this.activeTimer)
    this.expandTimer = null
    this.activeTimer = null
  },
  methods: {
    // 加载数据
    loadData(data) {
      this.$confirm('确定加载数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = { id: data.id }
        loadData(params).then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg)
          }
        })
      })
    },
    // 释放内存
    releaseData(data) {
      const params = { id: data.id }
      releaseData(params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
        }
      })
    },
    // 展开树
    expandTree(row) {
      this.expandTimer = setTimeout(() => {
        this.$refs.xTree.setTreeExpand(row, true)
      }, 0)
    },
    // 高亮行
    setCurrentRow(row) {
      this.activeTimer = setTimeout(() => {
        this.$refs.xTree.setCurrentRow(row)
      }, 100)
    },
    // 清除高亮
    clearCurrentRow() {
      this.$refs.xTree.clearCurrentRow()
    },
    // 新增子组
    addChildGroup(row) {
      this.$emit('addChildGroup', row)
    },
    // 新增表格
    openAddDashboard(row) {
      if (row.type === 'GROUP') {
        if (!this.originDataArr.length) {
          this.getOriginData()
        }
        this.currentNodeData = row
        this.addTableVisivle = true
      } else {
        // 发布图表
        const text = '发布图表后，该图表将顶替其它已发布的图表，是否继续？'
        this.$confirm(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          changeStatus(row.id, 'PUBLISH').then(res => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '发布成功!'
              })
              this.$emit('refreshTable')
            }
          })
        })
      }
    },
    addTableSuccess(targetId, ...params) {
      this.addTableVisivle = false
      this.$emit('refreshTable', targetId, ...params)
    },
    // 获取源数据
    getOriginData() {
      getViewList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code === 200) {
          this.originDataArr = res.rows
        }
      })
    },
    closeTable() {
      this.addTableVisivle = false
    },
    // 删除节点
    delDashboard(row) {
      const delTxt = row.type === 'GROUP' ? '删除分组时，该分组下级的所有子组和图表将一并删除，是否继续?' : '确定删除该图表吗?'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDashboard(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.$emit('refreshTable')
          }
        })
      })
    },
    resetRenameForm() {
      if (this.$refs.reNameForm) {
        this.$refs.reNameForm.resetFields()
      }
    },
    // 打开重命名弹框
    openReName(row) {
      const { name } = row
      this.reNameVisible = true
      this.resetRenameForm()
      this.reNameForm.name = name
      this.currentNodeData = row
    },
    closeReName() {
      this.reNameVisible = false
    },
    handleReName() {
      this.$refs['reNameForm'].validate((valid) => {
        if (valid) {
          const { type, id, parentId } = this.currentNodeData
          const params = {
            id: id,
            name: this.reNameForm.name,
            type: type,
            parentId: parentId
          }
          editDashboard(params).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.closeReName()
              this.$emit('refreshTable', id)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    clickTreeNode(row) {
      if (row.type == 'GROUP') return
      this.$emit('openTableDetail', row)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/variables';

.dashboard-list {
  height: calc(100vh - 180px);
}

.table-node {
  color: $primary;
  cursor: pointer;

  i {
    margin-right: 2px;
  }
}

.custom-node {
  .vxe-icon-table {
    height: 22px;
    line-height: 24px;
  }
}
</style>
