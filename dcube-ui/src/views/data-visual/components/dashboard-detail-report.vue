<!-- 报告图表 -->
<template>
  <div id="chart-report-wrap" v-loading="wrapLoading" element-loading-spinner="el-icon-loading" :element-loading-text="loadingText||'加载中...'" :class="`chart-report-wrap ${modelType === 'view' && !isRt ? 'chart-view-wrap' : (isRt ? 'chart-rt-model-wrap' : '')}`">
    <div class="chart-report">
      <div id="chart-report-inner" class="chart-report-inner">
        <!-- 标题 -->
        <div :class="`chart-item size-lg chart-center ${activeIndexArr[0]===-1?'is-active':''}`" @click="()=>{cardClick('',-1)}">
          <el-input v-model="chartTitle" :disabled="modelType === 'view'" />
        </div>
        <!-- 日期 -->
        <div v-show="modelType !== 'view' || (modelType === 'view' && chartDate)" :class="`chart-item chart-center size-small ${activeIndexArr[0]===-2?'is-active':''}`" @click="()=>{cardClick('',-2)}">
          <el-date-picker
            v-model="chartDate"
            format="yyyy 年 MM 月 dd 日"
            prefix-icon=""
            :disabled="modelType === 'view'"
            :append-to-body="false"
            type="date"
            placeholder="选择日期"
          />
        </div>
        <template v-if="chartList.length>1 || (chartList.length===1 && modelType !== 'view')">
          <draggable
            v-model="chartList"
            chosen-class="chosen"
            force-fallback="true"
            group="people"
            handle=".mover"
            animation="1000"
            :move="onMove"
            @start="onStart"
            @end="onEnd"
          >
            <transition-group>
              <div
                v-for="(item,index) in chartList"
                :key="(item.id||item.customId)"
                :class="{'chart-item':true,'chart-echart':isEchartsType(item.chartType),
                         'chart-empty':!item.chartType,
                         'is-active':activeIndexArr[0]===index}"
                @click="()=>{cardClick(item,index)}"
              >
                <ToolBar
                  :model-type="modelType"
                  :item="item"
                  @addWidget="addWidget"
                  @delWidget="delWidget"
                  @changeWidgetType="changeWidgetType"
                  @filterWidget="filterWidget"
                  @handleMaxChart="handleMaxChart"
                />
                <!-- 有选择图表类型 -->
                <template v-if="item.chartType">
                  <HChart
                    v-if="isHType(item.chartType)"
                    :sys-vars-list="sysVarsList"
                    :show-input="activeIndexArr[0]===index"
                    :text.sync="item.text"
                    :h-type="item.chartType"
                    :variables-list="variablesList"
                    :model-type="modelType"
                    :is-saving="isSaving"
                    @handleConfigVar="handleConfigVar"
                  />
                  <TextChart
                    v-if="item.chartType==='text'"
                    :sys-vars-list="sysVarsList"
                    :show-input="activeIndexArr[0]===index"
                    :text.sync="item.text"
                    :h-type="item.chartType"
                    :variables-list="variablesList"
                    :model-type="modelType"
                    @handleConfigVar="handleConfigVar"
                  />
                  <div v-if="isConfigType(item) && item.chartType !== 'var'" :style="{width:`${item.w || 60}%`,height:'100%'}">
                    <div
                      v-if="item.chart && item.chart[0] && item.chart[0].dataErrorTip"
                      style="text-align: center;margin-top:10%;"
                    >
                      {{ item.chart[0].dataErrorTip }}
                    </div>
                    <template v-else>
                      <FormGridChart
                        v-if="item.chartType==='formGrid'"
                        :x-dim="item.dimColumns[0] && item.dimColumns[0].desc"
                        :y-dim="item.legendColumns[0] && item.legendColumns[0].desc"
                        :title="`${item.title}`"
                        :chart-data="item.chart"
                        :is-downloading="isDownloading"
                        :data-columns="item.dataColumns"
                        :origin-legend-dim-columns="item.legendColumns"
                        report-type="report"
                        title-position="bottom"
                      />
                      <LineChart
                        v-if="item.chartType==='line'"
                        title-position="bottom"
                        :legend-right="legendRight"
                        :echarts-theme="echartsTheme"
                        :show-series-label="item.dataColumns&&item.dataColumns[0]&&(item.dataColumns[0].showValue === 'true' ||item.dataColumns[0].showValue === true )"
                        :auto-size="autoSize"
                        :chart-data="item.chart"
                        :title="item.title"
                      />
                      <PieChart
                        v-else-if="item.chartType==='pie'"
                        title-position="bottom"
                        :legend-right="legendRight"
                        :echarts-theme="echartsTheme"
                        :show-series-label="item.dataColumns&&item.dataColumns[0]&&(item.dataColumns[0].showValue === 'true' ||item.dataColumns[0].showValue === true )"
                        :auto-size="autoSize"
                        :chart-data="item.chart"
                        :title="item.title"
                      />
                      <BarChart
                        v-else-if="item.chartType==='bar'"
                        title-position="bottom"
                        :legend-right="legendRight"
                        :echarts-theme="echartsTheme"
                        :show-series-label="item.dataColumns&&item.dataColumns[0]&&(item.dataColumns[0].showValue === 'true' ||item.dataColumns[0].showValue === true )"
                        :auto-size="autoSize"
                        :chart-data="item.chart"
                        :title="item.title"
                      />
                    </template>
                  </div>
                </template>
                <!-- 还未选择图表类型 -->
                <template v-else>
                  <div
                    class="add-icon-wrap"
                    @click="()=>{
                      curNeedInsertSourceChart='';
                      curNeedChangeSourceChart='';
                      dialogVisible = true;} "
                  >
                    <i class="el-icon-plus add-icon" />
                  </div>
                </template>
              </div>
            </transition-group>
          </draggable>
        </template>

        <!-- 新增更多的组件 -->
        <!-- <div class="chart-item chart-empty">
        <div class="add-icon-wrap" @click="()=>{this.dialogVisible = true;} ">
          <i class="el-icon-plus add-icon" />
        </div>
      </div> -->
        <!-- 变量的展示 -->
        <TableVar
          v-if="modelType !== 'view'"
          :customer-vars-list="variablesList"
          :sys-vars-list="sysVarsList"
          @handlerDelVar="handlerDelVar"
          @handleConfigVar="handleConfigVar"
        />
      </div>
    </div>
    <!-- 配置项 -->
    <div v-if="activeIndexArr.length || curChart.chartType || modelType === 'view'" :class="`chart-config ${isFoldConfigForm ? 'is-fold' : ''}`">
      <ChartConfig
        :clearable-legend-dim="curChart.chartType === 'formGrid'"
        :clearable-x-dim-key="curChart.chartType === 'var'"
        :dashboard-name="dashboardName"
        :is-config-type="isConfigType(curChart)"
        :cur-chart="curChart && curChart.chartType ? curChart : {}"
        :is-rt="isRt"
        :is-fold-config-form="isFoldConfigForm"
        :echarts-theme="echartsTheme"
        :model-type="modelType"
        :table2d-data="table2dData"
        :handle-change-has-config-change="handleChangeHasConfigChange"
        @handleIsDownloading="(val)=>{listLoading=val;loadingText='下载中';isDownloading=val;}"
        @handleSaveOrUpdateChartList="handleSaveOrUpdateChartList"
        @handleChangeFoldType="handleChangeFoldType"
        @handleChangeTheme="handleChangeTheme"
        @handleToViewModel="handleToViewModel"
        @titleInput="titleInput"
        @select2dTable="select2dTable"
        @changeFilterDim="changeFilterDim"
        @changeXDim="changeXDim"
        @handleClickDimAttr="handleClickDimAttr"
        @changeLegendDim="changeLegendDim"
        @changeIndicatorDim="changeIndicatorDim"
        @heightChange="heightChange"
        @widthChange="widthChange"
      />
    </div>

    <!-- 弹出选择图表类型界面 -->
    <SelectChartType :dialog-visible.sync="dialogVisible" @updateChartType="updateChartType" />

    <!-- 全屏展示完整echarts数据 -->
    <el-dialog
      title=""
      custom-class="max-chart-show-dialog"
      :visible="maxChartShowDialog"
      :fullscreen="true"
      height="100%"
      width="100%"
      @close="() => { this.maxChartShowDialog = false }"
    >
      <FormGridChart
        v-if="curMaxChart.chartType==='formGrid'"
        :x-dim="curMaxChart.dimColumns[0] && curMaxChart.dimColumns[0].desc"
        :y-dim="curMaxChart.legendColumns[0] &&curMaxChart.legendColumns[0].desc"
        :title="curMaxChart.title"
        :chart-data="curMaxChart.chart"
        :data-columns="curMaxChart.dataColumns"
        :origin-legend-dim-columns="curMaxChart.legendColumns"
        :table-max-height-full="curMaxChart.tableMaxHeight"
        report-type="report"
        title-position="bottom"
      />
      <BarChart
        v-if="curMaxChart.chartType === 'bar'"
        :echarts-theme="echartsTheme"
        :show-series-label="curMaxChart.dataColumns && curMaxChart.dataColumns[0]
          && (curMaxChart.dataColumns[0].showValue === 'true'
            || curMaxChart.dataColumns[0].showValue === true)"
        :is-max="true"
        :auto-size="autoSize"
        :chart-data="curMaxChart.chart"
        :title="curMaxChart.title"
      />
      <LineChart
        v-if="curMaxChart.chartType === 'line'"
        :echarts-theme="echartsTheme"
        :show-series-label="curMaxChart.dataColumns && curMaxChart.dataColumns[0]
          && (curMaxChart.dataColumns[0].showValue === 'true'
            || curMaxChart.dataColumns[0].showValue === true)"
        :is-max="true"
        :auto-size="autoSize"
        :chart-data="curMaxChart.chart"
        :title="curMaxChart.title"
      />
      <PieChart
        v-if="curMaxChart.chartType === 'pie'"
        :echarts-theme="echartsTheme"
        :show-series-label="curMaxChart.dataColumns && curMaxChart.dataColumns[0]
          && (curMaxChart.dataColumns[0].showValue === 'true'
            || curMaxChart.dataColumns[0].showValue === true)"
        :is-max="true"
        :auto-size="autoSize"
        :chart-data="curMaxChart.chart"
        :title="curMaxChart.title"
      />
    </el-dialog>
    <FilterConfig
      :current-model-dim-show-filter-dialog="currentModelDimShowFilterDialog"
      :dim-show-filter-dialog="dimShowFilterDialog"
      @closeDimShowFilterDialog="closeDimShowFilterDialog"
      @filterChange="filterChange"
      @handleDimShowOk="handleDimShowOk"
    />
  </div>
</template>

<script>
import LineChart from './LineChart'
import PieChart from './PieChart'
import BarChart from './BarChart'
import HChart from './dashboard/hChart.vue'
import TextChart from './dashboard/textChart.vue'
import FormGridChart from './dashboard/formGridChart.vue'
import SelectChartType from './dashboard/selectChartType'
import ChartConfig from './dashboard/chartConfig'
import FilterConfig from './dashboard/filterConfig'
import TableVar from './dashboard/tableVar'
import ToolBar from './dashboard/toolBar'
import draggable from 'vuedraggable'

import { batchSaveOrUpdateReport, getWidget, get2dTableList, getPreviewData, getTheme, getSystemVariableValue, getSystemVariable } from '@/api/data-visual'
import { deepClone, debounce } from '@/utils'

import windowResize from './mixins/windowResize' // 窗口变化后重新处理流式布局

let hiddenPicOut = null
export default {
  components: { FilterConfig, draggable, LineChart, PieChart, BarChart, SelectChartType, ToolBar, HChart, FormGridChart, TextChart, ChartConfig, TableVar },
  mixins: [windowResize],
  props: {
    // 颜色配置
    defaultDashboardConfig: {
      type: Object,
      default: () => {
        return {}
      }
    },
    curTableDetail: {
      type: Object,
      default: () => {
      }
    },
    isRt: { // 是否是运行态运行在首页等地方的
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isSaving: false,
      isDownloading: false, // 是否正在打印，用于一些组件做一些额外处理
      legendRight: 80, // 右边图例的占位宽度
      curNeedChangeSourceChart: '', // 当前需要修改组件类型的原组件
      curNeedInsertSourceChart: '', // 当前需要在其上面插入组件的原组件
      sysVarsList: [], // 系统变量
      maxChartShowDialog: false,
      curMaxChart: {},
      loadingDOM: null,
      listLoading: false,
      loadingText: '',
      variables: [], // 变量接口列表
      variablesList: [], // 变量页面展示列表
      initialChartParams: {
        'dataColumns': [],
        'dimColumns': [
          {
            'aggregation': '', // 聚合类型
            'desc': '',
            'name': '',
            'orderBy': '', // 排序
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'legendColumns': [], // 图例维度相关的
        'filterColumns': [// 筛选维度相关的
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'page': {
          'currentPage': 1,
          'pageSize': 10
        },
        'sourceType': 'MEMORY',
        'viewId': '',
        'viewScript': ''
      },
      dashboardConfig: {}, // 颜色配置
      dashboardConfigList: {}, // 颜色可配置列表
      echartsTheme: 'light', // 主题颜色
      chartTitle: '管理会计报告',
      chartDate: '',
      autoSize: true, // 用来处理值改变就重置echarts图的大小
      dialogVisible: false,
      activeIndexArr: [-1, 1],
      activeIndexVar: -2,
      curChart: {},
      curChartType: 'chart', // 当前选中组件的类型
      chartList: [],
      widgetList: [],
      isFoldConfigForm: false,
      modelType: 'edit', // edit:编辑视图模式；view:查看视图模式
      queryParams: {
        dashboardId: undefined
        // name: undefined
      },
      dimShowFilterDialog: false,
      currentModelDimShowFilterDialog: [], // 当前弹框需要展示的筛选维数据
      table2dData: [], // 二维表数据列表
      wrapLoading: false
    }
  },
  watch: {
    dashboardConfig: {
      immediate: true,
      handler(newVal) {
        this.echartsTheme = newVal && newVal.themeCode ? newVal.themeCode : 'light'
        this.chartDate = newVal.reportTime || ''
        this.chartTitle = newVal.title || ''
      }
    },
    listLoading: {
      immediate: true,
      handler(newval) {
        if (newval) {
          this.wrapLoading = true
        } else {
          this.wrapLoading = false
        }
        this.loadingText = ''
      }
    },
    curTableDetail: {
      handler(newVal, oldVal) {
        this.queryParams.dashboardId = this.curTableDetail.id // 请求当前报告的唯一标识id
        this.dashboardId = this.curTableDetail.id
        this.dashboardName = this.curTableDetail.name
        this.initData()
      },
      immediate: true,
      deep: true
    },
    activeIndexVar(newVal) {
      // console.log(newVal, 'activeIndexVar')
      if (this.curChartType === 'var') {
        if (newVal === -1) {
          // 新增的情况
          this.curChart = this.initPreviewChartList({ chartType: 'var', isAdd: true }, {})
        } else {
          this.curChart = this.variablesList[newVal] || {}
        }
      }
    },
    activeIndexArr(newVal) {
      if (this.curChartType === 'var') {
        //
      } else {
        this.curChart = this.chartList[newVal[0]] || {}
      }
      this.autoSize = !this.autoSize
    },
    chartList: {
      deep: true,
      handler(newVal) {
        // console.log(newVal, '监听到的组件列表数据')
        if (this.curChartType === 'chart') {
          this.curChart = this.chartList[this.activeIndexArr[0]] || {}
        }
      }
    },
    variablesList(newVal) {
      // console.log(newVal, 'variablesList自动以变量的列表值')
      if (newVal === -1) {
        //
      } else {
        this.curChart = newVal[this.activeIndexVar] || {}
      }
    },
    /**
     * 监听到接口获取的组件列表更改，需要重新处理当前页面
     * @param newVal
     * @param oldVal
     */
    widgetList(newVal, oldVal) {
      const result = []
      newVal && newVal.forEach(item => {
        const { configJson } = item
        if (configJson && configJson.chartType) {
          const newItem = this.initPreviewChartList(item, configJson) // 初始化不同组件类型需要的参数
          this.handleUpdateChart(newItem)
          result.push(newItem)// 初始化不同组件类型需要的参数
        }
      })
      // 当前组件和新增组件合并,还需要根据X来排序
      result.sort((a, b) => a.x - b.x)
      result.map((item, index) => {
        item.x = index
      })
      this.chartList = result.concat([{
        ...this.initPreviewChartList(),
        x: result.length,
        y: 1
      }])
    },
    variables(newVal) {
      const result = []
      newVal && newVal.forEach(item => {
        const { config: configJson } = item
        if (configJson) {
          const newItem = this.initPreviewChartList(item, configJson) // 初始化不同组件类型需要的参数
          newItem.chartType = 'var'
          this.handleUpdateChart(newItem) // 处理是否需要获取预览的数据，和维度根据数据源获取的各个下
          result.push(newItem)
        }
      })
      this.variablesList = result
    },
    modelType() {
      setTimeout(() => {
        this.autoSize = !this.autoSize
      }, 1500)
    },
    autoSize() {
      // this.handleAutoSize()
    }
  },
  created() {
    this.handleAutoSize = debounce(this.handleAutoSize, 100)// 处理屏幕变化后内容溢出后影响自适应
  },
  methods: {
    onMove(e) {
      if (!e.relatedContext.element.chartType) return false
      return true
    },
    /**
     * 在当前组件上面插入组件
     */
    addWidget(item) {
      this.curNeedInsertSourceChart = item
      this.dialogVisible = true
    },
    /**
     * 更改组件的类型
     * @param item
     */
    changeWidgetType(item) {
      this.$confirm('确定修改组件类型吗？修改后配置参数会被清空', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.curNeedChangeSourceChart = item
        this.dialogVisible = true
      })
    },
    onStart() {
      this.drag = true
    },
    onEnd() {
      // 拖拽结束修改chart的x属性，保存后可以保留当前顺序
      this.drag = false
      this.chartList.map((item, index) => {
        item.x = index
      })
    },
    initData() {
      if (this.isRt) {
        this.modelType = 'view'
      }
      getTheme().then(res => {
        if (res.code === 200) {
          this.dashboardConfigList = res.data
        }
      })
      getSystemVariable().then(res => {
        if (res.code === 200) {
          this.sysVarsList = res.data
          this.sysVarsList.forEach(async(s, i) => {
            const val = await getSystemVariableValue(s.code)
            this.sysVarsList[i].chart = val && val.data ? val.data : ''
          })
        }
      })
      // 获取数据源信息
      get2dTableList().then(res => {
        if (res.code === 200) {
          this.table2dData = this.removeLeafChild(res.data.memView)
          // if (this.isRt) {
          //   this.widgetList = this.rtWidgets
          //   this.dashboardConfig = this.defaultDashboardConfig
          //   return
          // }
          this.getWidget() // 获取当前报告组件列表
        }
      })
    },
    /**
     * 打开筛选的值设置弹框
     * @param id
     * @param chart
     */
    filterWidget(id, chart) {
      this.dimShowFilterDialog = true
      const flag = this.commonGetCurChart([chart.x, chart.y])
      this.currentModelDimShowChart = chart
      // 赋值弹框展示当前选中项的过滤数组，且必需有name属性展示的
      this.currentModelDimShowFilterDialog = deepClone(flag.filterColumns || []).filter(item => item.name)
    },
    closeDimShowFilterDialog(bol) {
      this.dimShowFilterDialog = bol
    },
    /**
     * 筛选框点击确认调用预览的接口
     */
    handleDimShowOk() {
      this.dimShowFilterDialog = false
      this.currentModelDimShowChart.filterColumns = this.currentModelDimShowFilterDialog
      this.currentModelDimShowFilterDialog = []
      this.filterPreviewChartData(this.currentModelDimShowChart)
      // this.handleChangeHasConfigChange(true, 'handleDimShowOk')
      // 更改完除了要调用预览的接口，还需要调用保存的接口，这里需要修改直接在右侧配置不用弹框
    },
    /**
     * 筛选值确定后调用的预览接口
     * @param target
     */
    filterPreviewChartData(target) {
      const { sourceType, page, viewScript, viewId, dimColumns, filterColumns, legendColumns, dataColumns } = target
      getPreviewData({ sourceType, page, viewScript, viewId, dimColumns, filterColumns, legendColumns, dataColumns }).then(res => {
        if (!res.data) return this.$message.error('数据获取异常')
        this.chartList.find((item, ind) => {
          if ((item.id && item.id === target.id) || (item.x === target.x)) {
            const chart = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
            const newItem = Object.assign({}, { ...item, chart: chart,
              queryChartParams: { ...target.queryChartParams, filterColumns, totalPage: res.data.totalPage, totalRows: res.data.totalRows } // 记录当前这个图数据请求完了没
            })
            this.chartList.splice(ind, 1, newItem)
            return true
          }
        })
      })
    },
    filterChange(value, index, type) {
      this.$set(this.currentModelDimShowFilterDialog[index], 'queryValue', value || '')
      this.currentModelDimShowFilterDialog[index].queryType = type === 'DECIMAL' ? 0 : -1 // 查询模糊度是精确还是模糊 0：精确 1：模糊
    },
    /**
     * 全屏展示图表
     * @param chart
     */
    async handleMaxChart(chart) {
      const queryChartParams = chart.queryChartParams || {}
      const { page, totalRows } = queryChartParams
      const { pageSize } = page
      const curMaxChart = deepClone(chart)
      if (pageSize < totalRows) {
        // 还有数据没有请求完需要一次请求所有数据
        const newPage = { currentPage: 1, pageSize: totalRows }
        await getPreviewData({ ...queryChartParams, page: newPage }).then(res => {
          if (!res.data) return this.$message.error(res.data.msg)
          const chartData = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
          curMaxChart.chart = chartData
        })
      }
      if (curMaxChart.chartType === 'formGrid') {
        curMaxChart.tableMaxHeight = document.body.clientHeight
      }
      this.curMaxChart = curMaxChart
      this.maxChartShowDialog = true
    },
    /**
     * 点击变量需要弹出变量的配置项界面,或者新增变量
     * @param item
     */
    handleConfigVar(item, type, index) {
      this.curChartType = 'var'
      this.activeIndexArr = []
      if (type === 'add') {
        // this.curVar = { chartType: 'var', isAdd: true }
        this.variablesList.push({ ...this.initPreviewChartList(), chartType: 'var', isAdd: true, title: '变量名' })
        this.activeIndexVar = this.variablesList.length - 1
      } else {
        this.activeIndexVar = index
        // this.previewChartData({}) // 预览数据。在预览的时候就可以直接取这里面的属性来展示了
      }
    },
    isEchartsType(val) {
      return val === 'line' || val === 'bar' || val === 'pie'
    },
    isHType(val) {
      return val === 'h1' || val === 'h2' || val === 'h3' || val === 'h4'
    },
    isConfigType(chart) {
      return chart && chart.chartType && (this.isEchartsType(chart.chartType) || chart.chartType === 'formGrid' || chart.chartType === 'var')
    },
    /**
     * targetChart:当前chartlist里对应的某个
     * 更新各个卡片的属性数据视图等信息
     * @param param0
     */
    handleUpdateChart(targetChart = {}) {
      const { viewId } = targetChart
      let node = {}
      this.table2dData.find(item => {
        if (item.id == viewId) {
          node = item
          return true
        }
        return item.children?.find(child => {
          if (child.id == viewId) {
            node = child
            return true
          }
          return false
        })
      })
      if (node.id) {
        this.select2dTable(node, '', 'setChart', targetChart) // 绑定配置的数据下拉值
        this.previewChartData(targetChart)
        // this.cardClick(targetChart, 'force') //预览数据
      }
    },
    /**
     * 如果原来是 x=1,y=1,width=1,height=1
     * 现在变成是 x=1,y=1,width=3,height=1
     * 那么需要把 x=1,并且y=2 和 y=3的隐藏
     *
     * 如果原来是 x=1,y=1,width=1,height=2
     * 现在变成是 x=1,y=1,width=3,height=2
     * 那么需要把 x=1或者x=2,并且y=2 和 y=3的隐藏
     *
     * 总结：
     * 如果是宽度变宽了，就处理高度是否是大于1的，大于1的需要循环多排，如果高度就是1，只用处理当前这一横排后面的y是否是小于等于当前y值和增加的宽度newval的和
     * 如果是高度变高了，就处理宽度是否是大于1的，大于1的就需要处理多列，如果宽度是1，只用处理当前这一列下面的x是否是小于等于当前x值和增加的高度newval的和
     */
    changeChartShowInit(newVal, sign, customTarget, type) {
      const target = customTarget || this.curChart
      const { x, y, w, h } = target
      const startIndex = sign === 'width' ? y : x
      const oldVal = customTarget && type !== 'delete' ? 1 : (sign === 'width' ? w : h)
      const positionSign = sign === 'width' ? 'y' : 'x'
      const fixedSign = sign === 'width' ? 'x' : 'y'
      const fixedVal = sign === 'width' ? h : w // 当前操作项的宽或者高
      const fixedStartIndex = sign === 'width' ? x : y // 当前操作项的x轴或者Y轴
      const newValW = sign === 'width' ? newVal : w
      const newValH = sign !== 'width' ? newVal : h

      let show = true
      const newVal2 = newVal
      if (newVal > oldVal) { // 值变大了需要把其相邻的隐藏
        show = false
      } else { // 值变小了需要把其相邻的释放出来
        show = true
        newVal = oldVal
      }

      const finalCharts = this.chartList
      let flag = []
      // eslint-disable-next-line no-inner-declarations
      function handleShow(count) {
        const result = []
        // 变大的情况横轴的话，就是向同一排的后面找y值小于当前y值和增加的宽度和
        let sI = startIndex
        let sE = startIndex + newVal
        // 变小的情况横轴的话，是反向处理的
        if (show) {
          sE = oldVal + startIndex
          sI = oldVal + startIndex - (oldVal - newVal2)
        }
        for (let i = sI; i < sE; i++) {
          const flagIndex = finalCharts.findIndex(item => item[positionSign] === i && item[fixedSign] === (count))
          const flag = flagIndex > -1 ? finalCharts[flagIndex] : false
          if ((flag.x === x && flag.y === y) || flag.show === show) { // 已经是符合条件了的继续往后找
            continue
          }
          if (flag && ((!show && (sign === 'width' ? newValW > flag.w && newValH >= flag.h : newValW >= flag.w && newValH > flag.h)) || show)) {
            result.push(flagIndex) // 记录下需要更改show属性为false的下标
          } else {
            return false
          }
        }
        return result
      }

      // 处理并列排的横轴或者纵轴,另一个方向的值大于1的时候就需要处理多排的,默认值不大于1时是处理的1排的
      for (let i = fixedStartIndex; i < fixedStartIndex + fixedVal; i++) {
        const r = handleShow(i)
        if (!r) {
          flag = false
          break
        }
        flag.push(...r)
      }

      if (flag && flag.length) {
        // 需要把这些下边的show属性都改成false
        flag.forEach(item => {
          // 如果是变小了是放出图，还需要额外判断下其上下左右是否有位置可以给其放出来
          if (!show || (show && this.handleCanShow(finalCharts[item], target))) {
            if (!show) {
              // 避免出现不能放出来情况把隐藏的宽高都重置为1
              finalCharts[item].w = 1
              finalCharts[item].h = 1
            }
            finalCharts[item].show = show
          }
        })
        return true
      }
      return false
    },
    /**
     * 属性配置--高度修改
     * @param value
     */
    heightChange(value) {
      this.autoSize = !this.autoSize // 重新处理流式布局
      if (this.curChart.h === value) return
      this.curChart.h = value
      this.handleChangeHasConfigChange(true, 'heightChange')
    },
    widthChange(value) {
      this.autoSize = !this.autoSize // 重新处理流式布局
      if (this.curChart.w === value) return
      this.curChart.w = value
      this.handleChangeHasConfigChange(true, 'widthChange')
    },
    changeFilterDim(val) {
      Object.assign(this.curChart, { ...val })
    },
    /**
     * 切换到视图模式
     */
    handleToViewModel() {
      this.modelType = this.modelType === 'edit' ? 'view' : 'edit'
      this.activeIndexArr = []
      this.activeIndexVar = -2
    },
    handleChangeTheme(val) {
      this.echartsTheme = val
    },
    commonGetCurChart(activeIndexArr, hasIndex) {
      if (hasIndex) {
        const i = this.chartList.findIndex(item => item.x === activeIndexArr[0])
        return { chart: this.chartList[i], index: i }
      }
      return this.chartList.find(item => item.x === activeIndexArr[0])
    },
    /**
     * 设置不同维度的额外属性 排序，聚合等
     * @param index
     * @param indexPath
     * @param v
     * @param type 不同的维度类型 dimColumns dataColumns
     */
    handleClickDimAttr({ needPreview, resultChart }) {
      if (resultChart.chartType === 'var') {
        if (!needPreview) {
          // 修改变量的配置项的行条件值
          this.$nextTick(() => {
            this.variablesList.splice(this.activeIndexVar, 1, { ...resultChart })
          })
          return
        }
        // 变量的直接预览
        this.previewChartData(resultChart)
        return
      }
      const { index } = this.commonGetCurChart([resultChart.x, resultChart.y], true)
      this.chartList.splice(index, 1, { ...resultChart })
      if (needPreview) {
        this.previewChartData(resultChart)
      }
      // console.log(this.chartList, 'this.chartList......')
    },
    /**
     * 折叠配置项
     */
    handleChangeFoldType() {
      this.isFoldConfigForm = !this.isFoldConfigForm; this.autoSize = !this.autoSize
    },
    /**
     * 新增新的组件类型
     * @param val
     */
    updateChartType(val) {
      if (this.curNeedInsertSourceChart) {
        const x = this.curNeedInsertSourceChart.x
        const newChart = { ...this.initPreviewChartList(), x: 1, y: 1, chartType: val }
        // 当前是需要在指定组件上面插入一个组件
        this.chartList.splice(x, 0, newChart)
        this.chartList.map((item, index) => {
          item.x = index
        })
        this.curNeedInsertSourceChart = ''
        this.cardClick(this.chartList[x], x)
        return
      }
      if (this.curNeedChangeSourceChart) {
        if (val === this.curNeedChangeSourceChart.chartType) {
          this.$message({
            message: '修改后和当前组件类型一致！',
            type: 'warning'
          })
          this.curNeedChangeSourceChart = ''
          return
        }
        // 当前是修改组件类型的操作
        const newChart = { ...this.initPreviewChartList(), x: this.curNeedChangeSourceChart.x, y: 1, chartType: val }
        this.chartList.splice(this.curNeedChangeSourceChart.x, 1, newChart)
        this.curNeedChangeSourceChart = ''
        return
      }
      this.curChart.chartType = val
      if (this.isEchartsType(val) && !this.curChart.chart) {
        this.curChart.chart = []
      }
      if (this.activeIndexArr[0] === this.chartList.length - 1) {
        this.chartList.push({
          ...this.initPreviewChartList(),
          x: this.activeIndexArr[0] + 1,
          y: 1
        })
      }
    },
    handleAutoSize() {
      this.hiddenPic = true
      if (hiddenPicOut) {
        clearTimeout(hiddenPicOut)
      }
      // 自适应完成后放开超出可见
      hiddenPicOut = setTimeout(() => {
        this.hiddenPic = false
      }, 1000)
    },
    // 去掉叶子结点的children属性
    removeLeafChild(tree) {
      for (const item of tree) {
        if (item.children) {
          item.children = this.removeLeafChild(item.children)
        } else {
          delete item.children
        }
      }
      return tree
    },

    /**
     * 属性配置--标题修改
     * @param value
     */
    titleInput(value) {
      this.curChart.title = value
      // if (this.curChartType === 'var') {
      //   // this.$set(this.variablesList[this.activeIndexVar], 'title', value)
      // }
      this.handleChangeHasConfigChange(true, 'titleInput')
    },
    handleChangeHasConfigChange(flag, from) {
      this.hasConfigChange = flag
    },
    /**
     * 属性配置--选择数据源方法,只修改当前的，不修改存库的数据，只有保存后才落库，某则切换视图后还原成库里的
     * @param node
     * @param instanceId
     */
    select2dTable(node, instanceId, type, targetChart) {
      const { id, metadata, viewScript, tableName } = node
      const target = targetChart || this.curChart
      // 切换了数据源清除以往维度数据
      if (id != target.viewId) {
        if (targetChart) {
          target.xDimKey = ''
          target.legendDimKey = ''
          target.indicatorKey = ''
          target.filterDimKey = ''
        } else {
          this.$set(this.curChart, 'xDimKey', '')
          this.$set(this.curChart, 'legendDimKey', '')
          this.$set(this.curChart, 'indicatorKey', '')
          this.$set(this.curChart, 'filterDimKey', '')
          if (!type) {
            this.handleChangeHasConfigChange(true, 'select2dTable')
          }
        }
      }

      // 横轴维度type="VARCHAR" 图例维度type！="VARCHAR"
      const [xDimArr, legendDimArr, indicatorDimArr, filterDimArr] = [[], [], [], []]
      metadata && metadata.forEach(item => {
        if (item.type === 'VARCHAR') {
          xDimArr.push(item)
          legendDimArr.push(item)
          if (target.chartType === 'var') { // 变量的指标可以是文本类型的
            indicatorDimArr.push(item)
          }
        } else if (item.type === 'DECIMAL' || item.type === 'DOUBLE' || item.type === 'INTEGER') {
          indicatorDimArr.push(item)
        }
        filterDimArr.push(item)
      })
      target.xDimArr = xDimArr
      target.filterDimArr = filterDimArr
      target.legendDimArr = legendDimArr
      target.indicatorDimArr = indicatorDimArr
      target.viewId = id
      target.viewName = tableName
      target.viewScript = viewScript
      return target
    },
    /**
     * 属性配置--横轴维度的下拉选择更改事件
     * @param key
     */
    changeXDim(key) {
      this.handleChangeHasConfigChange(true, 'changeXDim')
      this.curChart.xDimKey = key
      this.previewChartData({})
    },
    /**
     * 属性配置--图例修改
     * @param key
     */
    changeLegendDim(key) {
      this.handleChangeHasConfigChange(true, 'changeLegendDim')
      this.curChart.legendDimKey = key
      this.previewChartData({})
    },
    /**
     * 属性配置--指标维修改
     * @param key
     */
    changeIndicatorDim(key) {
      this.handleChangeHasConfigChange(true, 'changeIndicatorDim')
      this.curChart.indicatorKey = key
      this.previewChartData({})
    },
    /**
     * 维度值更改后触发的预览事件
     */
    previewChartData(targetChart = {}) {
      const target = targetChart.chartType ? targetChart : this.curChart
      const { viewId, xDimKey, legendDimKey, viewScript, indicatorKey, filterColumns, dimColumns = [], dataColumns = [], legendColumns = [] } = target
      // formGrid的组件可以图例维度为空，且指标为多选的时候也可以走接口
      const formGridCanPreview = target.chartType !== 'formGrid' || (target.chartType === 'formGrid' && (!target.indicatorKey || target.indicatorKey.split(',').length < 1))
      // 变量类型的组件，行条件可以是空
      if (!viewId || (!xDimKey && target.chartType !== 'var') || (!legendDimKey && (!target || (target.chartType !== 'pie' && target.chartType !== 'var' && formGridCanPreview))) || !indicatorKey) return
      let xDimInfo = (target.xDimArr && target.xDimArr.find(item => item.name === xDimKey)) || null
      const legendInfo = (target.legendDimArr && target.legendDimArr.find(item => item.name === legendDimKey)) || null
      let indicatorInfo = (target.indicatorDimArr && target.indicatorDimArr.find(item => item.name === indicatorKey)) || null

      if (indicatorKey.split(',').length > 1) {
        indicatorInfo = (target.indicatorDimArr && target.indicatorDimArr.filter(item => indicatorKey.split(',').indexOf(item.name) > -1)) || null
        // console.log(indicatorInfo, 'indicatorInfoindicatorInfo')
      }

      if (xDimKey.split(',').length > 1) {
        xDimInfo = (target.xDimArr && target.xDimArr.filter(item => xDimKey.split(',').indexOf(item.name) > -1)) || null
      }

      const dimColumns2 = xDimInfo ? [{ ...dimColumns[0], ...xDimInfo }] : []
      const tQueryChartParams = deepClone(target.queryChartParams)
      if (Array.isArray(xDimInfo)) { // 多选的指标
        tQueryChartParams.dimColumns = xDimInfo ? xDimInfo.map((item, i) => { return { ...dimColumns[i], ...item } }) : []
      } else {
        tQueryChartParams.dimColumns = dimColumns2
      }
      // tQueryChartParams.dimColumns = dimColumns2

      const filterColumns2 = filterColumns ? [...filterColumns] : []
      tQueryChartParams.filterColumns = filterColumns2

      tQueryChartParams.legendColumns = legendInfo ? [{ ...legendColumns[0], ...legendInfo }] : []
      // tQueryChartParams.dataColumns = indicatorInfo ? [{ ...dataColumns[0], ...indicatorInfo }] : []

      if (Array.isArray(indicatorInfo)) { // 多选的指标
        tQueryChartParams.dataColumns = indicatorInfo ? indicatorInfo.map((item, i) => { return { ...dataColumns[i], ...item } }) : []
      } else {
        tQueryChartParams.dataColumns = indicatorInfo ? [{ ...dataColumns[0], ...indicatorInfo }] : []
      }

      tQueryChartParams.viewScript = viewScript
      tQueryChartParams.viewId = viewId

      if (target.chartType === 'var') { // 变量类型预览的特殊参数hasVariable
        tQueryChartParams.hasVariable = true
      } else {
        delete tQueryChartParams.hasVariable
      }

      target.queryChartParams = tQueryChartParams

      getPreviewData({ ...target.queryChartParams, filterColumns: target.queryChartParams.filterColumns.filter(f => f.name) }).then(res => {
        if (!res.data) return this.$message.error('暂无数据')
        const chart = res.data ? (res.data.rows ? res.data.rows : [{ dataErrorTip: '暂无数据' }]) : [{ dataErrorTip: '数据源已被修改，无法展示数据' }]
        if (target.chartType !== 'var') {
          this.chartList.find((item, ind) => {
            if ((item.id && item.id === target.id) || (item.x === target.x)) {
              const newItem = Object.assign({}, { ...item, chart: chart, ...target.queryChartParams,
                queryChartParams: { ...target.queryChartParams, totalPage: res.data.totalPage, totalRows: res.data.totalRows } // 记录当前这个图数据请求完了没
              })
              this.chartList.splice(ind, 1, newItem)
              return true
            }
          })
        } else {
          // 把变量的值放到属性上在预览的时候直接取值展示
          Object.assign(target, { chart, ...target.queryChartParams })
        }
      })
    },
    // 获取当前视图的组件数据
    getWidget() {
      this.listLoading = true
      getWidget(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dashboardConfig = res.data.dashboardConfig || {} // themeCode:"light",themeName:"经典白"
          this.widgetList = res.data.widgets || []
          if (!this.widgetList.length) {
            this.chartList = [].concat([{
              ...this.initPreviewChartList(),
              x: 0,
              y: 1
            }])
          }
          this.variables = res.data.dashboardConfig ? res.data.dashboardConfig.variables : []
          // || [
          //   {
          //     'config': {
          //       'title': '金额变量',
          //       'x': 2,
          //       'y': 3,
          //       'w': 1,
          //       'h': 1,
          //       'viewId': '562',
          //       'viewName': '测试2222',
          //       'sourceType': 'MEMORY',
          //       'viewScript': 'SELECT * FROM grid.t_cs2222_ud1j3u7cxp',
          //       'chartType': 'var',
          //       'dimColumns': [
          //         {
          //           'name': 'col_xh_38aa0b',
          //           'desc': '序号',
          //           'type': 'VARCHAR',
          //           'orderBy': '',
          //           'aggregation': '',
          //           'queryType': '',
          //           'queryValue': '',
          //           'showValue': null
          //         }
          //       ],
          //       'dataColumns': [
          //         {
          //           'name': 'col_je_1pxqcx',
          //           'desc': '金额',
          //           'type': 'DOUBLE',
          //           'orderBy': '',
          //           'aggregation': '',
          //           'queryType': '',
          //           'queryValue': '',
          //           'showValue': false
          //         }
          //       ],
          //       'filterColumns': [

          //       ],
          //       'page': {
          //         'currentPage': 1,
          //         'pageSize': 10
          //       }
          //     },
          //     'name': '金额变量'
          //   }
          // ]
        }
      }).finally(() => {
        setTimeout(() => {
          this.listLoading = false
        }, 700)
      })
    },
    /**
     * 删除变量
     */
    handlerDelVar(item) {
      this.delWidget(item.id, item, 'var')
    },
    /**
     * 删除格子里组件,并不是真实删除，而是去掉chartlist的某一条，只有在保存的时候才是真实删除
     * @param id
     */
    delWidget(id, chart, type) {
      const delTxt = '确定删除该组件吗?'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 没有保存过数据落库的直接本地清除相关组件
        let hasIndex2 = -1
        let target = this.chartList
        if (type === 'var') {
          target = this.variablesList
          hasIndex2 = this.variablesList.findIndex(item => item.id === id || item.title === chart.title)
        } else {
          hasIndex2 = this.chartList.findIndex(item => item.id === id || item.x === chart.x)
        }

        if (hasIndex2 > -1) {
          target.splice(hasIndex2, 1)
        }
        return
      }).catch(error => {
        // 取消
        return error
      })
    },
    /**
     * 保存或者新增组件配置
     */
    async handleSaveOrUpdateChartList() {
      this.isSaving = true
      await new Promise((resolve) => {
        setTimeout(() => { resolve() })
      })
      // if () {
      const { dashboardId } = this.queryParams
      const list = this.chartList.filter(item => item.chartType).map(item => {
      // const list = this.chartList.map(item => {
        return {
          id: item.id,
          viewId: item.viewId,
          viewName: item.viewName,
          name: this.dashboardName,
          configJson: {
            ...item
          }
        }
      })
      const variables = this.variablesList.filter(item => item.viewId).map(item => {
        return {
          id: item.id,
          // viewId: item.viewId,
          // viewName: item.viewName,
          name: this.dashboardName,
          config: {
            ...item
          }
        }
      })
      // console.log(list, 'widgets', variables)
      // return
      batchSaveOrUpdateReport({
        dashboardId,
        widgets: list,
        config: {
          title: this.chartTitle, // 当前报告的标题
          reportTime: this.chartDate, // 当前报告的时间
          themeCode: this.echartsTheme,
          themeName: this.echartsTheme === 'light' ? '经典白' : '科技蓝',
          variables
        }
      }).then(res => {
        if (res) {
          this.$message({
            type: 'success',
            message: '保存成功!'
          })
        }
      }).finally(() => {
        this.isSaving = false
        // const curChartIndex = this.chartList.findIndex(item => item.x === this.activeIndexArr[0] && item.y === this.activeIndexArr[1])
        // if (curChartIndex > -1) {

        // }
      }).catch(() => {
        this.$message({
          type: 'error',
          message: '保存失败!'
        })
      })
      // }
    },
    /**
     * curchart没有属性的时候默认赋值一些这样在更新时才可以监听到
     */
    initCurChart() {
      return {
        chart: [],
        'w': 1,
        'h': 1,
        show: true,
        'chartType': null,
        'code': '',
        'dataColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': '',
            'showValue': false,
            'format': ''
          }
        ],
        'dimColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'filterColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'legendColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'page': {
          'currentPage': 0,
          'pageSize': 0
        },
        'sourceType': 'MEMORY',
        'title': '',
        'viewId': '',
        'viewName': '',
        'viewScript': ''
      }
    },
    initPreviewChartList(widget = {}, config = {}) {
      const { x, dimColumns = [], filterColumns = [], legendColumns = [], dataColumns = [], chartType } = config
      const queryChartParams = deepClone(this.initialChartParams)
      if (chartType === 'formGrid') {
        // 表格的需要一次请求尽量多的数据，界面里会做虚拟滚动处理不会影响性能
        queryChartParams.page.pageSize = 10000
      }
      const obj = {}
      // 把属性配置需要的属性复制到form身上一份
      obj.xDimKey = dimColumns && dimColumns.length ? dimColumns.map(item => item.name).join(',') : ''
      // obj.xDimKey = dimColumns[0] ? dimColumns[0].name : ''
      obj.filterDimKey = filterColumns && filterColumns.length ? filterColumns.filter(f => f.name).map(i => i.name) : ''
      obj.legendDimKey = legendColumns[0] ? legendColumns[0].name : ''
      obj.indicatorKey = dataColumns && dataColumns.length ? dataColumns.map(item => item.name).join(',') : ''
      if (chartType === 'var') {
        // 变量类型的预览的时候多一个hasVariable参数
      }
      return {
        customId: new Date().getTime(), // 唯一key，在更新时用到
        chart: [],
        'w': 100,
        'h': 1,
        'x': x || 999,
        'y': 1,
        show: true,
        'chartType': null,
        'code': '',
        'dataColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': '',
            'showValue': false,
            'format': ''
          }
        ],
        'dimColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'filterColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'legendColumns': [
          {
            'aggregation': '',
            'desc': '',
            'name': '',
            'orderBy': '',
            'queryType': '',
            'queryValue': '',
            'type': ''
          }
        ],
        'page': {
          'currentPage': 0,
          'pageSize': 0
        },
        'sourceType': 'MEMORY',
        'title': '',
        'viewId': '',
        'viewName': '',
        'viewScript': '',
        ...widget,
        ...config,
        ...obj,
        queryChartParams
      }
    },
    /**
     * 卡片组件点击后事件，处理清空当前选中的数据源相关内容，初始化为当前选中的卡片的数据源等
     * @param param0
     */
    cardClick(targetChart, index) {
      if (this.activeIndexArr[0] === index) {
        return
      }
      if (this.modelType === 'view') {
        return
      }
      this.curChartType = 'chart'
      if (!isNaN(index)) {
        this.activeIndexArr = [index, 1] // 没有y轴默认是1
        this.activeIndexVar = -2
      }
      if (targetChart) {
        targetChart.queryChartParams = deepClone(targetChart.queryChartParams || this.initialChartParams)
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/variables';
  .chart-report-wrap{
    display: flex;
    background:#f0f2f5;
    .chart-report{
      max-width: 100%;
      flex:1;
      background:#f0f2f5;
      .chart-report-inner{
        box-sizing: border-box;
        width: 190mm;
        margin:0 auto;
        background:white;
        padding:10mm;
        margin-top:15px;
        margin-bottom:15px;
      }
      .chart-item{
        background: white;
        position: relative;
        border: 1px solid #DCDFE6;
        margin-bottom:8px;
        min-height:40px;
        &.is-active{
          border-color:$primary;
          &:has(.operateBtn){
            padding-top:25px;
          }
        }
        &.size-lg{
          font-size:18px;
          ::v-deep{
            .el-input__inner{
              padding:0;
              font-size:18px;
              font-weight: bold;
              font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
            }
          }
        }
        &.size-small{
          font-size:12px;
          ::v-deep{
            .el-input__inner{
              padding:0;
              font-size:12px;
              font-weight: bold;
              font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
            }
          }
        }
        &.chart-echart{
          height:250px;
        }
        &.chart-empty{
          min-height: 80px;
          line-height: 80px;
          text-align: center;
        }
        &.chart-center{
          ::v-deep{
            .el-input__inner{
              text-align: center;
            }
            .el-date-editor.el-input{
              width: 100%;
            }
          }
        }
        &:hover{
          ::v-deep{
            .operateBtn{
              opacity: 1;
            }
          }
        }
      }
    }
    &:not(.chart-view-wrap,.chart-rt-model-wrap){
      height: calc(100vh - 80px - 30px);
      .chart-report{
        overflow: auto;
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: #c1c1c1;
          border-radius: 8px;
        }
        scrollbar-color: #c1c1c1 transparent;
        scrollbar-width: thin;
      }
    }
    &.chart-view-wrap,.chart-rt-model-wrap{
      padding:12px;
      .chart-item{
        border:none;
      }
      .chart-empty{
        display: none;
      }
      .chart-config{
        flex-basis: 0;
      }
      ::v-deep{
        .el-date-editor{
          .el-input__prefix{
            display: none;
          }
        }
        .el-input{
          &.is-disabled .el-input__inner{
            font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
            background:white;
            border:none;
            color:#606266;
            font-weight: bold;
          }
        }
      }
    }
    .chart-config{
      background:white;
      flex-basis: 285px;
      &.is-fold{
        flex-basis: 24px;
      }
    }
  }
</style>
