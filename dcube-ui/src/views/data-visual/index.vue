<template>
  <div class="app-container">
    <!-- tag区域 -->
    <div class="tag-box">
      <el-tag
        v-for="tag in tags"
        :key="tag.name"
        effect="plain"
        :closable="tag.close"
        :type="tag.id===currentTagId?'':'info'"
        size="small"
        @click="clickTag(tag)"
        @close="closeTag(tag)"
      >
        {{ tag.name }}
      </el-tag>
    </div>
    <!-- 数据列表 -->
    <div v-show="currentTagId === 0">
      <div class="top-search-area">
        <el-form ref="queryForm" size="small" :model="queryParams" :inline="true">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入图表名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="main-btn">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="addRootGroup"
          >新增分组</el-button>
        </div>
      </div>

      <table-list ref="treeTable" :dict-type="dictType" :table-data="tableData" :loading="listLoading" @refreshTable="getList" @addChildGroup="openChildGroup" @openTableDetail="openTableDetail" />
    </div>
    <div v-if="currentTagId !== 0">
      <dashboard-detail v-if="curTableDetail.type==='BULLETIN_BOARD'" :table-permission="tablePermission" :dict-type="dictType" :cur-table-detail="curTableDetail" @changeTable="changeTable" />
      <dashboard-report v-else :table-permission="tablePermission" :dict-type="dictType" :cur-table-detail="curTableDetail" @changeTable="changeTable" />
    </div>
    <add-group v-if="addGroupVisible" :current-node-id="currentNodeId" @close="closeAddGroup" @handleAddGroup="handleAddGroup" />
  </div>
</template>

<script>
import { addDashboard, getList } from '@/api/data-visual'
import tableList from '@/views/data-visual/components/dashboard-list'
// import dashboardDetail from '@/views/data-visual/components/dashboard-detail'
import dashboardDetail from '@/views/data-visual/components/dashboard-detail-batch-save'
import dashboardReport from '@/views/data-visual/components/dashboard-detail-report'
import addGroup from '@/views/data-visual/components/add-group'

export default {
  components: { tableList, addGroup, dashboardDetail, dashboardReport },
  data() {
    return {
      tags: [
        { name: '图表列表', id: 0, close: false }
      ],
      lastTagId: 0,
      currentTagId: 0,
      queryParams: {
        name: undefined
      },
      total: 0,
      tableData: [],
      listLoading: false,
      addGroupVisible: false,
      curTableDetail: {},
      dictType: [],
      timer: null,
      permissionArr: this.$store.getters.tablePermissions,
      tablePermission: null
    }
  },
  watch: {

  },
  created() {
    this.getList()
  },
  beforeDestroy() {
  },
  methods: {
    // 表格详情页保存操作
    changeTable(data) {
      this.curTableDetail = data
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    openChildGroup(row) {
      this.currentNodeId = row.id
      this.addGroupVisible = true
    },
    addRootGroup() {
      this.currentNodeId = ''
      this.addGroupVisible = true
    },
    // 关闭分组弹框
    closeAddGroup() {
      this.addGroupVisible = false
    },
    // 查询
    searchTable() {

    },
    // 获取表格数据
    getList(targetId, parentId, name) {
      this.listLoading = true
      getList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.tableData = res.data
          this.total = res.total
          // 如果是新增则展开刚才新增的表格数据
          if (targetId || parentId) {
            this.loopExpandTree(targetId, parentId, name)
          }
        }
      }).finally(() => {
        this.listLoading = false
      })
    },
    // 需要循环遍历该节点上的所有父节点并一一展开
    loopExpandTree(id, parentId, name) {
      const addRow = this.tableData.find(item => item.id === id || (!id && item.name === name && item.parentId === parentId))
      if (addRow.parentId !== 0) {
        const expandRow = this.tableData.find(item => item.id === addRow.parentId)
        if (expandRow && expandRow.id) {
          if (this.$refs.treeTable) {
            // this.$refs.treeTable.setAllTreeExpand(false)
            this.$refs.treeTable.expandTree(expandRow)
          }
          this.loopExpandTree(expandRow.id)
        }
      }
      this.$refs.treeTable.setCurrentRow(addRow)
    },
    // 新增分组
    handleAddGroup(form) {
      const params = this.currentNodeId ? {
        name: form.name,
        parentId: this.currentNodeId,
        type: 'GROUP'
      } : {
        name: form.name,
        type: 'GROUP'
      }
      addDashboard(params).then(res => {
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.closeAddGroup()
          this.getList()
        }
      })
    },
    // 点击tag
    clickTag(tag) {
      if (tag.id !== 0) {
        this.lastTagId = tag.id
      }
      this.currentTagId = tag.id
      this.curTableDetail = this.tableData.find(item => item.id === tag.id) || {}
      if (tag.id === 0) {
        this.loopExpandTree(this.lastTagId)
      }
    },
    closeTag(tag) {
      // 如果关闭的tag在列表中高亮则先清除列表高中状态
      if (tag.id === this.lastTagId) {
        this.$refs.treeTable.clearCurrentRow()
      }
      this.tags = this.tags.filter((item) => item.id !== tag.id)
      if (this.currentTagId !== 0) {
        this.currentTagId = this.tags.length === 1 ? 0 : this.tags[1].id
        this.clickTag({ id: this.currentTagId })
      }
    },
    // 打开数据表(打开一个表新开一个tab)
    openTableDetail(row) {
      const { id, name } = row
      const dashboardName = name
      const len = this.tags.length
      const target = this.tags.find(item => item.id === id)
      if (len === 1 || !target) {
        this.tags.push({ name: dashboardName, id: id, close: true })
      }
      this.currentTagId = id
      this.lastTagId = id
      this.curTableDetail = row
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.tag-box {
  margin-bottom:10px;
}
</style>
