<template>
  <el-dialog class="add-table" :title="diaTitle" :visible="true" width="1000px" :close-on-click-modal="false" @close="closeDialog">
    <!-- 查询选择区域 start-->
    <div class="search-select">
      <div class="search">
        <el-form ref="addForm" :model="queryParams" size="small" :inline="true" :rules="addTableRules">
          <el-form-item label="表格名称" prop="tableName">
            <el-input
              v-model="queryParams.tableName"
              placeholder="请输入表格名称"
              clearable
            />
          </el-form-item>
          <el-form-item v-if="isAddChildTable" label="父表入口列" prop="parentCol">
            <el-select v-model="queryParams.parentCol" @change="changeParentCol">
              <el-option
                v-for="item in parentColArr"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="建表方式" prop="createMode">
            <el-radio-group v-model="queryParams.createMode">
              <el-radio label="MANUAL">手动添加设置列</el-radio>
              <el-radio label="AUTO">数据源自动设置列</el-radio>
              <el-radio v-if="!isAddChildTable" label="POLY">存量表格聚合生成列</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

    </div>
    <!-- 查询选择区域 end -->
    <div class="table-wrapper">
      <!-- 数据源生成列 -->
      <div v-show="queryParams.createMode==='AUTO'" class="content">
        <el-form ref="viewForm" :model="viewForm" size="small" :inline="true" :rules="viewFormRules">
          <el-form-item label="源数据视图" prop="viewId">
            <el-select v-model="viewForm.viewId" @change="fetchData">
              <el-option
                v-for="item in originDataArr"
                :key="item.id"
                :label="item.viewName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="static-table">
          <vxe-table
            ref="xTable"
            border
            height="auto"
            size="mini"
            :data="viewTable"
            :loading="loading"
            show-overflow
            :column-config="{isCurrent: true, isHover: true}"
            :row-config="{isCurrent: true, isHover: true}"
          >
            <vxe-column
              v-for="(config,index) in tableMetaJson"
              :key="config.code"
              :field="config.code"
              :title="config.name"
              :align="formatAlign(config.dataFormat)"
              show-overflow
              width="180"
            >
              <template #header="{ column }">
                <div slot="reference" class="flex-vertical-center custom-col" @click="clickCol(column)">
                  <div class="flex-vertical-center col-left">
                    <i v-if="index===0" class="iconfont icon-yuechi1" />
                    <i v-if="config.systemKey==='ORG' || config.systemKey==='USER'" class="vxe-icon-user" />
                    <i v-if="typeof config.condition === 'boolean'" class="iconfont icon-shaixuan" />
                    <span class="custom-ellipsis dynamic-col-title">{{ config.name }}</span>
                  </div>
                  <el-popover
                    placement="bottom-start"
                    width="200"
                    trigger="click"
                  >
                    <div class="popver-box">
                      <div class="row-cell">
                        <el-input v-model="config.name" size="small" />
                      </div>
                      <div class="row-cell">
                        <el-cascader
                          v-model="config.dataFormatId"
                          size="small"
                          placeholder="选择数据格式"
                          :options="formatTree"
                          :props="formatProps"
                          :show-all-levels="false"
                          @change="changeFormatTree"
                        />
                      </div>
                      <div class="row-cell">
                        <el-select v-model="config.condition" size="small" placeholder="选择筛选条件">
                          <el-option
                            v-for="item in filterOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </div>
                      <div class="row-cell">
                        <el-select v-model="config.systemKey" size="small" placeholder="关联系统参数">
                          <el-option
                            v-for="item in relateOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </div>
                      <div v-if="config.no>1" class="row-cell setcol-btn">
                        <el-button class="del-btn" type="text" @click="delColumn">删除列</el-button>
                      </div>
                    </div>
                    <i slot="reference" class="iconfont icon-new-setting" />
                  </el-popover>
                </div>
              </template>

            </vxe-column>
            <template #empty>
              <table-empty />
            </template>
          </vxe-table>
        </div>
        <div class="table-footer">
          <el-pagination
            :current-page="pageInfo.pageNum"
            :page-sizes="[20,50,100]"
            :page-size="pageInfo.pageSize"
            layout="total"
            :total="totalRecords"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <!-- 存量表格聚合生成列 -->
      <div v-show="queryParams.createMode==='POLY'" class="content">
        <el-form ref="polyForm" :model="polyForm" size="small" :inline="true" :rules="polyFormRules">
          <el-form-item label="源二维表" prop="tableId">
            <el-cascader
              v-model="polyForm.tableId"
              class="custom-cascader"
              size="small"
              placeholder="请选择二维表"
              :options="polyDataArr"
              :props="formatTableProps"
              :show-all-levels="true"
              @change="fetchTable"
            />
          </el-form-item>
          <!-- <el-form-item label="二维表" prop="tableId">
            <el-cascader
              v-model="polyForm.tableId"
              size="small"
              placeholder="请选择二维表"
              :options="polyDataArr"
              :props="formatTableProps"
              :show-all-levels="true"
              @change="fetchTable"
            />
          </el-form-item> -->
        </el-form>
        <div class="poly-table">
          <vxe-table
            ref="xTable2"
            border
            height="auto"
            size="mini"
            :data="polyTable"
            :loading="polyLoading"
            show-overflow
            :column-config="{isCurrent: true, isHover: true}"
            :row-config="{isCurrent: true, isHover: true}"
          >
            <vxe-column
              v-for="(config) in polyMetaJson"
              :key="config.code"
              :field="config.code"
              :title="config.name"
              :align="formatAlign(config.dataFormat)"
              show-overflow
              width="180"
            >
              <template #header="{ }">
                <div class="flex-vertical-center custom-col">
                  <div class="flex-vertical-center col-left">
                    <span class="custom-ellipsis dynamic-col-title">{{ config.name }}</span>
                  </div>
                  <div class="right-title">
                    <div class="text">{{ formatPopTitle(config) }}</div>
                    <div class="el-icon-circle-plus-outline dimension-icon add" @click="addDimension(config)" />
                  </div>
                </div>
              </template>
            </vxe-column>
            <!-- <template #empty>
              <table-empty />
            </template> -->
          </vxe-table>
        </div>
        <!-- 分析维度列和聚合指标列 -->
        <div class="dimension-area">
          <!-- 分析维度列 -->
          <div class="dimension-col">
            <div class="dimension-title">分析维度列</div>
            <div class="dimension-table">
              <vxe-table
                ref="xTable3"
                border
                height="auto"
                size="mini"
                :data="[]"
                show-overflow
                :column-config="{isCurrent: true, isHover: true}"
                :row-config="{isCurrent: true, isHover: true}"
              >
                <vxe-column
                  v-for="(config) in analysisDimCol"
                  :key="config.code"
                  :field="config.code"
                  :title="config.name"
                  show-overflow
                  width="180"
                >
                  <template #header="{ }">
                    <div slot="reference" class="flex-vertical-center custom-col">
                      <div class="flex-vertical-center col-left">
                        <span class="custom-ellipsis dynamic-col-title">{{ config.name }}</span>
                      </div>
                      <div class="el-icon-remove-outline dimension-icon del" @click="delDimension(config)" />
                    </div>
                  </template>
                </vxe-column>
                <!-- <template #empty>
                  <table-empty />
                </template> -->
              </vxe-table>
            </div>
          </div>
          <!-- 聚合指标列 -->
          <div class="dimension-col">
            <div class="dimension-title">聚合指标列</div>
            <div class="dimension-table">
              <vxe-table
                ref="xTable4"
                border
                height="auto"
                size="mini"
                :data="[]"
                show-overflow
                :column-config="{isCurrent: true, isHover: true}"
                :row-config="{isCurrent: true, isHover: true}"
              >
                <vxe-column
                  v-for="(config) in polyTargetCol"
                  :key="config.code"
                  :field="config.code"
                  :title="config.name"
                  show-overflow
                  width="180"
                >
                  <template #header="{ }">
                    <div slot="reference" class="flex-vertical-center custom-col">
                      <div class="flex-vertical-center col-left">
                        <span class="custom-ellipsis dynamic-col-title">{{ config.name }}</span>
                      </div>
                      <el-popover
                        placement="bottom-start"
                        width="200"
                        trigger="click"
                      >
                        <div class="popver-box">
                          <div class="row-cell">
                            <el-input v-model="config.name" size="small" />
                          </div>
                          <div class="row-cell">
                            <el-cascader
                              v-model="config.funcVal"
                              size="small"
                              placeholder="请选择聚合函数"
                              :options="polyFuncList"
                              :props="polyProps"
                              @change="(val)=>changePolyTree(val,config)"
                            />
                          </div>
                          <div v-if="config.no>1" class="row-cell setcol-btn">
                            <el-button class="del-btn" type="text" @click="delPolyCol(config)">删除列</el-button>
                          </div>
                        </div>
                        <i slot="reference" class="el-icon-setting font-15" @click="changePolyFuncList(config)" />
                      </el-popover>
                    </div>
                  </template>
                </vxe-column>
                <!-- <template #empty>
                  <table-empty />
                </template> -->
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
      <div v-show="queryParams.createMode==='MANUAL'" class="content">
        <div class="cus-header">
          <el-button plain type="primary" size="mini" icon="el-icon-plus" @click="addColumn">添加列</el-button>
          <!-- <el-button plain type="danger" size="mini" icon="el-icon-delete" @click="delLastColumn">删除最后一列</el-button> -->
        </div>
        <dynamic-col :dynamic-column="handCol" @changeFormat="changeHandColFormat" @delColumn="delHandCol" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="closeDialog">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleAddTable">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import VXETable from 'vxe-table'
import { cloneDeep } from 'lodash'
import { loadView } from '@/api/system/data-view'
import { getTableCols, getTableKey, addPolyTable, getPolyFuncList, getList, getTableDetail, get2dTable, addTable, loadViewData, getParentCol, addChildTable } from '@/api/base-table'
import dynamicCol from './dynamic-col.vue'

export default {
  components: {
    dynamicCol
  },
  props: {
    currentNodeData: {
      type: Object,
      default: () => {}
    },
    originDataArr: {
      type: Array,
      default: () => []
    },
    dictType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      queryParams: {
        tableName: '',
        createMode: 'AUTO',
        parentCol: ''
      },

      viewFormRules: {
        viewId: [
          { required: true, message: '请选择源数据视图', trigger: ['blur', 'change'] }
        ]
      },
      viewForm: {
      // 源数据id
        viewId: ''
      },
      polyFormRules: {
        tableId: [
          { required: true, message: '请选择二维表', trigger: ['blur', 'change'] }
        ]
      },
      polyLoading: false,
      polyForm: {
        tableId: ''
      },
      polyTable: [],
      polyDataArr: [],
      showFilter: false,
      // 数据源加载列
      tableMetaJson: null,
      polyMetaJson: null,
      pageInfo: {
        pageNum: 1,
        pageSize: 20
      },
      totalRecords: 0,
      curCol: null,
      filterForm: {
        isEmpty: false,
        search: false,
        dataRange: ''
      },
      handCol: [],
      viewTable: [],
      sumCount: 0,
      avageCount: 0,
      currentNo: 1,
      filterOption: Object.freeze([{
        label: '不作为查询条件',
        value: null
      }, {
        label: '精确查询',
        value: false
      }, {
        label: '模糊查询',
        value: true
      }]),
      relateOption: Object.freeze([{
        label: '不关联系统参数',
        value: null
      }, {
        label: '关联机构',
        value: 'ORG'
      }, {
        label: '关联人员',
        value: 'USER'
      }]),
      parentColArr: [], // 父表入口列
      curParentCol: null,
      formatTree: this.$store.getters.formatListTree,
      formatList: this.$store.getters.formatList,
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'groupInstanceName' },
      polyProps: { emitPath: true, expandTrigger: 'hover', value: 'funcVal', label: 'funcName', children: 'ruleFuncList' },
      defaultFormatId: '3b583866df798d08b8dd811f1f14fe0a',
      targetType: Object.freeze(['INTEGER', 'DOUBLE', 'DATE']),
      analysisDimCol: [],
      polyTargetCol: [],
      polyFuncList: [],
      parentTableKeyCol: null,
      formatTableProps: { checkStrictly: true, emitPath: false, expandTrigger: 'hover', value: 'id', label: 'tableName' }
    }
  },
  computed: {
    isAddChildTable() {
      return this.currentNodeData.type === 'TABLE'
    },
    // ancestorsLen() {
    //   return this.currentNodeData.ancestors.split(',').length
    // },
    diaTitle() {
      return this.isAddChildTable ? '新增子表' : '新增表格'
    },
    addTableRules() {
      var checkName = (rule, value, callback) => {
        const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
        if (!value) {
          return callback(new Error('请输入名称'))
        }
        if (value && !reg.test(value)) {
          return callback(new Error('名称只能为汉字、英文、数字、下划线的组合'))
        }
        callback()
      }
      return this.isAddChildTable ? {
        tableName: [
          { required: true, validator: checkName, trigger: 'blur' }
        ],
        parentCol: [
          { required: true, message: '请选择入口列', trigger: ['blur', 'change'] }
        ]
      } : {
        tableName: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      }
    },
    defaultFormat() {
      return this.formatList.filter(item => item.id === this.defaultFormatId)[0]
    }
  },
  watch: {
    'queryParams.createMode'(newVal) {
      this.queryParams.tableName = ''
      this.queryParams.parentCol = ''
      // this.viewForm.viewId = ''
      if (this.$refs.addForm) this.$refs.addForm.clearValidate(['tableName', 'parentCol'])
      if (this.$refs.viewForm) this.$refs.viewForm.clearValidate('viewId')
      if (newVal === 'POLY') {
        // 获取二维表
        this.get2dList()
      }
      // 如果是手动添加子表则handCol要加上父表主键
      if (newVal === 'MANUAL' && this.isAddChildTable) {
        this.getAllCol()
      }
    }
  },
  created() {
    this.initHandCol()
    if (this.isAddChildTable) {
      this.getParentColArr()
      this.getAllCol()
      // this.getParentTableKey()
    }
  },
  methods: {
    formatAlign(val) {
      let align = 'left'
      if (val) {
        align = val.contentAlign === 'middle' ? 'center' : val.contentAlign
      }
      return align
    },
    // 递归树列表
    listToTree(list) {
      const obj = {}
      list.forEach(item => { obj[item.id] = item })
      const res = []
      list.forEach(item => {
        const parent = obj[item.parentId]
        // 如果parent存在则item是叶子节点，否则就是根节点
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(item)
        } else {
          res.push(item)
        }
      })
      return res
    },
    // 获取父表主键
    getParentTableKey() {
      getTableKey({ id: this.currentNodeData.id }).then(res => {
        if (res.code === 200) {
          this.initHandCol()
          this.parentTableKeyCol = res.data
          this.handCol.unshift(res.data)
        }
      })
    },
    getAllCol() {
      getTableCols(this.currentNodeData.id).then(res => {
        if (res.code === 200) {
          if (res.code === 200) {
            this.initHandCol()
            const tableMetaJson = res.data.tableMetaJson
            let arr = []
            const { type, tableLevel } = this.currentNodeData
            if (type === 'TABLE') {
              if (tableLevel === 0) {
                arr = tableMetaJson.slice(0, 1)
              } else if (tableLevel === 1) {
                arr = tableMetaJson.slice(0, 2)
              }
            }
            this.handCol = arr.concat(this.handCol)
          }
        }
      })
    },
    // 获取聚合函数列表
    changePolyFuncList(config) {
      const avgPolyCol = []
      this.polyMetaJson.forEach((col) => {
        const storageType = col.dataFormat.storageType
        if (['INTEGER', 'DOUBLE'].includes(storageType) && (col.code !== config.code)) {
          const { code, name } = col
          avgPolyCol.push({
            funcVal: code,
            funcName: name
          })
        }
      })
      getPolyFuncList().then(res => {
        if (res.code === 200) {
          this.polyFuncList = this.formatCascaderTree(res.data)
          this.polyFuncList.forEach(item => {
            if (item.funcVal === 'vavgw()') {
              item.ruleFuncList = avgPolyCol
            }
          })
          console.log(this.polyFuncList)
        }
      })
    },
    formatCascaderTree(arr) {
      if (!arr || !arr.length) {
        return []
      }
      arr.forEach(item => {
        if (!item.ruleFuncList.length) {
          delete item.ruleFuncList
        }
      })
      return arr
    },
    // 选择聚合函数
    changePolyTree(val, config) {
      const { code } = config
      const funcTarget = this.polyFuncList.find(item => item.funcVal === val[0])
      const { funcName } = funcTarget
      this.polyTargetCol.forEach(col => {
        if (col.code === code) {
          col.functionValue = val[0]
          col.functionName = funcName
          col.avgParmaColumnCode = val[0] === 'vavgw()' ? val[1] : ''
        }
      })
    },
    // 添加到指标或者维度列
    addDimension(config) {
      const deepConfig = cloneDeep(config)
      const formatTitle = this.formatPopTitle(deepConfig)
      if (formatTitle === '维度') {
        if (!this.analysisDimCol.find(item => item.code === deepConfig.code)) {
          this.analysisDimCol.push(deepConfig)
        } else {
          this.$message.error('分析维度列已添加,请勿重复添加')
        }
      } else {
        if (!this.polyTargetCol.find(item => item.code === deepConfig.code)) {
          this.polyTargetCol.push(deepConfig)
        } else {
          this.$message.error('聚合指标列已添加,请勿重复添加')
        }
      }
    },
    // 删除分析维度列
    delDimension(config) {
      const { code } = config
      this.analysisDimCol = this.analysisDimCol.filter(item => item.code !== code)
    },
    // 删除聚合函数列
    delPolyCol(config) {
      const { code } = config
      this.$confirm('确定删除该列吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.polyTargetCol = this.polyTargetCol.filter(item => item.code !== code)
      })
    },
    get2dList() {
      getList({ }).then(res => {
        if (res.code === 200) {
          res.data.forEach(item => {
            item.disabled = item.type === 'GROUP'
          })
          this.polyDataArr = this.listToTree(res.data)
        }
      })
    },
    formatPopTitle(config) {
      const colType = config?.dataFormat?.storageType
      return this.targetType.includes(colType) ? '指标' : '维度'
    },
    initHandCol() {
      const colName = this.isAddChildTable ? '子表列名' : '表格列名'
      this.handCol = [{ no: 1, name: colName, dataFormatId: this.defaultFormatId, dataFormat: this.defaultFormat, newColumnType: 'VARCHAR', oldColumnType: 'VARCHAR', condition: null, systemKey: null }]
    },
    // 自动建表改变格式
    changeFormatTree(val) {
      const formatList = [...this.formatList]
      const target = formatList.filter(item => item.id === val)[0]
      this.tableMetaJson.forEach(col => {
        if (col.dataFormatId === val) {
          col.dataFormat = target
        }
      })
      console.log(val, this.tableMetaJson)
    },
    // 手动建表改变数据格式
    changeHandColFormat(val, index) {
      const target = this.formatList.filter(item => item.id === val)[0]
      this.handCol[index].dataFormat = target
      this.handCol[index].dataFormatId = target.id
    },
    async importData() {
      try {
        const { file } = await VXETable.readFile({
          types: ['xlsx', 'xls']
        })
        VXETable.modal.alert(`文件名：${file.name}，文件大小：${file.size}`)
        console.log(66666, file)
      } catch (e) {
        console.log(e)
      }
    },
    // 加载数据源
    fetchData() {
      if (!this.viewForm.viewId) {
        this.$message.error('请先选择数据源')
        return
      }
      const params = new FormData()
      params.append('id', this.viewForm.viewId)
      this.loading = true
      loadView(params).then(res => {
        if (res.code === 200) {
          this.tableMetaJson = res.data
          this.loadViewData()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 加载数据源行数据
    loadViewData() {
      this.loading = true
      loadViewData(this.viewForm.viewId).then(res => {
        if (res.code === 200) {
          const { result, totalCount } = res.data
          this.viewTable = result
          this.totalRecords = totalCount
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 处理listMemData接口数据
    formatListMem(arr, type) {
      const validArr = []
      arr && arr.forEach(item => {
        const { cells } = item
        const obj = {}
        cells.forEach(cell => {
          const { code } = cell
          obj[code] = cell[type] || cell['originalValue']
        })
        validArr.push(obj)
      })
      return validArr
    },
    // 加载二维表
    async fetchTable(id) {
      this.polyLoading = true
      await getTableDetail(id).then(async res => {
        if (res.code === 200) {
          const { tableMetaJson } = res.data
          this.polyMetaJson = tableMetaJson
          await this.load2dTable(id)
        }
      }).finally(() => {
        this.polyLoading = false
      })
    },
    async load2dTable(id) {
      const params = {
        pageNum: 1,
        pageSize: 10,
        tableId: id,
        sortList: [],
        filterList: []
      }
      this.polyLoading = true
      await get2dTable(params).then(res => {
        if (res.code === 200) {
          const arr = res.rows && res.rows[0] && res.rows[0].rows
          if (arr && arr.length) {
            this.polyTable = this.formatListMem(arr, 'displayValue')
            this.totalRecords = res.total
          } else {
            // 如果没有行数据则添加一行空数据
            this.polyTable = []
          }
        }
      }).finally(() => {
        this.polyLoading = false
      })
    },

    // 获取父表入口列
    getParentColArr() {
      const { id } = this.currentNodeData
      getParentCol({ id }).then(res => {
        if (res.code === 200) {
          this.parentColArr = res.data && res.data.filter(item => item.name !== 'id')
        }
      })
    },
    handleQuery() {},
    resetQuery() {},
    closeDialog() {
      this.$emit('close')
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val
      console.log(`当前页: ${val}`)
    },
    handleAddTable() {
      const { createMode } = this.queryParams
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (createMode === 'AUTO') {
        if (!this.viewForm.viewId) {
          this.$message.error('请加载源数据视图')
          return
        }
        let flag1 = true
        let tips1 = ''

        this.tableMetaJson.forEach(col => {
          const { name } = col
          if (!name) {
            flag1 = false
            tips1 = '列名不能为空'
            return
          }
          if (name && !reg.test(name)) {
            flag1 = false
            tips1 = '列名只能为汉字、英文、数字、下划线的组合'
            return
          }
        })
        if (!flag1) {
          this.$message.error(tips1)
          return
        }
      }
      if (createMode === 'MANUAL') {
        if (this.handCol.length < this.ancestorsLen) {
          this.$message.error(`请至少新增${this.ancestorsLen}列`)
          return
        }
        let flag = true
        let tips = ''
        this.handCol.forEach(col => {
          const { name } = col
          if (!name) {
            flag = false
            tips = '列名不能为空'
            return
          }
          if (name && !reg.test(name)) {
            flag = false
            tips = '列名只能为汉字、英文、数字、下划线的组合'
            return
          }
        })
        if (!flag) {
          this.$message.error(tips)
          return
        }
      }
      if (createMode === 'POLY') {
        if (!this.polyForm.tableId) {
          this.$message.error('请加载二维表')
          return
        } else {
          if (this.polyTargetCol.some(col => !col.functionValue)) {
            this.$message.error('聚合指标列必须选择聚合函数')
            return
          }
        }
      }
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          if (createMode === 'POLY') {
          // 新增聚合表
            const dimColumnList = this.analysisDimCol.map(item => {
              const { code, name } = item
              return {
                dimColumnCode: code,
                dimColumnName: name
              }
            })
            const indColumnList = this.polyTargetCol.map(item => {
              const { code, name, functionName, functionValue, avgParmaColumnCode } = item
              return {
                avgParmaColumnCode,
                functionName,
                functionValue,
                indColumnCode: code,
                indColumnName: name
              }
            })
            const polyAddParams = {
              dimColumnList,
              indColumnList,
              relationTableId: this.polyForm.tableId,
              tableName: this.queryParams.tableName
            }
            addPolyTable(polyAddParams).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.$emit('addSuccess', res.data)
              }
            })
          } else {
            const validHandCol = cloneDeep(this.handCol)
            validHandCol.forEach(item => {
              const { storageType } = item.dataFormat
              item.newColumnType = storageType
              item.oldColumnType = storageType
            })
            const baseParams = {
              tableName: this.queryParams.tableName,
              parentId: this.currentNodeData.id,
              tableMetaJson: createMode === 'AUTO' ? this.tableMetaJson : validHandCol,
              type: 'TABLE',
              createMode: createMode,
              viewId: createMode === 'AUTO' ? this.viewForm.viewId : undefined
            }
            let addChildTableParams = {}
            if (this.isAddChildTable) {
              const { name, code, no } = this.curParentCol
              addChildTableParams = {
                parentTableId: this.currentNodeData.id,
                parentTableMetaCode: code,
                parentTableMetaName: name,
                parentTableMetaNo: no
              }
            }
            const validParams = { ...baseParams, ...addChildTableParams }
            const validFunc = this.isAddChildTable ? addChildTable : addTable
            validFunc(validParams).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.$emit('addSuccess', res.data)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 获取所选父表列
    changeParentCol(val) {
      this.curParentCol = this.parentColArr.find(item => item.code === val)
    },
    clickCol(col) {
      console.log('当前点击列', col)
      this.curCol = col
    },
    addColumn() {
      const colName = this.isAddChildTable ? '子表列名' : '表格列名'
      this.currentNo++
      this.handCol.push({ no: this.currentNo, name: colName, dataFormatId: this.defaultFormatId, dataFormat: this.defaultFormat, newColumnType: 'VARCHAR', oldColumnType: 'VARCHAR', condition: null, systemKey: null, systemValue: null })
    },
    // 删除最后一列(手动建表)
    delLastColumn() {
      if (this.handCol.length > 1) {
        if (this.tableMetaJson && this.tableMetaJson.length > 1) {
          this.$confirm('确定删除最后一列吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.currentNo--
            this.handCol.pop()
          })
        }
      }
    },
    // 删除最后一列(自动建表)
    delAutoLast() {
      if (this.tableMetaJson && this.tableMetaJson.length > 1) {
        this.$confirm('确定删除最后一列吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tableMetaJson.pop()
        })
      }
    },
    // 删除放在设置框里(自动建表)
    delColumn() {
      if (this.tableMetaJson.length <= 1) {
        this.$message.error('请至少保留一列')
        return
      }
      if (this.curCol) {
        this.$confirm('确定删除该列吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const curField = this.curCol.field
          this.tableMetaJson = this.tableMetaJson.filter(item => item.code !== curField)
        })
      } else {
        this.$message.error('请选择一列进行删除')
      }
    },
    //  删除放在设置框里(手动建表)
    delHandCol(config) {
      this.$confirm('确定删除该列吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentNo--
        this.handCol = this.handCol.filter(item => item.no !== config.no)
      })
    },
    openSetting() {
      console.log(1)
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.add-table {
  ::v-deep .el-dialog__body {
    min-height: 400px;
    padding:10px 20px;
  }
}
.cus-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 55px;
  div {
    margin: 0 5px;
  }
  button {
    margin-left: 20px;
  }
}

.filter-form {
  ::v-deep .el-form-item {
    margin-bottom: 0px;
  }
}
.select-table {
  display: flex;
  .label {
    margin-right: 10px;
  }
}
.table-wrapper {
  height: 400px;
  .content {
    height: 100%;
  }
  .static-table {
    height: 300px;
  }
  // .dynamic-table {
  //   height: calc(100% - 80px)
  // }
}
.dynamic-table {
  display: flex;
  width: 100%;
  overflow: auto;
}
.poly-table {
  height: 170px;
}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .sum-avg {
    font-size: 13px;
    margin-right: 20px;
    .label {
      color: $formLabel;
    }
  }
}
.custom-col {
  justify-content: space-between;
  i {
    cursor: pointer;
    color: $formLabel;
  }
}
.popver-box {
  text-align: left;
  .popver-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    .title {
      margin: 0 10px;
      &.active {
        color: $primary;
        cursor: pointer;
      }
    }

  }
}
  .row-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 44px;
    padding: 5px;
    .el-checkbox {
      margin-right: 10px;
    }
  }
  .col-left {
    span {
      margin-left: 2px;
    }
    i {
      color: $formLabel;
    }
  }
  .setcol-btn {
    height: 24px;
    .el-button {
      font-size: 13px;
    }
    .del-btn {
      color: $mainRed
    }
  }
  .add-dimension-popover {
    display: flex;
    align-items: center;

  }

.dimension-area {
  display: flex;
  .dimension-col {
    flex: 1;
    margin: 0 10px;
    overflow-x: auto;
    .dimension-title {
      font-size: 14px;
      height: 34px;
      line-height: 34px;
      color: $menuActiveText;
      font-weight: bold;
    }
    .dimension-table {
      height: 120px;
      ::v-deep .body--wrapper {
        overflow-y: hidden;
      }
    }
  }
}
.dimension-icon {
  cursor: pointer;
  font-size: 16px;
  &.add {
    color: $primary;
  }
  &.del {
    color: $mainRed;
  }
}
.right-title {
  display: flex;
  justify-content: center;
  align-items: center;
  .text {
    margin-right: 2px;
    color: #222;
  }
}
.dynamic-col {
  border-right: 1px solid #ddd;
}
.custom-cascader {
  width: 400px;
}
</style>
