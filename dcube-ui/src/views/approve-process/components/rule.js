import {checkRule} from '@/api/base-table'

const validateRule = (form, tableId, columnCode, type, dimTableId, dimRuleId, indId) => async (rule, value, callback) => {
  if (!form.rule) {
    callback()

  } else {
    const params = new FormData()
    params.append('express', JSON.stringify(form.rule))
    params.append('tableId', tableId)
    params.append('columnCode', columnCode)
      if (type === 'cube') {
          params.append('type', type)
          params.append('dimTableId', dimTableId)
          params.append('dimRuleId', dimRuleId)
          params.append('indId', indId)
      }
      console.log('validate', params)
    const result = await checkRule(params)
    if (result.code !== 200) {
      callback(new Error(result.msg))

    }
  }
}

const rules = {
    createCalcRule: (form, tableId, columnCode, type, dimTableId, dimRuleId, indId) => ({
    rule: [{
        validator: validateRule(form, tableId, columnCode, type, dimTableId, dimRuleId, indId),
      trigger: 'blur'
    }]
  })
}

export default rules
