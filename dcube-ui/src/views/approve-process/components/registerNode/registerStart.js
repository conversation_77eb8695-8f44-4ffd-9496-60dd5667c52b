export default function registerStart (lf) {
  lf.register('start', ({ CircleNode, CircleNodeModel, h }) => {
    class StartNode extends CircleNode {
      getIconShape () {
        const {model} = this.props
        const {
          x,
          y,
          width,
          height
        } = model
        const stroke = '#0070c0'
        return h(
          'svg',
          {
            x: x - 22,
            y: y - 25,
            width: 50,
            height: 50,
            viewBox: '0 0 1024 1024'
          },
          h(
            'path',
            {
              fill: stroke,
              d: 'M256.079818 160.365384 256.079818 863.633592 808.649815 511.983627Z'
            }
          )
        )
      }
      getShape () {
        const {model} = this.props
        const {
          x,
          y,
          r,
        } = model
        const {
          fill,
          stroke,
          strokeWidth} = model.getNodeStyle()
        return h(
          'g',
          {
          },
          [
            h(
              'circle',
              {
                cx: x,
                cy: y,
                r,
                fill,
                stroke:'#0070c0',
                strokeWidth
              }
            ),
            this.getIconShape()
          ]
        )
      }
    }
    class StartModel extends CircleNodeModel {
      // 自定义节点形状属性
      initNodeData(data) {
        data.text = {
          value: (data.text && data.text.value) || '',
          x: data.x,
          y: data.y + 48,
          dragable: false,
          editable: true
        }
        super.initNodeData(data)
        this.r = 30
      }
      // 自定义节点样式属性
      getNodeStyle() {
        const style = super.getNodeStyle()
        return style
      }
      // 自定义锚点样式
      getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.hover.r = 8;
        style.hover.fill = "rgb(24, 125, 255)";
        style.hover.stroke = "rgb(24, 125, 255)";
        return style;
      }
      // 自定义节点outline
      getOutlineStyle() {
        const style = super.getOutlineStyle();
        style.stroke = '#88f'
        return style
      }
    }
    return {
      view: StartNode,
      model: StartModel
    }
  })
}
