<template>
  <div class="property-dialog">
    <LineProperty
      v-if="nodeData.type === 'polyline'"
      :nodeData="nodeData"
      :lf="lf"
      @onClose="handleClose"/>
    <CommonProperty
      v-else
      :nodeData="nodeData"
      :lf="lf"
      :flowId="flowId"
      @onClose="handleClose"/>
  </div>
</template>
<script>
import CommonProperty from './CommonProperty'
import LineProperty from './LineProperty.vue'

export default {
  name: 'PropertyDialog',
  components: {
    CommonProperty,
    LineProperty
  },
  props: {
    nodeData: Object,
    lf: Object,
    flowId: String
  },
  data () {
    return {}
  },
  methods: {
    handleClose () {
      this.$emit('setPropertiesFinish')
    }
  }
}
</script>
<style>
.property-dialog{
  padding: 20px;
}
</style>
