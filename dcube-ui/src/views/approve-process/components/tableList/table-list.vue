<template>
  <div class="table-list">
    <vxe-table
      ref="xTree"
      size="mini"
      height="auto"
      :loading="loading"
      show-overflow
      :tree-config="treeConfig"
      :data="tableData"
      :row-config="{isCurrent: true, isHover: true}"
    >
      <vxe-column field="flowName" title="名称" tree-node show-overflow width="350">
        <template #default="{ row }">
          <span class="flex-vertical-center custom-node" :class="row.type==='FLOW'?'table-node':''" @click="clickTreeNode(row)">
            <template v-if="row.type==='GROUP'">
              <i class="tree-node-icon" :class="$refs.xTree.isTreeExpandByRow(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'" />
            </template>
            <template v-else>
              <i class="iconfont icon-liucheng" />
            </template>
            <div class="custom-ellipsis">{{ row.flowName }}</div>
          </span>
        </template>
      </vxe-column>
      <vxe-column field="flowTypeName" title="流程类型" />
      <vxe-column field="tableName" title="表名" />
      <vxe-column field="createTime" title="创建日期" />
      <vxe-column field="updateTime" title="修改日期" />
      <vxe-column title="操作" width="450">
        <template #default="{ row }">
          <el-button
            v-if="row.type==='GROUP'"
            size="mini"
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="addChildGroup(row)"
          >新增子组</el-button>
          <el-button
            v-if="row.type==='GROUP'"
            size="mini"
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="addFlow(row)"
          >新增流程</el-button>
          <el-button
            type="text"
            status="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="openReName(row)"
          >重新命名</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="delNode(row)"
          >删除</el-button>
          <el-button
            v-if="row.type==='FLOW' && row.flowType==='CUBE'"
            type="text"
            size="mini"
            icon="el-icon-video-play"
            @click="handleStartProcess(row)"
          >发起流程</el-button>
          <el-button
            v-if="row.type==='FLOW' && row.flowType==='CUBE'"
            type="text"
            size="mini"
            icon="el-icon-switch-button"
            @click="handleStopProcess(row)"
          >终止流程</el-button>
        </template>
      </vxe-column>
      <template #empty>
        <table-empty />
      </template>
    </vxe-table>

    <!-- 重命名弹框 -->
    <el-dialog title="重新命名" :visible.sync="reNameVisible" width="400px" :close-on-click-modal="false" @close="closeReName">
      <el-form ref="reNameForm" label-position="right" label-width="60px" size="small" :model="reNameForm" :rules="reNameRules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="reNameForm.name" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeReName">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleReName">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增审批流程" :visible="showFlowConfig" width="600px" append-to-body center :close-on-click-modal="false" @close="closeFlowConfig">
      <el-form ref="flowConfigForm" label-position="right" label-width="120px" size="small" :model="flowConfig" :rules="flowConfigRules">
        <el-form-item label="流程名称" prop="flowName">
          <el-input v-model="flowConfig.flowName" clearable />
        </el-form-item>
        <el-form-item label="流程维度" prop="flowType">
          <el-radio-group v-model="flowConfig.flowType">
            <el-radio label="TABLE">二维流程</el-radio>
            <el-radio label="CUBE">多维流程</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="flowConfig.flowType==='TABLE'" label="二维表" prop="table2dId">
          <el-cascader
            v-model="flowConfig.table2dId"
            style="width:100%"
            size="small"
            placeholder="请选择二维表"
            :options="table2dData"
            :show-all-levels="true"
            :props="formatProps"
            @change="changetable2d"
          />
        </el-form-item>
        <el-form-item v-if="flowConfig.flowType==='TABLE'" label="流程状态列" prop="statusCol">
          <el-select v-model="flowConfig.statusCol" placeholder="请选择" :style="{width:'100%'}">
            <el-option
              v-for="item in tableCol"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="flowConfig.flowType==='TABLE'" label="是否批量提交" prop="batchSubmit">
          <el-radio-group v-model="flowConfig.batchSubmit">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="flowConfig.flowType==='CUBE'" label="多维数方" prop="cubeId">
          <el-cascader
            v-model="flowConfig.cubeId"
            style="width:100%"
            size="small"
            placeholder="请选择多维表"
            :options="cubeList"
            :show-all-levels="true"
            :props="formatProps"
            @change="changeCubeId"
          />
        </el-form-item>
        <el-form-item v-if="flowConfig.flowType==='CUBE'" label="流程审批维度" prop="dimId">
          <el-select v-model="flowConfig.dimId" placeholder="请选择" :style="{width:'100%'}">
            <el-option
              v-for="item in cubeDimsList"
              :key="item.id"
              :label="item.dimDirectoryName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeFlowConfig">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddFlow">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteWorkflow,
  get2dcol,
  get2dTableList,
  getCubeTableList,
  getDimsByCubeId,
  startProcess,
  stopProcess,
  updateWorkflow
} from '@/api/approve-progress.js'
// import { getToken } from '@/utils/auth'

export default {
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    dictType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为汉字、英文、数字、下划线的组合'))
      }
      callback()
    }
    return {
      treeConfig: { transform: true, rowField: 'id', parentField: 'parentId', expandAll: true },
      currentNodeData: {},
      reNameVisible: false,
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      addTableVisivle: false,
      expandTimer: null,
      activeTimer: null,
      originDataArr: [],
      user: this.$store.getters.user,
      progress: 0,
      showFlowConfig: false,
      flowRow: {},
      flowConfig: {
        flowName: '',
        flowType: 'TABLE',
        table2dId: '',
        statusCol: '',
        batchSubmit: undefined,
        cubeId: '',
        dimId: ''
      },
      flowConfigRules: {
        flowName: [
          { required: true, message: '请输入流程名称', trigger: 'blur' }
        ],
        flowType: [
          { required: true, message: '请选择流程维度', trigger: 'change' }
        ],
        table2dId: [
          { required: true, message: '请选择二维表', trigger: 'blur' }
        ],
        statusCol: [
          { required: true, message: '请选择流程状态列', trigger: 'blur' }
        ],
        batchSubmit: [
          { required: true, message: '请选择是否批量提交', trigger: 'change' }
        ],
        cubeId: [
          { required: true, message: '请选择多维表', trigger: 'blur' }
        ],
        dimId: [
          { required: true, message: '请选择流程审批维度', trigger: 'blur' }
        ]
      },
      table2dData: [],
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'tableName' },
      tableCol: [],
      cubeList: [],
      cubeDimsList: [],
      expandAllTimer: null
    }
  },
  created() {
    get2dTableList().then(res => {
      if (res.code === 200) {
        this.table2dData = this.listToTree(res.data)
      }
    })
    getCubeTableList().then(res => {
      if (res.code === 200) {
        this.cubeList = this.listToTree(res.data)
      }
    })
  },
  beforeDestroy() {
    if (this.expandTimer) clearTimeout(this.expandTimer)
    if (this.activeTimer) clearTimeout(this.activeTimer)
    this.expandTimer = null
    this.activeTimer = null
  },
  methods: {
    // 递归树列表
    listToTree(list) {
      const obj = {}
      list.forEach(item => { obj[item.id] = item })
      const res = []
      list.forEach(item => {
        const parent = obj[item.parentId]
        // 如果parent存在则item是叶子节点，否则就是根节点
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(item)
        } else {
          res.push(item)
        }
      })
      return res
    },
    // 新增子组
    addChildGroup(row) {
      this.$emit('addChildGroup', row)
    },
    // 新增流程
    addFlow(row) {
      this.flowRow = row
      this.showFlowConfig = true
    },
    changetable2d(value) {
      const tableId = value
      get2dcol(tableId).then(res => {
        if (res.code === 200) {
          this.tableCol = res.data
        }
      })
    },
    changeCubeId(value) {
      getDimsByCubeId(value).then(res => {
        if (res.code === 200) {
          this.cubeDimsList = res.data
        }
      })
    },
    // 展开树并定位到当前节点
    setAllTreeExpand(id) {
      this.clearTimer(this.expandAllTimer)
      this.expandAllTimer = setTimeout(() => {
        this.$refs.xTree.setAllTreeExpand(true)
        if (id) {
          const target = this.tableData.find(item => item.id === id)
          this.$refs.xTree.setCurrentRow(target)
        }
      }, 10)
    },
    // 高亮行
    setCurrentRow(row) {
      this.activeTimer = setTimeout(() => {
        this.$refs.xTree.setCurrentRow(row)
      }, 100)
    },
    // 清除高亮
    clearCurrentRow() {
      this.$refs.xTree.clearCurrentRow()
    },
    handleAddFlow() {
      this.$refs.flowConfigForm.validate(valid => {
        if (!valid) return
        let tableId, flowStateColumnCode
        if (this.flowConfig.flowType === 'TABLE') {
          tableId = this.flowConfig.table2dId
          flowStateColumnCode = this.flowConfig.statusCol
          const colInfo = this.tableCol.find(item => item.code === flowStateColumnCode)
          this.flowConfig.flowStateColumnName = colInfo.name
        } else {
          tableId = this.flowConfig.cubeId
          flowStateColumnCode = this.flowConfig.dimId
          const colInfo = this.cubeDimsList.find(item => item.id === flowStateColumnCode)
          this.flowConfig.flowStateColumnName = colInfo.dimDirectoryName
        }
        const params = { ...this.flowConfig, parentId: this.flowRow.id, type: 'FLOW', tableId, flowStateColumnCode }
        this.$emit('addFlow', params)
        this.showFlowConfig = false
      })
    },
    closeFlowConfig() {
      this.$refs.flowConfigForm.resetFields()
      this.showFlowConfig = false
    },
    addTableSuccess(targetId) {
      this.addTableVisivle = false
      this.$emit('refreshTable', targetId)
    },
    closeTable() {
      this.addTableVisivle = false
    },
    // 删除节点
    delNode(row) {
      const delTxt = row.type === 'GROUP' ? '删除分组时，该分组下级的所有子组和流程将一并删除，是否继续?' : '确定删除该流程吗?'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteWorkflow(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const target = this.tableData.find(item => item.id === row.id)
            this.$emit('refreshTable', target.parentId)
          }
        })
      })
    },
    resetRenameForm() {
      if (this.$refs.reNameForm) {
        this.$refs.reNameForm.resetFields()
      }
    },
    // 打开重命名弹框
    openReName(row) {
      const { flowName } = row
      this.reNameVisible = true
      this.resetRenameForm()
      this.reNameForm.name = flowName
      this.currentNodeData = row
    },
    closeReName() {
      this.reNameVisible = false
    },
    handleReName() {
      this.$refs['reNameForm'].validate((valid) => {
        if (valid) {
          const { type, id, parentId } = this.currentNodeData
          const params = {
            id: id,
            flowName: this.reNameForm.name,
            type: type,
            parentId: parentId
          }
          updateWorkflow(params).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.closeReName()
              this.$emit('refreshTable', id)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    clickTreeNode(row) {
      if (row.type !== 'FLOW') return
      if (row.flowType === 'TABLE') {
        this.$emit('openFlowChart', row)
      } else {
        // 打开流程详情，true为新增tag，false为已有tag
        this.$emit('openCubeFlow', row, true)
      }
    },
    handleStartProcess(row) {
      const params = {
        wfDimDefineId: row.id,
        flowType: row.flowType
      }
      startProcess(params).then(res => {
        if (res.code !== 200) return this.$message.error(res.msg)
        this.$message.success('启动成功')
      })
    },
    handleStopProcess(row) {
      const params = {
        wfDimDefineId: row.id,
        flowType: row.flowType
      }
      stopProcess(params).then(res => {
        if (res.code !== 200) return this.$message.error(res.msg)
        this.$message.success('终止成功')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.table-list {
  height: calc(100vh - 180px);
}
.table-node {
  color: $primary;
  cursor: pointer;
  i {
    margin-right: 2px;
  }
}
.custom-node {
  .vxe-icon-table {
    height: 22px;
    line-height: 24px;
  }
}
.excel-area {
  width: 100%;
  display: flex;
  height: 400px;
  .left {
    width: 680px;
  }
  .right {
    width: 600px;
    margin-left: 20px;
    position: relative;
  }
  .preview-title {
    font-weight: bold;
    color: $primary;
    margin-bottom: 10px;
    position: absolute;
    left: 27px;
    top: -30px;
    transform: translateX(-50%);
    z-index: 1;
  }
}
.upload-file {
  .el-input {
    width: 500px;
  }
  display: flex;
  .el-button {
    margin-left: 10px;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.custom-progress {
  width: 600px;
  text-align: center;
  .label {
    // color: $primary;
  }
}
.preview-table {
  ::v-deep .vxe-table--body-wrapper {
    overflow-y: auto;
  }
}
.upload-progress {
  width: 300px;
  margin-left: 20px;
  line-height: 2;
}
</style>
