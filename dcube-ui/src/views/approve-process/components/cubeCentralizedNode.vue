<template>
  <div class="centralized-nodes-wrap">
    <div class="top-bar">
      <span>归口节点维护</span>
      <el-button size="small" type="primary" @click="handleAdd">新增节点</el-button>
    </div>
    <vxe-table
      border="inner"
      ref="tableRef"
      :column-config="{resizable: true}"
      :tree-config="{transform: true, rowField: 'id', parentField: 'parentId'}"
      :data="tableData">
      <vxe-column field="centralizeName" title="归口节点名称"></vxe-column>
      <vxe-column field="centralizeDimSize" title="归口节点维度成员数"></vxe-column>
      <vxe-column title="审批人">
        <template #default="{ row }">
          <a :class="{'approver-select-wrap':true,'approver-select-active':nodeApproverRow.id===row.id}"
             @click="changeApproveUser(row)">{{ row.userName && row.userName !== '' ? row.userName : '无' }}</a>
        </template>
      </vxe-column>
      <vxe-column title="操作">
        <template #default="{ row }">
          <el-button size="small" type="text" @click="handleEdit(row)">修改</el-button>
          <!-- <el-button size="small" type="text" @click="openApproverModal(row)">设置审批人</el-button> -->
        </template>
      </vxe-column>
    </vxe-table>
    <el-dialog
      custom-class="centralized-node-modal" 
      v-if="showNodeModal" 
      :title="nodeRecord.id?'修改归口节点':'新增归口节点'" 
      :visible="showNodeModal" 
      width="600px" 
      :close-on-click-modal="false" 
      @close="closeNodeModal">
      <div class="name-wrap">
        <span>归口节点名称：</span>
        <el-input style="width:80%" v-model="nodeName" size="small"></el-input>
      </div>
      <div class="memeber-title">
        <span>成员名称</span>
        <el-checkbox v-model="memberParentRelative">父子联动</el-checkbox>
      </div>
      <el-tree 
        :data="dimMemberTree" 
        :props="treeProps" 
        node-key="id" 
        show-checkbox
        :defaultExpandedKeys="defaultExpandedKeys"
        :defaultCheckedKeys="nodeMembers" 
        :check-strictly="!memberParentRelative" 
        @check="memeberChange">
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeNodeModal">取 消</el-button>
        <el-button size="mini" type="primary" @click="updateNode">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="node-approver-modal" 
      v-if="showNodeApprover" 
      title="设置归口节点审批人" 
      :visible="showNodeApprover" 
      width="60vw"
      top="0"
      :close-on-click-modal="false" 
      @close="closeNodeApprover">
      <CubeApproveUser 
        :approverSelected="selectedApprover"
        @changeAprrover="changeAprrover"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeNodeApprover">取 消</el-button>
        <el-button size="mini" type="primary" @click="saveNodeApprover">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getCentralizedNodes, saveCentralizedNode} from '@/api/approve-progress.js'
import {getMember} from '@/api/system/dimension-set'
import {listToTree} from '@/utils/index'
import {cloneDeep} from 'lodash'
import CubeApproveUser from './cubeApproveUser.vue'

export default {
  name:'cubeCentralizedNode',
  components:{ CubeApproveUser },
  props:{
    centralizedRow: Object,
  },
  data(){
    return {
      tableData:[],
      treeProps: {
        children: 'children',
        label: 'dimName'
      },
      dimMemberTree:[],
      // 新增修改归口节点 基本信息
      showNodeModal: false,
      nodeName:'',
      memberParentRelative: false,
      nodeMembers:[],
      nodeRecord:{},
      // 新增修改归口节点 审批人
      showNodeApprover: false,
      nodeApproverRow: {},
      selectedApprover:[],
    }
  },
  computed:{
    defaultExpandedKeys(){
      let arr = []
      if(this.dimMemberTree.length){
        arr.push(this.dimMemberTree[0].id)
      }
      return arr
    }
  },
  watch:{
    centralizedRow:{
      handler(){
        this.getCentralizedList();
        this.getDimMemberList();
      },
      immediate: true,
      deep: true,
    }
  },
  created(){},
  methods:{
    getCentralizedList(){
      const params = new FormData()
      params.append('dimDefineDetailId', this.centralizedRow.id)
      getCentralizedNodes(params).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data;
        }
      })
    },
    getDimMemberList(){
      const params = {dimDirectoryId:this.centralizedRow.centralizedDimId}
      getMember(params).then(res => {
        this.dimMemberTree = listToTree(cloneDeep(res.data))
      })
    },
    handleAdd(){
      this.nodeName = '';
      this.nodeMembers = [];
      this.nodeRecord = {};
      this.showNodeModal = true;
    },
    handleEdit(row){
      this.nodeRecord = row;
      this.nodeName = row.centralizeName;
      if(row.centralizeDimId&&row.centralizeDimId.length){
        this.nodeMembers = row.centralizeDimId.split(',').map(Number);
      }else{
        this.nodeMembers = []
      }
      this.showNodeModal = true;
    },
    closeNodeModal(){
      this.showNodeModal = false;
      this.memberParentRelative = false;
    },
    memeberChange(curNode, {checkedKeys, checkedNodes}) {
      const memberStr =  checkedKeys.join(',');
      this.nodeRecord.centralizeDimId = memberStr;
      this.nodeRecord.centralizeDimSize = checkedKeys.length;
    },
    updateNode(){
      if(!this.nodeName||this.nodeName=='') return this.$message.error('请填写节点名称')
      const params = {
        ...this.nodeRecord,
        "centralizeName": this.nodeName,
        "dimDefineDetailId": this.centralizedRow.id,
      }
      saveCentralizedNode(params).then(res=>{
        if (res.code === 200) {
          this.getCentralizedList();
          this.closeNodeModal()
          this.$message.success(params.id?'修改成功':'新增成功')
        }
      })
    },
    openApproverModal(row){
      this.nodeApproverRow = row;
      if(row.userIds&&row.userIds.length){
        this.selectedApprover = row.userIds
      }else{
        this.selectedApprover =[]
      }
      this.showNodeApprover = true;
    },
    changeAprrover(ids){
      this.selectedApprover = ids;
    },
    saveNodeApprover(){
      const params = {
        ...this.nodeApproverRow,
        "userIds": this.selectedApprover,
        "userId": this.selectedApprover.join(','),
      }
      saveCentralizedNode(params).then(res=>{
        if (res.code === 200) {
          this.closeNodeApprover()
          this.getCentralizedList();
          this.$message.success('设置审批人成功')
        }
      })
    },
    closeNodeApprover(){
      this.showNodeApprover = false;
      this.nodeApproverRow = {};
    },
    changeApproveUser(row) {
      this.nodeApproverRow = row;
      if (row.userIds && row.userIds.length) {
        this.selectedApprover = row.userIds
      } else {
        this.selectedApprover = []
      }
      this.showNodeApprover = true;
    },
  }
}
</script>

<style lang="scss">
.centralized-nodes-wrap{
  padding: 0 12px 12px 12px;
}
.top-bar{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  >span{
    font-weight: 500;
  }
}
.centralized-node-modal{
  .memeber-title {
    padding-left: 4px;
    font-size: 13px;
    font-weight: 700;
    line-height: 28px;
    color: #606266;
    background: #f6f6f9;
    margin-top: 8px;

    .el-checkbox {
      float: right;
      right: 8px;

      .el-checkbox__label {
        font-size: 13px;
      }
    }
  }
  .el-tree{
    height: 42vh;
    overflow: auto;
  }
  .dialog-footer{
    text-align: center;
  }
}
.node-approver-modal{
  .department-wrapper .btn-group{
    display: none;
  }
  .dialog-footer{
    text-align: center;
  }
}
</style>