<template>
  <div class="table-list">
    <vxe-table
      ref="xTree"
      size="mini"
      height="auto"
      border="inner"
      :loading="loading"
      show-overflow
      :tree-config="treeConfig"
      :data="tableData"
      :row-config="{isCurrent: true, isHover: true}"
    >
      <vxe-column field="tableName" title="名称" tree-node show-overflow width="350">
        <template #default="{ row }">
          <span class="flex-vertical-center custom-node" :class="row.type==='TABLE'?'table-node':''" @click="clickTreeNode(row)">
            <template v-if="row.type==='GROUP'">
              <i class="tree-node-icon" :class="$refs.xTree.isTreeExpandByRow(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'" />
            </template>
            <template v-else>
              <i class="tree-node-icon vxe-icon-table" />
            </template>
            <div class="custom-ellipsis">{{ row.tableName }}</div>
          </span>
        </template>
      </vxe-column>
      <vxe-column field="totalSize" title="数据条数" min-width="100" />
      <vxe-column field="createBy" title="创建人" min-width="150" />
      <vxe-column field="createTime" title="创建日期" min-width="140" />
      <vxe-column field="updateTime" title="修改日期" min-width="140" />
      <vxe-column title="操作" width="350">
        <template #default="{ row }">
          <el-button
            v-if="row.type==='GROUP'"
            size="mini"
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="addChildGroup(row)"
          >新增子组</el-button>
          <el-button
            v-if="row.type === 'GROUP'"
            type="text"
            size="mini"
            icon="el-icon-circle-plus-outline"
            @click="openAddTable(row)"
          >新增底表</el-button>
          <el-button
            v-if="row.type === 'TABLE'"
            type="text"
            status="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="openEditTable(row)"
          >修改底表</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="delNode(row)"
          >删除</el-button>
        </template>
      </vxe-column>
      <template #empty>
        <table-empty />
      </template>
    </vxe-table>
    <!-- 新增底表 -->
    <add-table :table-visible="addTableVisible" :cur-type="addTableType" :current-node-data="currentNodeData" @close="closeAddTable" @addSuccess="addTableSuccess" />
  </div>
</template>

<script>
import { getDict, delNode } from '@/api/data-base'
import AddTable from './add-table.vue'

export default {
  components: { AddTable },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    dictType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为汉字、英文、数字、下划线的组合'))
      }
      callback()
    }
    return {
      treeConfig: { transform: true, rowField: 'id' },
      currentNodeData: {},
      addGroupForm: {
        name: ''
      },
      reNameVisible: false,
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      expandTimer: null,
      activeTimer: null,
      originDataArr: [],
      user: this.$store.getters.user,
      addTableVisible: false,
      addTableType: 'add'
    }
  },
  watch: {},
  created() {

  },
  methods: {
  // 展开树
    expandTree(row) {
      this.expandTimer = setTimeout(() => {
        this.$refs.xTree.setTreeExpand(row, true)
      }, 0)
    },
    // 高亮行
    setCurrentRow(row) {
      this.activeTimer = setTimeout(() => {
        this.$refs.xTree.setCurrentRow(row)
      }, 100)
    },
    // 清除高亮
    clearCurrentRow() {
      this.$refs.xTree.clearCurrentRow()
    },
    // 新增子组
    addChildGroup(row) {
      this.$emit('addChildGroup', row)
    },
    clickTreeNode(row) {
      if (row.type !== 'TABLE') return
      this.$emit('openTableDetail', row)
    },
    // 删除节点
    delNode(row) {
      const delTxt = row.type === 'GROUP' ? '删除分组时，该分组下级所有的子组和底表将一并删除，是否继续?' : '确定删除该底表吗？'
      this.$confirm(delTxt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delNode(row.id).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const target = this.tableData.find(item => item.id === row.id)
            this.$emit('refreshTable', target.parentId)
          }
        })
      })
    },
    openAddTable(row) {
      this.addTableType = 'add'
      this.currentNodeData = row
      this.addTableVisible = true
    },
    openEditTable(row) {
      this.addTableType = 'edit'
      this.currentNodeData = row
      this.addTableVisible = true
    },
    closeAddTable() {
      this.addTableVisible = false
    },
    addTableSuccess(id) {
      this.$emit('refreshTable', id)
    },
    // 获取数据格式树
    getDataFormat() {
      this.$store.dispatch('table/getDataFormat')
    },
    // 获取字典集合
    getDictType() {
      const params = {
        groupId: '7c8a84e530fcd75b65c80f64f6bd3bf9'
      }
      getDict(params).then(res => {
        if (res.code === 200) {
          this.dictType = res.rows
        }
      })
    },
    // 新增子组
    openChildGroup(row) {
      this.currentNodeId = row.id
      this.addGroupVisible = true
    },
    // 新增分组
    addRootGroup() {
      this.currentNodeId = ''
      this.addGroupVisible = true
    },
    // 关闭分组弹框
    closeAddGroup() {
      this.addGroupVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.table-list {
  height: calc(100vh - 155px);
}
.table-node {
  color: $primary;
  cursor: pointer;
  i {
    margin-right: 2px;
  }
}
</style>
