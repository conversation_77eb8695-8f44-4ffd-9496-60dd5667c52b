<template>
  <div class="table-detail-wrapper">
    <div class="table-list">
      <div v-if="dynamicSearchArr.length" class="search-container" :style="{'--height':searchHeight}">
        <!-- 动态查询条件 -->
        <div class="left" :class="{ 'collapsed': !isExpanded }">
          <el-form ref="searchForm" :inline="true" class="search-form" size="mini">
            <div class="left-area">
              <el-form-item v-for="(item,index) in dynamicSearchArr" :key="item.columnCode" :ref="`searchItem${index}`" :label="item.name">
                <div v-if="((item.storageType === 'VARCHAR'&&item.columnCode!=='version'&&item.columnCode!=='dateInterval')||item.storageType === null)&&item.dicFlag==='N'" class="custom-control">
                  <el-select v-model="item.prefixVal" size="mini">
                    <el-option v-for="opt in searchOpts2" :key="opt.value" :value="opt.value" :label="opt.label" />
                  </el-select>
                  <el-input v-model="item.value" size="mini" type="text" />
                </div>
                <div v-if="numType.includes(item.storageType)" class="custom-control">
                  <el-select v-model="item.prefixVal" size="mini">
                    <el-option v-for="opt in searchOpts1" :key="opt.value" :value="opt.value" :label="opt.label" />
                  </el-select>
                  <el-input v-model="item.value" :type="item.storageType==='DOUBLE'?'number':'integer'" size="mini" />
                </div>
                <div v-if="item.columnCode === 'baseDataDtStart'||item.columnCode === 'baseDataDtEnd'" class="custom-control custom-date">
                  <el-date-picker
                    v-model="item.value"
                    size="mini"
                    type="date"
                    placeholder="选择日期"
                    @change="changeCustomDate(item)"
                  />
                </div>
                <div v-if="item.columnCode === 'dateInterval'" class="custom-control">
                  <el-select v-model="item.value" class="select-datetype" size="mini">
                    <el-option v-for="opt in searchOpts3" :key="opt.value" :value="opt.value" :label="opt.label" />
                  </el-select>
                </div>
                <div v-if="item.columnCode === 'version'" class="custom-control">
                  <el-select v-model="item.value" class="select-version" size="mini" clearable>
                    <el-option v-for="opt in versionList" :key="opt.id" :value="opt.version" :label="opt.version" />
                  </el-select>
                </div>
                <el-select v-if="item.dicFlag==='Y'" v-model="item.value" size="mini">
                  <el-option v-for="opt in item.dicItems" :key="opt.dicCode" :value="opt.dicCode" :label="opt.dicLabel" />
                </el-select>
              </el-form-item>
            </div>
            <div class="right-area" :style="{'--width':rightAreaW}">
              <el-form-item>
                <el-button size="mini" type="primary" icon="el-icon-search" @click="searchTable">查询</el-button>
                <el-button size="mini" type="primary" icon="iconfont icon-new-scst" @click="createView">生成视图</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
              <div v-if="showExpandButton" class="expandArea" @click="toggleExpand">
                <el-button
                  type="text"
                  size="mini"
                >
                  {{ isExpanded ? '收起' : '展开' }}
                </el-button>
                <i :class="isExpanded?'el-icon-arrow-up':'el-icon-arrow-down'" />
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <div class="table-content" :style="{'--translatedY':translatedY}">
        <div class="table-box" :style="{'--height':tableHeight}">
          <vxe-table
            :key="randomKey"
            ref="xTable"
            size="mini"
            height="auto"
            class="dynamic-table"
            border
            keep-source
            :data="viewTable"
            :loading="loading"
            :column-config="{isCurrent: true, isHover: true,useKey:true,resizable: true}"
            :row-config="{isCurrent: true, isHover: true}"
            :edit-config="{trigger: 'click', mode: 'cell',showStatus: true,showUpdateStatus:true,enabled:false}"
            :scroll-y="{scrollToTopOnChange:true}"
            :mouse-config="{selected: true}"
            :sort-config="{multiple: true}"
            :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true, isChecked: true}"
            @current-change="currentChangeEvent"
            @resizable-change="colResizeChange"
            @sort-change="sortChangeEvent"
          >
            <vxe-column
              v-for="(config,index) in tableMetaJson"
              :key="config.code"
              :params="config"
              :field="config.code||String(config.no)"
              :title="config.name"
              :align="formatAlign(config.dataFormat)"
              :edit-render="{autofocus: '.vxe-input--inner', autoselect: true,enabled:index>0&&!config.computationRule&&(config.systemKey!=='USER'&&config.systemKey!=='ORG')}"
              show-header-overflow
              show-overflow="title"
              :width="config.columnWidth||160"
              sortable
            >
              <template #header="{ column }">
                <div slot="reference" class="flex-vertical-center custom-col" @click="clickCol(column)">
                  <div class="flex-vertical-center col-left">
                    <i v-if="config.systemKey==='ORG' || config.systemKey==='USER'" class="iconfont new-user" />
                    <i v-if="config.condition===true" class="iconfont icon-shaixuan" />
                    <div class="flex-vertical-center">
                      <el-tooltip class="item" effect="dark" :content="config.name" placement="top-start">
                        <span
                          class="custom-ellipsis"
                          :style="{width:`${allColWArr[index]-(config.condition===null?80:90)}px`}"
                        >{{ config.name }}</span>
                      </el-tooltip>

                    </div>
                  </div>
                  <div class="set-pannel">
                    <el-popover
                      placement="bottom-start"
                      width="200"
                      trigger="click"
                    >
                      <div class="popver-box">
                        <!-- 列名 -->
                        <div class="row-cell">
                          <el-input v-model="config.name" placeholder="列名不能超过30个字符" size="small" maxlength="30">
                            <el-tooltip slot="prefix" class="item" effect="dark" content="列名不能超过30个字符" placement="top">
                              <i class="el-input__icon vxe-icon-question-circle" />
                            </el-tooltip>
                          </el-input>
                        </div>
                        <!-- 数据格式 -->
                        <div class="row-cell">
                          <el-cascader
                            v-model="config.dataFormatId"
                            size="small"
                            placeholder="选择数据格式"
                            :options="formatTree"
                            :props="formatProps"
                            :show-all-levels="false"
                            @change="changeFormatTree"
                          />
                        </div>
                        <!-- 筛选条件 -->
                        <div class="row-cell">
                          <el-select v-model="config.condition" size="small" placeholder="选择筛选条件">
                            <el-option
                              v-for="item in filterOption"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </div>
                        <!-- 关联系统参数 -->
                        <div class="row-cell">
                          <el-select v-model="config.systemKey" size="small" placeholder="选择关联参数" @change="changeSystemKey(config)">
                            <el-option
                              v-for="item in relateOption"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </div>
                        <div v-if="config.systemKey==='DICT'" class="row-cell">
                          <el-select v-model="config.systemValue" size="small" placeholder="选择字典">
                            <el-option
                              v-for="item in dictType"
                              :key="item.id"
                              :label="item.groupInstanceName"
                              :value="item.id"
                            />
                          </el-select>
                        </div>
                        <div class="row-cell setcol-btn">
                          <el-button class="del-btn" type="text" @click="updateCol(config)">确  认</el-button>
                        </div>

                      </div>
                      <el-tooltip :slot="'reference'" class="item" effect="dark" content="列设置" placement="top">
                        <i style="margin-top:6px" class="iconfont icon-new-setting" />
                      </el-tooltip>
                    </el-popover>
                  </div>

                </div>
              </template>
              <template #edit="{ row }">
                <vxe-input v-if="(config.dataFormat.storageType === 'VARCHAR'||config.dataFormat.storageType === null)&&config.dataFormat.dicFlag==='N'" v-model="row[config.code]" type="text" />
                <vxe-input v-if="numType.includes(config.dataFormat.storageType)" v-model="row[config.code]" :type="config.dataFormat.storageType==='DOUBLE'?'number':'integer'" />
                <vxe-input
                  v-if="config.dataFormat.storageType === 'DATE'"
                  v-model="row[config.code]"
                  size="mini"
                  type="date"
                  placeholder="选择日期"
                />
                <vxe-select v-if="config.dataFormat.dicFlag==='Y'" v-model="row[config.code]">
                  <vxe-option v-for="item in config.dicItems" :key="item.dicCode" :value="item.dicCode" :label="item.dicLabel" />
                </vxe-select>
              </template>
            </vxe-column>
            <template #empty>
              <table-empty />
            </template>
          </vxe-table>
          <div class="add-col">
            <el-tooltip class="item" effect="dark" content="数据深索" placement="left">
              <el-button circle icon="iconfont icon-sjss" size="mini" @click="openDataSeek" />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="查询列" placement="left">
              <el-button circle icon="el-icon-search" size="mini" @click="openSearchCol" />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="列自动命名" placement="left">
              <el-button circle icon="el-icon-more" size="mini" @click="openColRename" />
            </el-tooltip>
          </div>
        </div>
        <div class="table-footer" :style="{'justify-content':processInfo||exportInfo.msg?'space-between':'flex-end'}">
          <div v-if="processInfo&&processInfo.status!=='ERROR'" class="process-info">
            <span>{{ processInfo.taskName }}：</span>
            <el-progress :percentage="formatProgress(processInfo.progress)" size="mini" />
            <span v-if="processInfo.status==='COMPLETE'" style="margin-left: 10px;">{{ processInfo.cost+'ms' }}</span>
          </div>
          <div v-if="exportInfo.msg" class="process-info">
            <span>{{ exportInfo.msg }}</span>
            <el-progress :percentage="exportInfo.process" size="mini" />
            <el-button v-if="exportInfo.extra" type="text" size="mini" class="download-link" @click="commonDownLoad">点击下载文件</el-button>
            <el-progress v-if="showDownloadProcess" :percentage="downloadPercentage" size="mini" />
          </div>
          <div class="right">
            <div v-if="sumavgObj.a && sumavgObj.s" class="sum-avg flex-vertical-center">
              <div class="flex-vertical-center" style="margin-right:20px">
                <div class="label">求和：</div>
                <div class="val">{{ sumavgObj.s }}</div>
              </div>
              <div class="flex-vertical-center">
                <div class="label">平均：</div>
                <div class="val">{{ sumavgObj.a }}</div>
              </div>
            </div>
            <el-pagination
              :current-page="pageInfo.currentPage"
              :page-sizes="[20,50,100]"
              :page-size="pageInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalRecords"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 查询列 -->
    <el-dialog title="查询列" :append-to-body="true" :visible.sync="searchColDiaShow" width="400px" :close-on-click-modal="false" @close="closeSearchCol">
      <el-form ref="searchColForm" label-position="right" label-width="80px" size="small" :model="searchColForm" :rules="searchColRules">
        <el-form-item label="列名" prop="code">
          <el-autocomplete
            v-model="searchColForm.code"
            value-key="name"
            class="inline-input"
            :fetch-suggestions="querySearch"
            placeholder="请输入列名"
            @select="handleSelectCol"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeSearchCol">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSearchCol">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 新增底表 -->
    <create-view :table-visible="viewVisible" :cur-type="'view'" :current-node-data="curViewData" :table-id="curTableDetail.id" @close="closeView" @addSuccess="addViewSuccess" />
    <!-- 选择人员 -->
    <select-user ref="selectUser" @selectUser="handleSelectUser" />
    <!-- 数据深索 -->
    <data-seek ref="dataSeekRef" :show-data-seek-dia="showDataSeekDia" :table-id="curTableId" from-module="dataBase" :data-seek-filter="dataSeekFilter" @close="closeSeek" />
    <!-- 列自动命名 -->
    <col-rename v-if="showColRename" ref="colRename" :col-arr="colRenameArr" @close="closeColRename" />
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cloneDeep } from 'lodash'

import {
  calcSumAvg,
  get2dTable,
  getSubTable,
  getTableDetail,
  saveCol,
  previewView,
  getVersionList
} from '@/api/data-base'
import dataSeek from '@/components/DataSeek'
import selectUser from '@/components/SelectUser'
import CreateView from './create-view.vue'
import ColRename from './col-rename.vue'
export default {
  name: 'TableDetail',
  components: {
    dataSeek,
    selectUser,
    CreateView,
    ColRename
  },
  props: {
    curTableDetail: {
      type: Object,
      default: () => {}
    },
    dictType: {
      type: Array,
      default: () => []
    },
    tablePermission: {
      type: [Object, null],
      default: null
    },
    twoCol: {
      type: Array,
      default: () => []
    },
    parentTableKeyVal: {
      type: String,
      default: ''
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[_a-zA-Z0-9]+$/
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为英文、数字、下划线的组合'))
      }
      callback()
    }
    return {
      searchOpts1: Object.freeze([{
        label: '等于',
        value: '0'
      }, {
        label: '小于',
        value: '-1'
      }, {
        label: '小于等于',
        value: '-2'
      }, {
        label: '大于',
        value: '1'
      }, {
        label: '大于等于',
        value: '2'
      }]),
      searchOpts2: Object.freeze([{
        label: '精确',
        value: '0'
      }, {
        label: '模糊',
        value: '-1'
      }]),
      searchOpts3: Object.freeze([{
        label: '每日',
        value: 'DAY'
      }, {
        label: '月末',
        value: 'MONTH'
      }, {
        label: '季末',
        value: 'QUARTER'
      }, {
        label: '年末',
        value: 'YEAR'
      }]),
      orgUserDict: Object.freeze(['ORG', 'USER', 'DICT']),
      deptTree: this.$store.getters.deptTree,
      numType: Object.freeze(['INTEGER', 'DOUBLE']),
      sumavgObj: {
        a: null,
        s: null
      },
      loading: false,
      // 动态查询数组
      dynamicSearchArr: [],
      orgArr: [],
      customTypeArr: [],
      tableMetaJson: [], // 页面上显示的列数据
      currentNo: 1,
      // 显示值表格
      viewTable: [],
      // 原始值表格
      originTable: [],
      pageInfo: {
        currentPage: 1,
        pageSize: 20
      },
      totalRecords: 0,
      filterOption: Object.freeze([{
        label: '不作为查询条件',
        value: null
      }, {
        label: '作为查询条件',
        value: true
      }]),
      relateOption: Object.freeze([{
        label: '不关联系统参数',
        value: null
      }, {
        label: '关联机构',
        value: 'ORG'
      }, {
        label: '关联人员',
        value: 'USER'
      }]),
      curRow: null,
      curCol: null,
      // 字典集
      dictMap: {},
      // 选择人员id和nickName集合,方便单元格格式化显示
      userMap: {},
      // 机构的id和name集合,方便单元格格式化显示
      orgMap: {},
      sortable2: null,
      tableClientW: 0,
      tableOffsetW: 0,
      // 单元格右键菜单
      menuConfig: {
        body: {
          options: [
            [
              { code: 'trace', name: '引用追溯', prefixIcon: 'vxe-icon-copy', disabled: false },
              { code: 'check', name: '被引用查看', prefixIcon: 'vxe-icon-search', disabled: false }
              // { code: 'edit', name: '修改痕迹', prefixIcon: 'vxe-icon-copy', disabled: false }
            ]
          ]
        }
      },
      // 查看修改痕迹
      showModifyHis: false,
      allColWArr: [],
      formatTree: this.$store.getters.formatListTree,
      formatList: this.$store.getters.formatList,
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'groupInstanceName' },
      defaultFormatId: '3b583866df798d08b8dd811f1f14fe0a',
      // 排序参数集合
      sortList: [],
      // 查询参数集合
      filterList: [],
      addKeyTimer: null,
      // 记录行编辑过的列code
      editRowColMap: {},
      relateSystemCol: null,
      randomKey: Math.round(Math.random() * 1000 + 1),
      processInfo: null,
      processTimer: null,
      // 刷新表格时默认更新查询条件(如果是点击查询按钮则不更新查询条件)
      updateSearch: true,
      // 规则快捷输入
      quickInputOpts: {},
      quickForm: { o: '', p: '', expressIf: '', t: [], c: [], func: [] },
      opifKey: Object.freeze(['o', 'p', 't']),
      showBackUp: false,
      backUpForm: { name: '' },
      backUpRules: {
        name: [{
          required: true,
          trigger: 'blur',
          message: '请输入版本名称'
        }]
      },
      showBackUp2: false,
      backUpForm2: { versionName: '', suffixType: '', isOverwrite: 'N', id: '' },
      backUpRules2: {
        versionName: [{
          required: true,
          trigger: 'blur',
          message: '请输入版本名称'
        }]
      },
      fieldTypeList: [],
      showRevert: false,
      revertForm: {
        name: ''
      },
      versionList: [],
      originList: [],
      showOrigin: false,
      originForm: {
        name: ''
      },
      scrollTimer: null,
      user: this.$store.getters.user,
      socket: null,
      exportInfo: {
        msg: '',
        process: 0,
        extra: ''
      },
      operateTime: '',
      localhost: window.location.host,
      taskInfoTimer: null,
      requestCount: 0,
      showDownloadProcess: false,
      downloadPercentage: 0,
      filesCurrentPage: 0, // 文件开始偏移量
      fileFinalOffset: 0, // 文件最后偏移量
      stopRecursiveTags: true, // 停止递归标签，默认是true 继续进行递归
      contentList: [], // 文件流数组
      fileName: '',
      // 子表弹框
      subTableShow: false,
      // 子表id
      relSubTableId: '',
      // 父表主键code
      parentTableKeyCode: '',
      // 父表主键的值
      // parentTableKeyVal: '',
      selectRow: null,
      submitEdges: [],
      showBackUpTable: false,
      backUpTableForm: { name: '' },
      backUpTableRules: {
        name: [{
          required: true,
          trigger: 'blur',
          message: '请输入备份表名称'
        }]
      },
      backupCols: [],
      showBackUpTable2: false,
      backUpTableForm2: { name: '', remark: '' },
      backUpTableRules2: {
        name: [{
          required: true,
          trigger: 'blur',
          validator: checkName
        }]
      },
      backupCols2: [],
      showCreateDataDia: false,
      funcDescList: [],
      searchColDiaShow: false,
      searchColForm: {
        code: ''
      },
      searchColRules: {
        code: [{
          required: true,
          trigger: ['blur', 'change'],
          message: '请输入列名'
        }]
      },
      curSelectCol: {},
      curRefCols: [],
      curRefRow: '',
      showDataSeekDia: false,
      drawers: [], // 存储抽屉实例
      drawerCounter: 0, // 抽屉唯一ID生成
      direction: 'rtl', // 抽屉打开方向
      maxLevel: 8, // 最大嵌套层级
      dialogVisible: false,
      currentRow: null,
      tableBData: [],
      fullscreenDialogs: [],
      dialogCounter: 0,
      isExpanded: false,
      totalSearchRows: 1, // 查询条件行数
      resizeObserver: null,
      viewVisible: false,
      curViewData: {},
      showColRename: false,
      dataSeekFilter: []
    }
  },
  computed: {
    // 是否显示展开收起按钮
    showExpandButton() {
      return this.totalSearchRows > 1
    },
    searchHeight() {
      return this.isExpanded ? `${this.totalSearchRows * 40}px` : '40px'
    },
    tableHeight() {
      let h = 'calc(100vh - 230px)'
      if (this.dynamicSearchArr.length) {
        h = this.isExpanded ? `calc(100vh - ${230 + this.totalSearchRows * 40}px)` : 'calc(100vh - 230px)'
      }
      return h
    },
    translatedY() {
      let y = '0px'
      if (this.dynamicSearchArr.length) {
        y = this.isExpanded ? `${this.totalSearchRows * 40}px` : '0px'
      }
      return y
    },
    rightAreaW() {
      return this.totalSearchRows > 1 ? '330px' : '290px'
    },
    currentTitle() {
      return this.currentRow ? `详情 - ${this.currentRow.name}` : '详情'
    },
    childFlag() {
      return this.curTableDetail.childFlag
    },
    userId() {
      return this.user.userId
    },
    curTableId() {
      return this.curTableDetail && this.curTableDetail.id || ''
    },
    tableLevel() {
      return this.curTableDetail.tableLevel || 0
    },
    defaultFormat() {
      return this.formatList.filter(item => item.id === this.defaultFormatId)[0]
    }
  },

  watch: {
    curTableDetail: {
      handler(newVal, oldVal) {
        if (newVal && newVal.id) {
          const { id } = newVal
          // 初始化请求条件
          this.initTableParams()
          this.fetchData(id, false)
        }
      },
      immediate: true,
      deep: true
    },
    tableMetaJson: {
      handler(newVal, oldVal) {
        this.initSearchArr()
        console.log('tableMetaJson', newVal, this.dynamicSearchArr)
        if (newVal.length) {
          // 初始化搜索条件
          newVal.forEach(col => {
            const { name, code, condition, dataFormat, dicItems, systemKey } = col
            const targetIdx = this.dynamicSearchArr.findIndex(item => item.columnCode === code)
            if (targetIdx < 0 && dataFormat && dataFormat.storageType && condition) {
              this.dynamicSearchArr.push({
                prefixVal: '0',
                name,
                columnCode: code,
                value: '',
                storageType: dataFormat.storageType,
                dicFlag: dataFormat.dicFlag,
                dicItems,
                systemKey
              })
            }
            if (targetIdx > -1 && !condition) {
              this.dynamicSearchArr.splice(targetIdx, 1)
            }
          })
          this.initResizeObserver()
        }
      },
      immediate: true,
      deep: true
    },
    sortList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.refreshTable()
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    if (this.addKeyTimer) {
      window.clearTimeout(this.addKeyTimer)
      this.addKeyTimer = null
    }
    this.clearScrollTimer()
  },
  methods: {
    // 修改日期联动版本
    changeCustomDate(item) {
      console.log(item, this.dynamicSearchArr)
      let baseDataDtStart
      let baseDataDtEnd = ''
      this.dynamicSearchArr.forEach(item => {
        if (item.columnCode === 'baseDataDtStart' && item.value) {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtStart = item.value
        }
        if (item.columnCode === 'baseDataDtEnd') {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtEnd = item.value
        }
      })
      console.log(baseDataDtStart, baseDataDtEnd)
      if (baseDataDtStart && baseDataDtEnd) {
        const params = {
          baseDataDtStart,
          baseDataDtEnd,
          tableId: this.curTableId
        }
        getVersionList(params).then(res => {
          if (res.code === 200) {
            this.versionList = res.data
          }
        })
      }
    },

    closeView() {
      this.viewVisible = false
    },
    addViewSuccess() {
      this.refreshTable()
    },
    // 初始化搜索数组
    initSearchArr() {
      if (!this.dynamicSearchArr || !this.dynamicSearchArr.length) {
        this.dynamicSearchArr = [{
          columnCode: 'baseDataDtStart',
          name: '起始数据日期',
          value: '',
          storageType: 'TIMESTAMP',
          prefixVal: '-1',
          op: 'and'
        }, {
          columnCode: 'baseDataDtEnd',
          name: '截止数据日期',
          value: '',
          storageType: 'TIMESTAMP',
          prefixVal: '-1',
          op: 'and'
        }, {
          columnCode: 'dateInterval',
          name: '',
          value: 'DAY',
          storageType: 'VARCHAR',
          prefixVal: '-1',
          op: 'and'
        }, {
          columnCode: 'version',
          name: '版本名称',
          value: '',
          storageType: 'VARCHAR',
          prefixVal: '-1',
          op: 'and'
        }]
      }
    },
    // 生成视图
    createView() {
      const params = {
        filterList: this.filterList,
        id: this.curTableDetail.id
      }
      previewView(params).then(res => {
        if (res.code === 200) {
          const { dataSource, sqlScript, tableMeta, result, totalCount } = res.data
          this.curViewData = { dataSource, sqlScript, tableMeta, result, totalCount }
          this.viewVisible = true
        }
      })
    },
    // 初始化ResizeObserver监听
    initResizeObserver() {
      this.resizeObserver = new ResizeObserver(this.calculateRows)
      this.$nextTick(() => {
        if (this.$refs.searchForm) {
          const container = this.$refs.searchForm.$el
          if (container) this.resizeObserver.observe(container)
        }
      })
    },

    // 计算行数（优化版）
    calculateRows() {
      const items = this.$refs.searchForm.$el.querySelectorAll('.el-form-item')
      if (items.length === 0) return

      let rowCount = 1
      let prevTop = null

      items.forEach((item, index) => {
        const rect = item.getBoundingClientRect()
        if (index === 0) {
          prevTop = rect.top
        } else if (rect.top > prevTop + 5) { // 允许5px的误差
          rowCount++
          prevTop = rect.top
        }
      })

      this.totalSearchRows = rowCount
    },

    // 切换展开状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    openDataSeek() {
      const filterArr = cloneDeep(this.dynamicSearchArr.slice(0, 4))
      const hasVal = filterArr.slice(0, 3).every(item => item.value)
      if (!hasVal) {
        this.$message.error('底表中数据量过大，请先选择需要深索数据的起始截止数据日期！')
        return
      }
      let baseDataDtStart
      let baseDataDtEnd = ''
      filterArr.forEach(item => {
        if (item.columnCode === 'baseDataDtStart' && item.value) {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtStart = item.value
        }
        if (item.columnCode === 'baseDataDtEnd') {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtEnd = item.value
        }
      })
      if (new Date(baseDataDtStart).getTime() > new Date(baseDataDtEnd).getTime()) {
        this.$message.error('起始数据日期不能大于截止数据日期')
        return
      }
      this.dataSeekFilter = filterArr.map(item => {
        const { columnCode, value, prefixVal, storageType } = item
        return { columnCode, value, prefixVal, storageType, op: 'and' }
      })
      this.showDataSeekDia = true
      this.$refs.dataSeekRef.scrollToBottom()
    },
    closeSeek() {
      this.showDataSeekDia = false
    },
    formatAlign(val) {
      let align = 'left'
      if (val) {
        align = val.contentAlign === 'middle' ? 'center' : val.contentAlign
      }
      return align
    },
    handleSelectCol(data) {
      this.curSelectCol = data
    },
    querySearch(queryString, cb) {
      const allCol = this.tableMetaJson
      const results = queryString ? allCol.filter(this.createFilter(queryString)) : allCol
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (allCol) => {
        return (allCol.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    closeSearchCol() {
      this.searchColDiaShow = false
    },
    handleSearchCol() {
      this.$refs['searchColForm'].validate((valid) => {
        if (valid) {
          this.closeSearchCol()
          const idx = this.tableMetaJson.findIndex(item => item.code === this.curSelectCol.code)
          this.scrollToCurCol(true, idx)
        }
      })
    },
    openSearchCol() {
      this.searchColDiaShow = true
      this.searchColForm.code = ''
      if (this.$refs['searchColForm']) {
        this.$refs['searchColForm'].resetFields()
      }
    },
    openColRename() {
      this.showColRename = true
      this.colRenameArr = this.tableMetaJson.map(item => {
        return {
          name: item.name,
          code: item.code
        }
      })
    },
    closeColRename() {
      this.showColRename = false
    },
    // 递归树列表
    listToTree(list) {
      const obj = {}
      list.forEach(item => { obj[item.id] = item })
      const res = []
      list.forEach(item => {
        const parent = obj[item.parentId]
        // 如果parent存在则item是叶子节点，否则就是根节点
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(item)
        } else {
          res.push(item)
        }
      })
      return res
    },

    initTableParams() {
      this.sortList = []
      this.pageInfo = {
        currentPage: 1,
        pageSize: 20
      }
    },
    clearScrollTimer() {
      if (this.scrollTimer) {
        window.clearInterval(this.scrollTimer)
        this.scrollTimer = null
      }
    },

    // 给原始数据加上X_ROW_KEY
    addKey() {
      if (this.viewTable.length && this.originTable.length) {
        this.viewTable.forEach((item, index) => {
          this.originTable[index]._X_ROW_KEY = item._X_ROW_KEY
        })
      }
    },
    // 改变格式
    changeFormatTree(val) {
      const target = this.formatList.filter(item => item.id === val)[0]
      this.tableMetaJson.forEach(col => {
        if (col.dataFormatId === val) {
          col.dataFormat = target
        }
      })
    },
    // 获取各列宽度
    getAllColW() {
      const allCol = this.$refs.xTable.getTableColumn().fullColumn
      this.allColWArr = allCol.map(col => col.renderWidth)
    },
    // 列宽拖动事件
    colResizeChange({ $rowIndex, column, columnIndex, $columnIndex, $event }) {
      this.getAllColW()
      const index = columnIndex
      this.tableMetaJson[index].columnWidth = column.renderWidth
      this.saveColumn()
    },
    // 初始化拖拽逻辑
    columnDrop2() {
      this.$nextTick(() => {
        const $table = this.$refs.xTable
        const $el = $table.$el.querySelector('.body--wrapper .vxe-table--body')
        this.sortable2 = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column',
          animation: 150,
          // 被禁止拖动的元素,第一列不允许被拖动
          filter: (evt, item) => {
            const attrs = item.getAttribute('class')
            if (attrs.includes('col--fixed')) {
              this.$message.error('第一列不允许拖拽')
              return true
            }
            return false
          },
          //* ********  拖拽中的事件(滚动条跟着拖拽滚动) *********
          onMove: (evt, originalEvent) => {
            // console.log(evt, originalEvent)
            // 鼠标移动距离
            const moveW = originalEvent.clientX
            // 表格滚动的left值
            // const tableScrollL = $table.getScroll().scrollLeft
            // 计算鼠标拖拽到离页面右边70px时开始滚动
            if (moveW > (this.tableClientW - 100)) {
              $table.scrollTo(this.tableOffsetW)
            } else if (moveW < 400) {
              $table.scrollTo(0)
            } else {
              $table.refreshScroll()
            }
          },
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              const oldThElem = wrapperElem.children[oldIndex]
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, oldThElem)
              } else {
                wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
              }
              this.$message.error('不允许拖到第一列')
              return
            }
            // 获取列索引 columnIndex > fullColumn
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            this.dragFun(fullColumn, oldColumnIndex, newColumnIndex)
            this.dragFun(this.tableMetaJson, oldColumnIndex, newColumnIndex)
            $table.loadColumn(fullColumn)
            this.saveColumn()
          }
        })
      })
    },
    // 拖拽
    dragFun(arr, index1, index2) {
      const oldItem = arr.splice(index1, 1)[0]
      arr.splice(index2, 0, oldItem)
      return arr
    },
    // 表格源数据
    async fetchData(id, flag = true) {
      this.loading = true
      await getTableDetail(id).then(async res => {
        if (res.code === 200) {
          const { tableMetaJson } = res.data
          this.tableMetaJson = tableMetaJson
          // 关联系统参数列
          this.relateSystemCol = this.tableMetaJson.find(item => item.systemKey)
          this.randomKey = Math.round(Math.random() * 80 + 10)
          // 获取tableMetaJson的最大no
          const noArr = this.tableMetaJson.map(item => item.no)
          this.currentNo = Math.max(...noArr)
          if (flag) {
            await this.load2dTable(id)
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async load2dTable(id) {
      const params = {
        ...this.pageInfo,
        tableId: id,
        sortList: this.sortList,
        filterList: this.filterList
      }
      this.loading = true
      const validApi = this.childFlag === 'Y' ? getSubTable : get2dTable
      await validApi(params).then(res => {
        if (res.code === 200) {
          const arr = res.rows && res.rows[0] && res.rows[0].rows
          if (arr && arr.length) {
            this.viewTable = this.formatListMem(arr, 'displayValue')
            // 存一份原始值的表格数据
            this.originTable = this.formatListMem(arr, 'originalValue')
            // 给原始表格加上X_ROW_KEY
            this.addKeyTimer = setTimeout(() => {
              this.addKey()
            }, 100)
            // 获取dictMap
            if (arr && arr.length) {
              const dictMap = {}
              const singleRow = arr[0].cells
              singleRow.forEach(item => {
                const { code, dicItems } = item || {}
                if (dicItems) {
                  dictMap[code] = dicItems
                }
              })
              this.dictMap = dictMap
            }
            this.totalRecords = res.total
          } else {
            // 如果没有行数据则添加一行空数据
            this.viewTable = []
            // this.addRow()
          }
        }
      }).finally(() => {
        this.getAllColW()
        this.loading = false
      })
    },
    // 计算表格各个宽度
    getTableW() {
      const $table = this.$refs.xTable
      const $el = $table.$el.querySelector('.body--wrapper .vxe-table--body')
      this.tableOffsetW = $el.offsetWidth
      this.tableClientW = $table.$el.clientWidth
    },
    // 处理listMemData接口数据
    formatListMem(arr, type) {
      const validArr = []
      arr && arr.forEach(item => {
        const { cells } = item
        const obj = {}
        cells.forEach(cell => {
          const { code } = cell
          obj[code] = cell[type] || cell['originalValue']
        })
        validArr.push(obj)
      })
      return validArr
    },
    // 排序
    sortChangeEvent({ sortList }) {
      console.info(sortList)
      this.sortList = sortList.map(item => {
        const { field, order } = item
        return { columnCode: field, sort: order }
      })
    },
    // 修改列
    updateCol(col) {
      const { code, systemKey, name } = col
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (!name) {
        this.$message.error('请输入列名')
        return
      }
      if (name && !reg.test(name)) {
        this.$message.error('列名只能为汉字、英文、数字、下划线的组合')
        return
      }
      const newTableJson = cloneDeep(this.tableMetaJson)
      newTableJson.forEach(item => {
        if (item.code === code) {
          item.operationType = 'UPDATE'
        }
      })
      this.curSelectCol = col
      if (systemKey && this.relateSystemCol && this.relateSystemCol.code !== code) {
        this.$confirm(`${this.relateSystemCol.name}列已经关联系统参数，是否覆盖？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          newTableJson.forEach(item => {
            if (item.code !== code) {
              item.systemKey = null
            }
          })
          this.tableMetaJson = newTableJson
          this.saveColumn()
        }).catch(() => {
        })
      } else {
        newTableJson.forEach(item => {
          if (this.relateSystemCol) {
            if (item.code !== code && item.code !== this.relateSystemCol.code) {
              item.systemKey = null
            }
          } else {
            if (item.code !== code) {
              item.systemKey = null
            }
          }
        })
        this.tableMetaJson = newTableJson
        this.saveColumn()
      }
    },
    // 横向滚动条滚动到最右边并高亮最后一列
    scrollToCurCol(flag, idx) {
      this.$nextTick(() => {
        const xTable = this.$refs.xTable
        // 由于固定列的动态切换是无状态的，所以需要手动刷新滚动位置
        xTable.refreshColumn().then(() => {
          const allCols = xTable.getTableColumn().fullColumn
          const curHighCol = flag ? allCols[idx] : allCols[allCols.length - 1]
          // 高亮最后一列
          xTable.setCurrentColumn(curHighCol)
          // 滚动到最后一列
          // xTable.scrollToColumn(curHighCol)
          const frontAllColW = flag ? this.allColWArr.slice(0, idx) : this.allColWArr
          const allW = frontAllColW.reduce((prev, cur) => {
            return prev + cur
          }, 0)
          const validScrollW = flag ? allW - 400 : allW
          xTable.scrollTo(validScrollW)
        })
      })
    },
    // 保存列
    saveColumn() {
      const { id, parentId, type, tableName, sqlScript } = this.curTableDetail
      const params = {
        id, parentId, type, tableName, sqlScript, tableMetaJson: this.tableMetaJson
      }
      saveCol(params).then(async res => {
        if (res.code === 200) {
          const msg = '更新列成功'
          this.$message.success(msg)
          await this.refreshTable()
          const idx = this.tableMetaJson.findIndex(item => item.code === this.curSelectCol.code)
          this.clearScrollTimer()
          this.scrollTimer = setTimeout(() => {
            this.scrollToCurCol(true, idx)
          }, 1000)
        }
      }).catch(() => {
        this.refreshTable()
      })
    },
    currentChangeEvent({ row }) {
      this.curRow = row
    },

    clickCol(col) {
      this.curCol = col
      const target = this.tableMetaJson.find(item => item.code === col.field)
      const params = {
        ...this.pageInfo,
        columnCode: target.code,
        tableId: this.curTableId,
        sortList: this.sortList,
        filterList: this.filterList
      }
      calcSumAvg(params).then(res => {
        if (res.code === 200) {
          this.sumavgObj = res.data || { a: null, s: null }
        }
      })
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.refreshTable()
    },
    handleCurrentChange(val) {
      this.pageInfo.currentPage = val
      this.refreshTable()
    },
    // 查询
    searchTable() {
      const requireArr = this.dynamicSearchArr.slice(0, 3)
      const hasVal = requireArr.every(item => item.value)
      if (!hasVal) {
        this.$message.error('标红色*号查询条件必填')
        return
      }
      let baseDataDtStart
      let baseDataDtEnd = ''
      this.dynamicSearchArr.forEach(item => {
        if (item.columnCode === 'baseDataDtStart' && item.value) {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtStart = item.value
        }
        if (item.columnCode === 'baseDataDtEnd') {
          item.value = this.parseTime(item.value, '{y}-{m}-{d}')
          baseDataDtEnd = item.value
        }
      })
      if (new Date(baseDataDtStart).getTime() > new Date(baseDataDtEnd).getTime()) {
        this.$message.error('起始数据日期不能大于截止数据日期')
        return
      }
      this.filterList = this.dynamicSearchArr.map(item => {
        const { columnCode, value, prefixVal, storageType } = item
        return { columnCode, value, prefixVal, storageType, op: 'and' }
      })
      this.refreshTable(false)
    },
    // 重置
    resetQuery() {
      this.dynamicSearchArr.forEach(item => {
        if (item.columnCode === 'dateInterval') {
          item.value = 'DAY'
        } else if (item.columnCode === 'baseDataDtStart' || item.columnCode === 'baseDataDtEnd' || item.columnCode === 'version') {
          item.value = ''
          item.prefixVal = '-1'
          item.op = 'and'
        } else {
          item.value = ''
          item.prefixVal = '0'
        }
      })
      // this.searchTable()
    },
    // 选择人员
    selectOrg(node, instanceId) {
      const { id, label } = node
      this.orgMap[id] = label
    },
    // 格式化单元格
    formatCell({ cellValue, row, column }, config) {
      if (config.systemKey === 'USER') {
        return this.userMap[cellValue]
      }
      if (config.systemKey === 'ORG') {
        return this.orgMap[cellValue]
      }
      return cellValue
    },
    // 打开人员弹框
    openUser(row, config) {
      this.curEditRow = row
      this.curEditCol = config
      this.$refs.selectUser.show()
    },
    // 选中人员
    handleSelectUser(user) {
      const { userId, nickName } = user
      this.curEditRow[this.curEditCol.code] = userId
      this.userMap[userId] = nickName
    },
    changeSystemKey(config) {
      config.systemValue = null
    },
    cellClick(row) {
      console.log(row)
    },
    // 刷新表格
    refreshTable(updateSearch = true) {
      this.updateSearch = updateSearch
      this.fetchData(this.curTableId)
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.table-detail-wrapper {
  background: #fff;
  border-radius: 8px;
  padding: 0 16px;
}
// 全屏弹框样式
.fullscreen-dialog {
  .el-dialog {
    margin: 0 !important;
    height: 100vh;
    width: 100vw;

    &__body {
      height: calc(100vh - 100px);
      overflow: auto;
    }
  }
}
.nested-drawer {
  &::v-deep .el-drawer__container {
    overflow: visible;
  }

  .el-drawer {
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
  }
}

// 逐级向右偏移
// .drawer-style(@index) {
//   margin-left: @index * 50px;
// }

// @for $i from 1 through 5 {
//   .nested-drawer:nth-child(#{$i}) {
//     &::v-deep .el-drawer {
//       @include drawer-style($i);
//     }
//   }
// }
.table-list {
  position: relative;
  margin-top: 10px;
}
.search-container {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 10px;
  min-height: 40px;
  // height: var(--height);
  position: relative;
  overflow: hidden;
  transition: height 0.3s ease;
  .left {
    width: 100%;
    background: #fff;
    &.collapsed {
      max-height: 40px; /* 单行高度 */
      overflow: hidden;
    }
  }

}
.search-form {
  .el-form-item {
    height: 30px;
    margin-bottom: 10px;
  }
  display: flex;
  .left-area {
    flex: 1;
    padding-left: 10px;
  }
  .right-area {
    width: var(--width);
    .el-button--text {
      padding-right: 0px;
      padding-left:0px;
    }
    i {
      color: $primary;
      font-size: 12px;
    }
  }

}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sum-avg {
    font-size: 12px;
    color:#606266;
    margin-right: 20px;
    .label {
      color: $formLabel;
    }
  }
  .right {
    display: flex;
  }
}

.table-box {
    display: flex;
    height: var(--height);
    .add-col {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-left: 12px;
      ::v-deep {
        .el-button--danger {
          background: #F54645;
          &:hover {
            background: #f35f5f;
          }
        }
      }
      .el-button {
        margin: 10px 0 0 0;
      }
    }
    .submmit-btn-wrap{
      position: absolute;
      right: -6px;
      top: 0;
      margin-top: 80px;
      margin-left: 10px;
    }
  }
.dynamic-table {
  width: calc(100vw - 98px);
    ::v-deep.vxe-table--header-wrapper .vxe-icon-edit {
      display: none;
    }
  ::v-deep .vxe-header--row .vxe-header--column {
    height: 30px;
    .vxe-cell--sort {
      height: 14px;
      .vxe-icon-caret-up,.vxe-icon-caret-down {
        font-size:11px;
        color:$secondText;
      }
    }
  }
  ::v-deep .subtable-cell {
    cursor: pointer;
    color: $primary;
    font-weight: bold;
    .vxe-cell--label {
      text-decoration: underline;
    }
  }
  ::v-deep .cur-ref-cell {
    border:2px solid #ff8800;
  }
  ::v-deep .vxe-input,.vxe-select {
    height: 26px;
    line-height: 26px;
  }
}

.custom-col {
  justify-content: space-between;
  i {
    cursor: pointer;
    color: $formLabel;
  }
  .icon-appjisuanqi {
    margin:0 3px;
  }
}
.popver-box {
  text-align: left;
  .popver-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    .title {
      margin: 0 10px;
      &.active {
        color: $primary;
        cursor: pointer;
      }
    }

  }
}
  .row-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 44px;
    padding: 5px;
    .el-checkbox {
      margin-right: 10px;
    }
  }

  .col-left {
    width: calc(100% - 40px);
    span {
      margin-left: 2px;
    }
    i {
      color: $formLabel;
    }
    .flex-vertical-center {
      width: 100% !important;
    }
  }
  .setcol-btn {
    height: 24px;
    .el-button {
      font-size: 13px;
    }
    .del-btn {
      color: $primary
    }
  }
  .vxe-table {
    ::v-deep .vxe-cell {
      padding-right: 3px;
      .vxe-select {
        z-index: 2;
      }
      .vxe-cell--title {
        width: 100%;
      }
    }
    // ::v-deep .vxe-body--column.col--dirty {
    //   background: #fdf6ec !important;
    //   &::before {
    //     display: none !important;
    //   }
    // }
  }
.open-user {
  cursor: pointer;
}
.col-sort {
  display: flex;
  flex-direction: column;
  margin-right: 3px;
  i {
    width: 10px;
    height: 10px;
    color: #c0c4cc;
    &.active {
      color:$primary;
    }
  }
}
.btn-group {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .btn-box {
    padding: 0 10px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: $secondText;
    cursor: pointer;
    &:hover {
      background: #f6f7f8;
      color: $primary;
    }
    .btn-text {
      margin-top: 8px;
      font-weight: bold;
    }
    .btn-icon {
      font-size: 18px;
    }
    .iconfont {
      font-size: 13px;
    }
    &.disabled {
      color: #909399;
    }
  }
  .gap-line {
    width: 1px;
    height: 30px;
    background: #e1e5eb;
  }
}
.process-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color:#606266;
  ::v-deep .el-progress {
    width: 100px;
    .el-progress__text {
      font-size: 12px !important;
    }
  }
}
.custom-control {
  display: flex;
  align-items: center;
  .vxe-select {
    width: 80px;
    border-right: none;
  }
  .el-select:not(.select-datetype,.select-version) {
    width: 80px;
    ::v-deep .el-input__inner {
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      border-right: none;
    }
    .el-input {
      width: 80px;
    }
  }
  .select-datetype {
    width: 80px;
    position: relative;
    &::before {
      content: '*';
      color: #F54645;
      position: absolute;
      left: -10px;
      top: 3px;
      z-index: 1;
      font-size: 18px;
    }
  }
  .el-input  {
    ::v-deep .el-input__inner {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  }
  .el-date-editor {
    width: 150px;
  }
  &.custom-date {
    position: relative;
    margin-right: 8px;
    &::before {
      content: '*';
      color: #F54645;
      position: absolute;
      left: -105px;
      top: 3px;
      z-index: 1;
      font-size: 18px;
    }
  }
}
.quick-input {
  display: flex;
  margin-bottom: 15px;
  .control-box {
    min-width: 110px;
    position: relative;
    margin-right: 25px;
    cursor: pointer;
    &:hover {
      .control-btn {
        background: #e8eaec;
      }
    }
    .control-btn {
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 110px;
      height: 30px;
      border-radius: 4px;
      pointer-events:none;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 13px;
      background: #fff;
      border: 1px solid #e8eaec;
      &.if {
        pointer-events:auto;
      }
    }
  }
  .el-select,.el-cascader {
    width: 100px;
  }
}
.del-select {
  width: 100%;
}
.delversion-option {
  padding-right: 0 !important;
  background: #fff;
  .del-icon {
    float: right;
    color: #8492a6;
    font-size: 13px;
    cursor: pointer;
    height: 34px !important;
    width: 34px !important;
    text-align: center;
    line-height: 34px;
  }
}
.download-link {
  color: $primary;
}
.commit-btn{
  display: inline-flex;
  &:hover{
    cursor: pointer;
  }
}
.back-up {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 15px;
  .top {
    display: flex;
    margin-bottom: 15px;
    .left,.right {
      flex: 1;
      font-weight: bold;
    }
  }
  .backup-col {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .col-name,.col-val {
      flex:1;
      margin: 0 5px;
    }
  }
}
.desc-box {
  height: 100px;
  overflow-y: auto;
  padding: 10px;
  background: #f6f7f8;
  .desc-item {
    margin: 7px 0;
    font-size: 13px;
  }
}
.calc-dialog {
  ::v-deep .el-dialog__body {
    padding-bottom: 10px !important;
  }
}
.bf-name {
  ::v-deep .el-form-item__content {
  }
  .gd-suffix {
    width: 35px;
  }
}
.back-rule {
  display: flex;
  align-items: center;
  ::v-deep .el-form-item {
    display: flex;
    align-items: center;
    .el-form-item__content {
      display: flex;
      align-items: center;
      width: 280px;
      .gd-suffix {
        width: 36px;
      }
    }
  }
}
.translatedY {
  transform: translateY(--translatedY);
}
.expandArea {
  display: inline-block;
  cursor: pointer;
}
</style>
