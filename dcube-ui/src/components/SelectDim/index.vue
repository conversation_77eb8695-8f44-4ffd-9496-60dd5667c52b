<template>
  <el-dialog class="select-dim-dia" :title="title" :visible="true" width="700px" :close-on-click-modal="false" @close="closeSelectDim">

    <el-row :gutter="15">
      <el-col :span="10">
        <el-scrollbar class="tree-content">
          <el-tree
            :expand-on-click-node="false"
            :check-on-click-node="true"
            :default-expand-all="true"
            :highlight-current="true"
            :data="dimTreeData"
            :props="defaultProps"
            @node-click="handleNodeClick"
          />
        </el-scrollbar>
      </el-col>
      <el-col :span="14">
        <el-scrollbar class="tree-content">
          <el-tree :data="dimMemberData" :props="defaultProps" />
        </el-scrollbar>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="closeSelectDim">取 消</el-button>
      <el-button size="mini" type="primary" @click="selectDim">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '选择维度'
    },
    dimTreeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dimMemberData: []
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    handleNodeClick(data) {
      console.log(data)
    },
    selectDim() {
      this.$emit('selectDim')
    },
    closeSelectDim() {
      this.$emit('closeSelectDim')
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-content {
  height: 300px;
  overflow-x: hidden;
  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  ::v-deep .el-scrollbar__bar.is-horizontal {
    display: none;
  }
}
</style>
