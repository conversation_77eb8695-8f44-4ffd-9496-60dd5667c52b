package com.dcube.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.common.core.domain.entity.Permission;
import com.dcube.system.vo.RolePermissionVo;
import com.dcube.system.vo.RoleTableListVo;

import java.util.List;

public interface IPermissionService extends IService<Permission> {

    void saveOrUpdate(RolePermissionVo rolePermissionVo);

    List<RoleTableListVo> selectRoleTableList(String roleId);

    List<Permission> selectUserTableList(Long userId, String userName);
}
