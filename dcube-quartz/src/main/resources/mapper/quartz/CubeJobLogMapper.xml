<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.quartz.mapper.CubeJobLogMapper">

    <resultMap type="CubeJobLog" id="CubeJobLogResult">
        <id property="jobLogId" column="job_log_id"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="invokeTarget" column="invoke_target"/>
        <result property="jobMessage" column="job_message"/>
        <result property="status" column="status"/>
        <result property="exceptionInfo" column="exception_info"/>
        <result property="jobId" column="job_id"/>
        <result property="startTime" column="start_time"/>
        <result property="stopTime" column="stop_time"/>
    </resultMap>

    <sql id="selectJobLogVo">
        select job_log_id,
               job_name,
               job_group,
               invoke_target,
               job_message,
               status,
               exception_info,
               job_id,
               start_time,
               stop_time
        from cube_job_log
    </sql>

    <select id="selectJobLogList" parameterType="CubeJobLog" resultMap="CubeJobLogResult">
        <include refid="selectJobLogVo"/>
        <where>
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND job_group = #{jobGroup}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="invokeTarget != null and invokeTarget != ''">
                AND invoke_target like concat('%', #{invokeTarget}, '%')
            </if>
            <if test="jobId != null and jobId != ''">
                AND job_id = #{jobId}
            </if>
            <if test="params.beginTime != null"><!-- 开始时间检索 -->
                and start_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null"><!-- 结束时间检索 -->
                and start_time &lt;= #{params.endTime}
            </if>
        </where>
        order by start_time desc
    </select>

    <select id="selectJobLogAll" resultMap="CubeJobLogResult">
        <include refid="selectJobLogVo"/>
    </select>

    <select id="selectJobLogById" parameterType="Long" resultMap="CubeJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_log_id = #{jobLogId}
    </select>

    <delete id="deleteJobLogById" parameterType="Long">
        delete
        from cube_job_log
        where job_log_id = #{jobLogId}
    </delete>

    <delete id="deleteJobLogByIds" parameterType="Long">
        delete from cube_job_log where job_log_id in
        <foreach collection="array" item="jobLogId" open="(" separator="," close=")">
            #{jobLogId}
        </foreach>
    </delete>

    <update id="cleanJobLog">
        truncate table cube_job_log
    </update>

    <insert id="insertJobLog" parameterType="CubeJobLog">
        insert into cube_job_log(
        <if test="jobLogId != null and jobLogId != 0">job_log_id,</if>
        <if test="jobName != null and jobName != ''">job_name,</if>
        <if test="jobGroup != null and jobGroup != ''">job_group,</if>
        <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
        <if test="jobMessage != null and jobMessage != ''">job_message,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="exceptionInfo != null and exceptionInfo != ''">exception_info,</if>
        <if test="jobId != null and jobId != ''">job_id,</if>
        <if test="startTime != null">start_time,</if>
        <if test="stopTime != null">stop_time</if>
        )values(
        <if test="jobLogId != null and jobLogId != 0">#{jobLogId},</if>
        <if test="jobName != null and jobName != ''">#{jobName},</if>
        <if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
        <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
        <if test="jobMessage != null and jobMessage != ''">#{jobMessage},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="exceptionInfo != null and exceptionInfo != ''">#{exceptionInfo},</if>
        <if test="jobId != null and jobId != ''">#{jobId},</if>
        <if test="startTime != null">#{startTime},</if>
        <if test="stopTime != null">#{stopTime}</if>
        )
    </insert>

</mapper> 