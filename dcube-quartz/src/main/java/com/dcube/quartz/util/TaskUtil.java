package com.dcube.quartz.util;

import com.dcube.common.constant.enums.JobOperateTypeEnums;
import com.dcube.common.constant.enums.StateEnum;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.uuid.IdUtils;
import com.dcube.quartz.constants.TaskConstant;
import com.dcube.quartz.exception.TaskExecutionException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

@Slf4j
@Component
public class TaskUtil {

    private static final Map<String, List<String>> JOB_TASK_MAP = Collections.synchronizedMap(new HashMap<>());

    private final static Map<String, String> TASK = Collections.synchronizedMap(new HashMap<>());

    private TaskUtil() {

    }

    public static ReportDto getTaskInfoCache(String taskId) {
        return TaskReport.get(taskId);
    }

    public static ReportDto getTaskInfoCache(String taskId, Function<String, ReportDto> function) {
        return TaskReport.get(taskId, function);
    }

    /**
     * @param tableId 二维表id
     * @param type
     * @return 返回当前表正在执行的taskId
     */
    public static String putTableCache(Integer tableId, JobOperateTypeEnums type) throws TaskExecutionException {
        return putTableCache(tableId, type, false);
    }


    /**
     * @param tableId 二维表id
     * @return 返回当前表正在执行的taskId
     */
    public static String putTableCache(Integer tableId, JobOperateTypeEnums type, boolean inner) throws TaskExecutionException {
        String taskId = "";
        String tableTaskId = MapUtils.getString(TASK, "grid." + tableId);
        if (StringUtils.isNotEmpty(tableTaskId)) {
            ReportDto reportDto = getTaskInfoCache(tableTaskId);
            if (reportDto != null && !(reportDto.getStatus() == TaskStatusEnums.COMPLETE || reportDto.getStatus() == TaskStatusEnums.ERROR)) {
                // 先加载数据然后再执行保存数据的规则
                if (inner && (reportDto.getType() == JobOperateTypeEnums.LOAD_DATA_SOURCE || reportDto.getType() == JobOperateTypeEnums.LOAD_DATA_VERSION) && type == JobOperateTypeEnums.SAVE_DATA) {
                    taskId = tableTaskId;
                }
                if (!StringUtils.equals(tableTaskId, taskId)) {
                    throw new TaskExecutionException("当前表已经有任务" + reportDto.getTaskName() + "正在执行，请等待任务执行完成再操作！");
                }
            }
        }
        if (StringUtils.isEmpty(taskId)) {
            taskId = IdUtils.getSnowflakeNextIdStr();
            ThreadLocalUtils.set(TaskUtil.class, TaskConstant.TASK_ID, taskId);
            TASK.put("grid." + tableId, taskId);
        }
        return taskId;
    }

    /**
     * 更新表任务状态
     *
     * @return
     */
    public static void updateTableTaskStatus(Integer tableId, TaskStatusEnums taskStatus, String msg) {
        String tableTaskId = MapUtils.getString(TASK, "grid." + tableId);
        updateTaskStatus(tableTaskId, taskStatus, msg);
    }

    /**
     * 更新任务状态
     *
     * @return
     */
    public static void updateTaskStatus(String taskId, TaskStatusEnums taskStatus, String msg) {
        if (StringUtils.isNotEmpty(taskId)) {
            ReportDto reportDto = getTaskInfoCache(taskId);
            if (reportDto != null) {
                reportDto.setStatus(taskStatus).setMsg(msg);
                if (taskStatus == TaskStatusEnums.ERROR) {
                    reportDto.setState(StateEnum.FAILED.getCode());
                }
            }
        }
    }

    /**
     * 获取表的任务id
     *
     * @param tableId
     * @return
     */
    public static String getTableTaskId(Integer tableId) {
        return MapUtils.getString(TASK, "grid." + tableId);
    }

    /**
     * 获取表的规则（不要直接使用）
     *
     * @param tableId
     * @return
     */
    public static ReportDto getTableTask(Integer tableId) {
        String tableTaskId = MapUtils.getString(TASK, "grid." + tableId);
        if (StringUtils.isNotEmpty(tableTaskId)) {
            return getTaskInfoDetail(tableTaskId);
        }
        return null;
    }

    /**
     * 获取任务的规则（不要直接使用）
     *
     * @param jobId
     * @return
     */
    public static List<ReportDto> getJobTableTask(Integer jobId) {
        List<String> taskIds = MapUtils.getObject(JOB_TASK_MAP, "job." + jobId);
        if (CollectionUtils.isNotEmpty(taskIds)) {
            List<ReportDto> list = new ArrayList<>(taskIds.size());
            for (String taskId : taskIds) {
                ReportDto reportDto = getTaskInfoDetail(taskId);
                if (reportDto == null) {
                    reportDto = getTaskInfoCache(taskId);
                }
                list.add(reportDto);
            }
            return list;
        }
        return Collections.emptyList();
    }


    public static ReportDto getTableCache(Integer tableId, JobOperateTypeEnums taskType) {
        String tableTaskId = MapUtils.getString(TASK, "grid." + tableId);
        if (StringUtils.isNotEmpty(tableTaskId)) {
            return getTaskInfoCache(tableTaskId, v -> {
                ReportDto reportDto = new ReportDto().setTaskId(tableTaskId).setTaskName(taskType.getName()).setType(taskType).setStatus(TaskStatusEnums.WILL_START).setStartTime(System.currentTimeMillis());
                log.info("创建任务id：{}，hashCode：{}", tableTaskId, reportDto.hashCode());
                return reportDto;
            });
        }
        return null;

    }


    public static ReportDto getTaskInfoDetail(String taskId) {
        ReportDto reportDto = getTaskInfoCache(taskId);
        if (reportDto == null) {
            return null;
        }
        ReportDto ret = new ReportDto();
        if (reportDto.getStatus() == null || reportDto.getStatus() == TaskStatusEnums.WILL_START) {
            ret.setTaskId(taskId).setTaskName(StringUtils.defaultIfEmpty(reportDto.getTaskName(), "任务正在启动")).setProgress("0%");
        } else {
            BeanUtils.copyProperties(reportDto, ret);
            if (ret.getStatus() == TaskStatusEnums.ERROR) {
                return ret;
            }
            double progress;
            Long total = ret.getTotalRecord();
            if (ret.getStatus() == TaskStatusEnums.COMPLETE) {
                progress = 1.0;
            } else if (total == null || total == 0) {
                ret.setProgress("NaN");
                return ret;
            } else {
                long count = 0;
                if (reportDto.getCurrentRecord() != null) {
                    if (reportDto.getCurrentRecord() instanceof Long) {
                        count += (Long) reportDto.getCurrentRecord();
                    } else if (reportDto.getCurrentRecord() instanceof AtomicLong) {
                        count += ((AtomicLong) reportDto.getCurrentRecord()).get();
                    }
                }
                progress = count / (double) total;
            }
            progress = (int) (progress * 100.0);
            if (progress > 100.0) {
                log.error("{}--任务进度超过100%，为{}，reportDto:{}", taskId, progress, reportDto);
                progress = 100.0;
            }
            ret.setProgress(progress + "%");
        }
        return ret;
    }

    public static AjaxResult getTaskIdAjaxResult(String taskId) {
        Map<String, Object> data = Maps.newHashMapWithExpectedSize(1);
        data.put("taskId", taskId);
        return AjaxResult.success(data);
    }

    public static AjaxResult getTaskIdAjaxResult() {
        String taskId = ThreadLocalUtils.get(TaskUtil.class, TaskConstant.TASK_ID);
        if (StringUtils.isNotEmpty(taskId)) {
            return getTaskIdAjaxResult(taskId);
        } else {
            return AjaxResult.success();
        }
    }

    public static void removeJobCache(Integer jobId) {
        JOB_TASK_MAP.remove("job." + jobId);
    }

    public static void putJobCache(Integer jobId, Integer tableId, JobOperateTypeEnums type) {
        String taskId = putTableCache(tableId, type, false);
        List<String> strings = JOB_TASK_MAP.computeIfAbsent("job." + jobId, k -> Collections.synchronizedList(new ArrayList<>()));
        strings.add(taskId);
    }

}
