package com.dcube.quartz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.dcube.common.constant.enums.JobOperateTypeEnums;
import com.dcube.common.constant.enums.TableBackupSuffixTypeEnum;
import com.dcube.common.constant.enums.YNEnum;
import com.dcube.quartz.constants.enums.JobTypeEnums;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 事件进程详情对象 cube_job_detail
 *
 * @date 2023-10-16
 */
@TableName("cube_job_detail")
@EqualsAndHashCode
@ToString
@Data
public class CubeJobDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 进程名称
     */
    private String processName;

    /**
     * 任务id
     */
    private Integer jobId;

    /**
     * 任务类型（1:表格进程；2:规则进程）
     */
    private JobTypeEnums jobType;

    /**
     * 操作类型（1:载入源数据；2:载入备份数据；3:释放内存；4:备份至数据库；5:删除备份；6:执行规则；7:保存规则；8:保存数据；9:删除数据；10:生成数据）
     */
    private JobOperateTypeEnums jobOperateType;

    /**
     * 二维表id
     */
    private Integer tableId;

    /**
     * 二维表名
     */
    private String tableName;

    /**
     * 数据版本
     */
    private Integer dataVersion;

    /**
     * 数据版本名称
     */
    private String dataVersionName;

    /**
     * 数据源
     */
    private String dataSource;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 进度
     */
    @TableField(exist = false)
    private String progress;

    /**
     * 后缀类型
     */
    @Schema(description = "后缀类型")
    private TableBackupSuffixTypeEnum suffixType;

    /**
     * 是否自动覆盖同名备份
     */
    @Schema(description = "是否自动覆盖同名备份（Y是；N否）")
    private YNEnum isOverwrite;

}
