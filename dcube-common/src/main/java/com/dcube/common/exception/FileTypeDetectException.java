package com.dcube.common.exception;

/**
 * 文件类型检测异常
 */
public class FileTypeDetectException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    public FileTypeDetectException(Throwable e) {
        super(e.getMessage(), e);
    }

    public FileTypeDetectException(String message) {
        super(message);
    }

    public FileTypeDetectException(String message, Throwable throwable) {
        super(message, throwable);
    }
}
