package com.dcube.common.annotation;

import com.dcube.common.enums.ThreadLocalCacheType;

import java.lang.annotation.*;

/**
 * 线程变量注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
//@Repeatable(ThreadLocalCache.ThreadLocalCaches.class)
public @interface ThreadLocalCache {
    ThreadLocalCacheType value() default ThreadLocalCacheType.TL;

//    @Target({ElementType.METHOD})
//    @Retention(RetentionPolicy.RUNTIME)
//    @interface ThreadLocalCaches {
//        ThreadLocalCache[] value();
//    }
}
