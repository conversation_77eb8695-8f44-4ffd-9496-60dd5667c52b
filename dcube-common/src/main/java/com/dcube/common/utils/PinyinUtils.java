package com.dcube.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.math3.random.RandomDataGenerator;

import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @创建人 zhouhx
 * @创建时间 2023/2/27 16:40
 * @描述
 */
public class PinyinUtils {
    /**
     * 将字符串中的中文转化为拼音,其他字符不变
     *
     * @param chinese
     * @return
     */
    public static String getPingYin(String chinese) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
       /* 设置转换后拼音的大小写
             UPPERCASE：大写  (ZHONG)
            LOWERCASE：小写  (zhong)
        */
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
      /* 设置转换后拼音是否输出拼接音调
            WITHOUT_TONE：无音标  (zhong)
            WITH_TONE_NUMBER：1-4数字表示英标  (zhong4)
            WITH_TONE_MARK：直接用音标符（必须WITH_U_UNICODE否则异常）  (zhòng)
       */
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
      /* 设置转换后拼音是否输出韵母
            WITH_V：用v表示ü  (nv)
            WITH_U_AND_COLON：用"u:"表示ü  (nu:)
            WITH_U_UNICODE：直接用ü (nü)
       */
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        StringBuilder sb = new StringBuilder();
        char[] arr = chinese.trim().toCharArray();
        try {
            for (char c : arr) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    //toHanyuPinyinStringArray有个容错判断 如果传入的不是汉字，就不能转换成拼音，那么直接返回null可以用正则表达式判断是否是中文,Character.toString(input[i]).matches("[\\u4E00-\\u9FA5]+")
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    sb.append(temp[0]);
                    continue;
                }

                sb.append(c);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    /**
     * 获取汉字串拼音首字母，英文字符不变
     *
     * @param chinese 汉字串
     * @param type    小于等于0：取汉字串拼音首字母；大于0：获取汉字串全拼音
     * @return 汉语拼音首字母
     */
    public static String getPingYin(String chinese, int type) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
       /* 设置转换后拼音的大小写
             UPPERCASE：大写  (ZHONG)
            LOWERCASE：小写  (zhong)
        */
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
      /* 设置转换后拼音是否输出拼接音调
            WITHOUT_TONE：无音标  (zhong)
            WITH_TONE_NUMBER：1-4数字表示英标  (zhong4)
            WITH_TONE_MARK：直接用音标符（必须WITH_U_UNICODE否则异常）  (zhòng)
       */
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        StringBuilder sb = new StringBuilder();
        char[] arr = chinese.trim().toCharArray();
        try {
            for (char c : arr) {
                //ASCII码表上一个128个字符,对应的索引为0-127,大于128表示非ASCII字符
                if (c > 128) {
                    //toHanyuPinyinStringArray有个容错判断 如果传入的不是汉字，就不能转换成拼音，那么直接返回null可以用正则表达式判断是否是中文,Character.toString(input[i]).matches("[\\u4E00-\\u9FA5]+")
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (temp != null) {
                        //输出全拼音
                        if (type <= 0) {
                            sb.append(PinyinHelper.toHanyuPinyinStringArray(c, format)[0]);
                        }
                        //输出拼音首字母
                        else {
                            sb.append(temp[0].charAt(0));
                        }
                    }
                    continue;
                }
                sb.append(c);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return sb.toString().replaceAll("\\W", "").trim();
    }

    /***
     * 将汉字转成拼音(取首字母或全拼)
     * @param chinese
     * @param full 是否全拼
     * @return
     */
    public static String convertHanzi2Pinyin(String chinese,boolean upper,boolean full) {
        /*
         * ^[\u2E80-\u9FFF]+$ 匹配所有东亚区的语言
         * ^[\u4E00-\u9FFF]+$ 匹配简体和繁体
         * ^[\u4E00-\u9FA5]+$ 匹配简体
         */
        String regExp="^[\u4E00-\u9FFF]+$";
        StringBuffer sb=new StringBuffer();
        if(chinese==null||"".equals(chinese.trim())) {
            return "";
        }
        String pinyin="";
        for (int i = 0; i < chinese.length(); i++) {
            char unit = chinese.charAt(i);
            // 是汉字，则转拼音
            if (match(String.valueOf(unit), regExp)) {
                pinyin = convertSingleHanzi2Pinyin(unit);
                if (full) {
                    sb.append(pinyin);
                } else {
                    sb.append(pinyin.charAt(0));
                }
            } else {
                sb.append(unit);
            }
        }
        return upper?StringUtils.upperCase(sb.toString()):sb.toString();
    }

    /***
     * 将单个汉字转成拼音
     * @param hanzi
     * @return
     */
    private static String convertSingleHanzi2Pinyin(char hanzi) {
        HanyuPinyinOutputFormat outputFormat = new HanyuPinyinOutputFormat();
        outputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        String[] res;
        StringBuffer sb=new StringBuffer();
        try {
            res = PinyinHelper.toHanyuPinyinStringArray(hanzi,outputFormat);
            sb.append(res[0]);//对于多音字，只用第一个拼音
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return sb.toString();
    }

    /***
     * @param str 源字符串
     * @param regex 正则表达式
     * @return 是否匹配
     */
    public static boolean match(String str,String regex){
        Pattern pattern=Pattern.compile(regex);
        Matcher matcher=pattern.matcher(str);
        return matcher.find();
    }

    public static void main(String[] args) {
    }
}

