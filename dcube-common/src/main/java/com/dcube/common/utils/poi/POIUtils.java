/*
 * Datart
 * <p>
 * Copyright 2021
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dcube.common.utils.poi;

import com.dcube.common.enums.FileTypeEnum;
import com.dcube.common.enums.JavaType;
import com.dcube.common.enums.ValueType;
import com.dcube.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.rmi.ServerException;
import java.util.*;

@Slf4j
public class POIUtils {

    private static IndexedColorMap indexedColorMap = new DefaultIndexedColorMap();

    public static void save(Workbook workbook, String path, boolean cover) throws IOException {
        if (workbook == null || path == null) {
            return;
        }
        File file = new File(path);
        if (file.exists()) {
            if (cover) {
                file.delete();
            } else {
                throw new ServerException("file (" + path + ")  already exists");
            }
        } else {
            file.getParentFile().mkdirs();
        }
        try (FileOutputStream fos = new FileOutputStream(file)) {
            workbook.write(fos);
        }
    }

    private static void mergeSheetCell(Sheet sheet, List<CellRangeAddress> mergeCells) {
        for (CellRangeAddress cellRangeAddress : mergeCells) {
            sheet.addMergedRegion(cellRangeAddress);
        }
    }

    public static ExcelData loadExcel(String path,Integer limit) throws IOException {
        ExcelData excelData = new ExcelData();
        excelData.setFilePath(path);
        LinkedList<List<Object>> rows = new LinkedList<>();
        try (InputStream inputStream = new FileInputStream(path)) {
            Workbook workbook;
            if (path.toUpperCase().endsWith(FileTypeEnum.XLS.getSuffix())) {
                workbook = new HSSFWorkbook(inputStream);
            } else if (path.toUpperCase().endsWith(FileTypeEnum.XLSX.getSuffix())) {
                workbook = new XSSFWorkbook(inputStream);
            } else {
                throw new ServerException("excel文件格式有误。");
            }

            if (workbook.getNumberOfSheets() < 1) {
                throw new ServerException("excel文件为空sheet。");
            }
            // 只处理第一个sheet
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.rowIterator();
            Row row0 = sheet.getRow(0);
            if (row0 == null) {
                throw new ServerException("excel文件为空。");
            }
            int columns = row0.getPhysicalNumberOfCells();
            excelData.setTotalRows(sheet.getLastRowNum());
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                LinkedList<Object> cellValues = new LinkedList<>();
                for (int i = 0; i < columns; i++)
                    cellValues.add(readCellValue(row.getCell(i)));
                rows.add(cellValues);
                if(Objects.nonNull(limit) && rows.size()==limit.intValue()+1){
                    break;
                }
            }
        }

        List<Column> columns = POIUtils.inferHeader(rows);
        excelData.setColumns(columns);
        excelData.setValues(POIUtils.parseValues(rows, columns));
        return excelData;
    }

    private static Object readCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                }
                return ((XSSFCell) cell).getRawValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            default:
                return cell.getStringCellValue();
        }
    }

    private static CellStyle getCellStyle(Sheet sheet, Object val, String fmt) {
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        DataFormat dataFormat = sheet.getWorkbook().createDataFormat();
        if (StringUtils.isNotBlank(fmt)) {
            cellStyle.setDataFormat(dataFormat.getFormat(fmt));
        } else if (val instanceof Number) {
        } else if (val instanceof Date) {
        } else {
            cellStyle.setDataFormat(dataFormat.getFormat("General"));
        }
        return cellStyle;
    }

    private static CellStyle getHeaderCellStyle(Sheet sheet) {
        XSSFCellStyle cellStyle = (XSSFCellStyle) sheet.getWorkbook().createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        XSSFColor grayColor = new XSSFColor(new java.awt.Color(220, 220, 220), indexedColorMap);
        XSSFColor whiteColor = new XSSFColor(new java.awt.Color(240, 240, 240), indexedColorMap);
        cellStyle.setTopBorderColor(whiteColor);
        cellStyle.setRightBorderColor(whiteColor);
        cellStyle.setBottomBorderColor(whiteColor);
        cellStyle.setLeftBorderColor(whiteColor);
        cellStyle.setFillForegroundColor(grayColor);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return cellStyle;
    }

    private static void setCellValue(Cell cell, Object val) {
        if (val == null) {
            cell.setCellValue("");
            return;
        }
        try {
            JavaType javaType = JavaType.valueOf(val.getClass().getSimpleName().toUpperCase());
            switch (javaType) {
                case BIGDECIMAL:
                case BYTE:
                case SHORT:
                case INTEGER:
                case LONG:
                case FLOAT:
                case DOUBLE:
                    cell.setCellValue(new BigDecimal(val.toString()).doubleValue());
                    break;
                case BOOLEAN:
                    cell.setCellValue((Boolean) val);
                    break;
                case DATE:
                default:
                    cell.setCellValue(val.toString());
                    break;
            }
        } catch (IllegalArgumentException e) {
            cell.setCellValue(val.toString());
        }
    }

    public static List<Column> inferHeader(List<List<Object>> values) {
        List<Object> typedValues = values.get(0);
        LinkedList<Column> columns = new LinkedList<>();
        boolean isHeader = typedValues.stream()
                .allMatch(typedValue -> typedValue instanceof String);
        if (isHeader) {
            typedValues = values.size() > 1 ? values.get(1) : typedValues;
            for (int i = 0; i < typedValues.size(); i++) {
                Column column = new Column();
                ValueType valueType = DataTypeUtils.javaType2DataType(typedValues.get(i));
                column.setType(valueType);
                String name = values.get(0).get(i).toString();
                column.setName(StringUtils.isBlank(values.get(0).get(i).toString()) ? "col" + i : name);
                columns.add(column);
            }
            values.remove(0);
        } else {
            for (int i = 0; i < typedValues.size(); i++) {
                Column column = new Column();
                ValueType valueType = DataTypeUtils.javaType2DataType(typedValues.get(i));
                column.setType(valueType);
                column.setName("column" + i);
                columns.add(column);
            }
        }
        return columns;
    }

    public static List<List<Object>> parseValues(List<List<Object>> values, List<Column> columns) {
        if (CollectionUtils.isEmpty(values)) {
            return values;
        }
        if (values.get(0).size() != columns.size()) {
            try {
                throw new ServerException("文件格式有误");
            } catch (ServerException e) {
                throw new RuntimeException(e);
            }
        }
        values.stream().forEach(vals -> {
            for (int i = 0; i < vals.size(); i++) {
                Object val = vals.get(i);
                if (val == null) {
                    vals.set(i, null);
                    continue;
                }
                switch (columns.get(i).getType()) {
                    case STRING:
                        val = val.toString();
                        break;
                    case NUMERIC:
                        if (val instanceof Number) {
                            break;
                        }
                        if (StringUtils.isBlank(val.toString())) {
                            val = null;
                        } else if (NumberUtils.isDigits(val.toString())) {
                            val = Long.parseLong(val.toString());
                        } else if (NumberUtils.isNumber(val.toString())) {
                            val = Double.parseDouble(val.toString());
                        } else {
                            val = null;
                        }
                        break;
                    case DATE:
                        if (val instanceof Date) {
                            val = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, (Date) val);
                        }
                        break;
                    default:
                }
                vals.set(i, val);
            }
        });
        return values;
    }
}
