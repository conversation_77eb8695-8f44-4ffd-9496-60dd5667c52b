package com.dcube.common.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.CryptoException;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CryptoUtil {

    @Autowired
    private RSA rsa;

    public String encrypt(String content, KeyType keyType) {
        return encrypt(rsa, content, keyType);
    }

    public String decryptStr(String encryptStr, KeyType keyType) {
        try {
            return decryptStr(rsa, encryptStr, keyType);
        } catch (CryptoException e) {
            log.error("解密时出现异常，字符串：{}", encryptStr, e);
            return "";
        }
    }

    public static String encrypt(RSA rsa, String content, KeyType keyType) {
        return rsa.encryptBase64(StrUtil.bytes(content, CharsetUtil.CHARSET_UTF_8), keyType);
//        return content;
    }

    public static String decryptStr(RSA rsa, String encryptStr, KeyType keyType) {
        return rsa.decryptStr(encryptStr, keyType);
//        return encryptStr;
    }

}
