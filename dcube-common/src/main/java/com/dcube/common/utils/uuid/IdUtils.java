package com.dcube.common.utils.uuid;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReflectUtil;
import com.dcube.common.properties.SnowflakeProperties;
import com.dcube.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * ID生成器工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class IdUtils {

    private static volatile Snowflake snowflake;

    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 随机UUID
     */
    public static String fastUUID() {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID() {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 获取数据中心ID
     */
    public static Long getSystemPropertyDataCenterId() {
        String datacenterId = System.getProperty("datacenterId");
        if (datacenterId != null) {
            return Long.parseLong(datacenterId);
        }
        return null;
    }

    /**
     * 获取机器ID，使用进程ID配合数据中心ID生成
     */
    public static Long getSystemPropertyWorkerId() {
        String workerIdStr = System.getProperty("workerId");
        if (workerIdStr != null) {
            return Long.parseLong(workerIdStr);
        }
        return null;
    }

    /**
     * 简单获取Snowflake 的 nextId
     * 终端ID 数据中心ID 默认为PID和MAC地址生成
     *
     * @return nextIdStr
     */
    public static String getSnowflakeNextIdStr() {
        if (snowflake == null) {
            synchronized (IdUtils.class) {
                if (snowflake == null) {
                    SnowflakeProperties snowflakeProperties = SpringUtils.getBean(SnowflakeProperties.class);
                    Long datacenterId = snowflakeProperties.getDatacenterId();
                    Long workerId = snowflakeProperties.getWorkerId();
                    if (datacenterId != null) {
                        if (workerId != null) {
                            snowflake = IdUtil.getSnowflake(workerId, datacenterId);
                        }
                    } else {
                        if (workerId != null) {
                            snowflake = IdUtil.getSnowflake(workerId);
                        }
                    }
                    if (snowflake == null) {
                        snowflake = IdUtil.getSnowflake();
                    }
                    log.info("获取到的Snowflake算法：dataCenterId：{} - workerId：{}", ReflectUtil.getFieldValue(snowflake, "dataCenterId"), ReflectUtil.getFieldValue(snowflake, "workerId"));
                }
            }
        }
        return snowflake.nextIdStr();
    }

}
