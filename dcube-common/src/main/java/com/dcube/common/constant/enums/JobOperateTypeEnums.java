package com.dcube.common.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型
 */
@Getter
@AllArgsConstructor
public enum JobOperateTypeEnums {
    /**
     * 载入源数据
     */
    LOAD_DATA_SOURCE("载入源数据", "1"),
    /**
     * 载入备份数据
     */
    LOAD_DATA_VERSION("载入备份数据", "2"),
    /**
     * 释放内存
     */
    RELEASE_MEMORY("释放内存", "3"),
    /**
     * 备份至数据库
     */
    BACKUP("备份至数据库", "4"),
    /**
     * 删除备份
     */
    DELETE_BACKUP("删除备份", "5"),
    /**
     * 执行规则
     */
    EXECUTE_RULE("执行规则", "6"),
    /**
     * 保存规则
     */
    SAVE_RULE("保存规则", "7"),
    /**
     * 保存数据
     */
    SAVE_DATA("保存数据", "8"),
    /**
     * 删除数据
     */
    DELETE_DATA("删除数据", "9"),
    /**
     * 生成数据
     */
    GENERATE_DATA("生成数据", "10"),
    ;

    private final String name;

    @EnumValue
    private final String type;


}
