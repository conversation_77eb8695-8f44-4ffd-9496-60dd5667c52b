package com.dcube.common.constant;

import cn.hutool.core.collection.ListUtil;
import io.jsonwebtoken.Claims;

import java.util.List;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.dcube"};

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.dcube.common.utils.file", "com.dcube.common.config"};
    public static final String COL_NAME_PREFIX = "COL_";

    public static final String TABLE_PREFIX = "T_";

    public static final String TABLE_BACKUP_PREFIX = "_BACKUP_";

    public static final String TABLE_BACKUP_VERSION_PREFIX = "VERSION_";

    public static final int TABLE_RANDOM_LENGTH = 10;
    public static final int COL_RANDOM_LENGTH = 6;
    public static final String PRIMARY_KEY = "_PRIMARY_KEY";
    public static final String CREATE_TIME = "_CREATE_TIME";
    public static final String UPDATE_TIME = "_UPDATE_TIME";
    public static final String CREATE_BY = "_CREATE_BY";
    public static final String VARCHAR = "VARCHAR";
    public static final String TIMESTAMP = "TIMESTAMP";

    //字典ID
    public static final String DICT_ID = "7c8a84e530fcd75b65c80f64f6bd3bf9";

    public static final List<String> NUMBER_TYPE_LIST = ListUtil.of("INT", "INTEGER", "BIGINT", "LONG", "DECIMAL", "DOUBLE", "DATE", "DATETIME");

    public static final List<String> STRING_TYPE_LIST = ListUtil.of("CHAR", "VARCHAR");

    public static final String DEFAULT_JOB_GROUP = "DEFAULT";

    public static final int CPU_PROCESSORS = Runtime.getRuntime().availableProcessors();

    public static final Integer TIMEOUT = 30;
    public static final int PREVIEW_COUNT = 10;
    public static final String EXCEL_IMP_META_CACHE_PREFIX = "meta:";
    public static final String EXCEL_IMP_META_JSON_CACHE_PREFIX = "meta_json:";

    public static final String EXCEL_IMP_VIEW_CACHE_PREFIX = "view:";

    public static final String WEB_SOCKET_TOKEN_KEY = "Sec-WebSocket-Protocol";
    public static final Short[] CELL_TYPE_DATE_IDX_ARRAY = new Short[]{176,177,178,181};
    public static final int MAX_EXPORT_COUNT = 1000000;

    public static final int ROWS_PER_SHEET = 80000;
    public static final int ROWS_PER_EXCEL = 100000;
    public static final int CHUNK_SIZE = 5000;
    public static final int DOUBLE_NUMBER_LENGTH = 9;
}
