package com.dcube.common.enums;

import java.util.Arrays;

public enum ValueType {

    STRING,

    NUMERIC,

    NUMBER,

    DECIMAL,

    DATE,

    DATETIME,

    TIMESTAMP,

    BOOLEAN,

    VARCHAR,

    INTEGER,

    INT,

    TINYINT,

    BIGINT,

    FLOAT,

    DOUBLE;

    public static ValueType find(String code) {
        ValueType yypeEnum = Arrays.stream(ValueType.values())
                .filter(input -> input.name().equals(code))
                .findFirst()
                .orElse(null);
        return yypeEnum;
    }
}
