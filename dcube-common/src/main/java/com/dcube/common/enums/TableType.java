package com.dcube.common.enums;

/**
 * 数据表类型，分租，表
 */
public enum TableType {
    GROUP("GROUP", "分组"), TABLE("TABLE", "表");

    private final String code;
    private final String name;

    TableType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
