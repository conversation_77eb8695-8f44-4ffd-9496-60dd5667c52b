package com.dcube.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
public enum DataEditionEnum {

    /**
     * 无权限
     */
    N(0, "无权限"),

    /**
     * 有权限
     */
    Y(1, "有权限");

    private final Integer code;

    private final String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DataEditionEnum enumItem : DataEditionEnum.values()) {
            Map<String, Object> entry = new HashMap<>();
            entry.put("id", enumItem.getCode());
            entry.put("description", enumItem.getDescription());
            list.add(entry);
        }
        return list;
    }
}
