package com.dcube.common.enums;

import com.dcube.common.utils.StringUtils;

import java.util.Arrays;

/**
 * 显示格式类型
 */
public enum FormatType {

    NUMBER_NO("21", "无千分分隔"),
    NUMBER_THREE("20", "有千分分隔"),
    DATE_CN("10", "yyyy年MM月dd日"),
    DATE_NORMAL("11", "yyyy-MM-dd"),
    DATE_EN("12", "yyyy/MM/dd"),
    DATE_EN_MON("13", "yyyy/MM"),
    DATETIME_NORM("14", "yyyy-MM-dd HH:mm:ss"),
    ;

    private final String code;
    private final String info;

    FormatType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static FormatType getFormatTypeByCode(String code) {
        return Arrays.stream(FormatType.values())
                .filter(v -> StringUtils.equals(v.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
