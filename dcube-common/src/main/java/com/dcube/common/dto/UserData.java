package com.dcube.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class UserData {
    @ExcelProperty(value = "用户编号")
    private String userName;

    @ExcelProperty(value = "姓名")
    private String nickName;

    @ExcelProperty(value = "部门名称")
    private String deptName;


}