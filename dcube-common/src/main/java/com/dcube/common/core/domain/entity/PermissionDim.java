package com.dcube.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
@TableName("cube_permission_dim")
public class PermissionDim extends Model<PermissionDim> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 多维表ID
     */
    private String tableId;

    /**
     * 数据范围
     */
    private int dataScope;

    /**
     * 表格定义
     */
    private int tableDefinition;

    /**
     * 数据编辑
     */
    private int dataEdition;
}
