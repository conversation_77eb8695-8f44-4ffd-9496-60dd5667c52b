package com.dcube.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
@TableName("cube_permission")
public class Permission extends Model<Permission> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer roleId;

    /**
     * 二维表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer tableId;

    /**
     * 数据范围
     */
    private int dataScope;

    /**
     * 表格定义
     */
    private int tableDefinition;

    /**
     * 数据编辑
     */
    private int dataEdition;
}
